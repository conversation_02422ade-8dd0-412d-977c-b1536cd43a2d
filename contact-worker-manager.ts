// contact-worker-manager.ts - Contact Worker管理器

import { ContactInfo } from './types';

interface WorkerMessage {
  type: string;
  data: any;
  id?: string;
}

interface WorkerResponse {
  type: string;
  data: any;
  id?: string;
  success: boolean;
}

interface SearchFilters {
  organization?: string;
  department?: string;
  role?: string;
  query?: string;
}

export class ContactWorkerManager {
  private worker: Worker | null = null;
  private messageId = 0;
  private pendingMessages: Map<string, { resolve: Function; reject: Function }> = new Map();
  private isInitialized = false;

  constructor() {
    this.initWorker();
  }

  /**
   * 初始化Worker
   */
  private initWorker() {
    try {
      // 创建Worker实例
      const workerCode = this.getWorkerCode();
      const blob = new Blob([workerCode], { type: 'application/javascript' });
      this.worker = new Worker(URL.createObjectURL(blob));
      
      // 设置消息监听
      this.worker.onmessage = this.handleWorkerMessage.bind(this);
      this.worker.onerror = this.handleWorkerError.bind(this);
      
      this.isInitialized = true;
      console.log('Contact Worker 初始化成功');
    } catch (error) {
      console.error('Contact Worker 初始化失败:', error);
      this.isInitialized = false;
    }
  }

  /**
   * 获取Worker代码字符串
   */
  private getWorkerCode(): string {
    // 返回contact-worker.ts的编译后代码
    // 这里简化为直接嵌入代码，实际项目中应该从编译后的文件加载
    return `
    // Worker代码开始
    const contacts = [];
    const organizations = {};
    const groupedContacts = {};
    const searchCache = new Map();

    function clearCache() {
      searchCache.clear();
    }

    function extractOrganizations(contactsData) {
      const orgs = {};
      
      contactsData.forEach(contact => {
        const branch = contact.branch || '未分类';
        const department = contact.department || '未分类';
        
        if (!orgs[branch]) {
          orgs[branch] = new Set();
        }
        orgs[branch].add(department);
      });

      const result = {};
      Object.keys(orgs).forEach(branch => {
        result[branch] = Array.from(orgs[branch]).sort();
      });

      if (Object.keys(result).length === 0) {
        result["总行"] = ["办公室", "人力资源部", "财务部", "信息技术部", "风险管理部"];
      }
      
      return result;
    }

    function groupContacts(contactsData) {
      const grouped = {};
      
      contactsData.forEach(contact => {
        const branch = contact.branch || '未分类';
        const department = contact.department || '未分类';
        
        if (!grouped[branch]) {
          grouped[branch] = {};
        }
        
        if (!grouped[branch][department]) {
          grouped[branch][department] = [];
        }
        
        grouped[branch][department].push(contact);
      });
      
      // 排序
      Object.keys(grouped).forEach(branch => {
        Object.keys(grouped[branch]).forEach(department => {
          grouped[branch][department].sort((a, b) => {
            const nameA = (a.name || '').toLowerCase();
            const nameB = (b.name || '').toLowerCase();
            return nameA.localeCompare(nameB);
          });
        });
      });
      
      return grouped;
    }

    function searchMatchesContact(contact, query) {
      if (!query.trim()) return true;
      
      const normalizedQuery = query.toLowerCase().trim();
      const isPhoneQuery = /^6\\d*$/.test(normalizedQuery);
      
      if (isPhoneQuery) {
        return contact.phone && contact.phone.includes(normalizedQuery);
      }
      
      const fieldsToSearch = ['name', 'phone', 'email', 'branch', 'department', 'title'];
      return fieldsToSearch.some(field => {
        const value = contact[field];
        return value && String(value).toLowerCase().includes(normalizedQuery);
      });
    }

    function applyFilters(contactsData, filters) {
      return contactsData.filter(contact => {
        if (filters.organization && filters.organization !== 'all') {
          if (contact.branch !== filters.organization) return false;
        }
        
        if (filters.department && filters.department !== 'all') {
          if (filters.department === '未分类') {
            if (contact.department && contact.department.trim()) return false;
          } else {
            if (contact.department !== filters.department) return false;
          }
        }
        
        if (filters.role && filters.role !== 'all') {
          if (contact.title !== filters.role) return false;
        }
        
        if (filters.query) {
          return searchMatchesContact(contact, filters.query);
        }
        
        return true;
      });
    }

    function search(filters, contactsData) {
      const cacheKey = JSON.stringify(filters);
      
      if (searchCache.has(cacheKey)) {
        return searchCache.get(cacheKey);
      }
      
      const results = applyFilters(contactsData, filters);
      
      if (searchCache.size > 100) {
        const firstKey = searchCache.keys().next().value;
        searchCache.delete(firstKey);
      }
      searchCache.set(cacheKey, results);
      
      return results;
    }

    function getStatistics(contactsData, groupedData) {
      const orgs = extractOrganizations(contactsData);
      const stats = {
        totalContacts: contactsData.length,
        totalBranches: Object.keys(orgs).length,
        totalDepartments: Object.values(orgs).reduce((sum, depts) => sum + depts.length, 0),
        branchStats: {}
      };

      Object.keys(groupedData).forEach(branch => {
        const departments = groupedData[branch];
        const contactCount = Object.values(departments).reduce((sum, contacts) => sum + contacts.length, 0);
        
        stats.branchStats[branch] = {
          departments: Object.keys(departments).length,
          contacts: contactCount
        };
      });

      return stats;
    }

    function getDepartmentOptions(contactsData, organizationFilter) {
      const departments = {};
      
      contactsData.forEach(contact => {
        if (organizationFilter && organizationFilter !== 'all' && contact.branch !== organizationFilter) {
          return;
        }
        
        const dept = contact.department || '未分类';
        departments[dept] = (departments[dept] || 0) + 1;
      });
      
      return Object.entries(departments)
        .map(([dept, count]) => ({ id: dept, label: dept, count }))
        .sort((a, b) => b.count - a.count);
    }

    function getRoleOptions(contactsData, organizationFilter, departmentFilter) {
      const roles = {};
      
      contactsData.forEach(contact => {
        if (organizationFilter && organizationFilter !== 'all' && contact.branch !== organizationFilter) {
          return;
        }
        
        if (departmentFilter && departmentFilter !== 'all') {
          if (departmentFilter === '未分类') {
            if (contact.department && contact.department.trim()) return;
          } else {
            if (contact.department !== departmentFilter) return;
          }
        }
        
        const role = contact.title || '未指定';
        roles[role] = (roles[role] || 0) + 1;
      });
      
      return Object.entries(roles)
        .map(([role, count]) => ({ id: role, label: role, count }))
        .sort((a, b) => b.count - a.count);
    }

    // 全局变量
    let currentContacts = [];
    let currentOrganizations = {};
    let currentGroupedContacts = {};

    // 消息处理
    self.onmessage = function(e) {
      const { type, data, id } = e.data;
      
      try {
        let result;
        
        switch (type) {
          case 'SET_CONTACTS':
            currentContacts = data;
            clearCache();
            currentOrganizations = extractOrganizations(currentContacts);
            currentGroupedContacts = groupContacts(currentContacts);
            result = { success: true };
            break;
            
          case 'GET_ORGANIZATIONS':
            result = currentOrganizations;
            break;
            
          case 'GET_GROUPED_CONTACTS':
            result = currentGroupedContacts;
            break;
            
          case 'SEARCH':
            result = search(data, currentContacts);
            break;
            
          case 'GET_STATISTICS':
            result = getStatistics(currentContacts, currentGroupedContacts);
            break;
            
          case 'GET_DEPARTMENT_OPTIONS':
            result = getDepartmentOptions(currentContacts, data.organizationFilter);
            break;
            
          case 'GET_ROLE_OPTIONS':
            result = getRoleOptions(currentContacts, data.organizationFilter, data.departmentFilter);
            break;
            
          default:
            throw new Error('Unknown message type: ' + type);
        }
        
        self.postMessage({
          type: type + '_RESPONSE',
          data: result,
          id: id,
          success: true
        });
        
      } catch (error) {
        self.postMessage({
          type: type + '_RESPONSE',
          data: { error: error.message },
          id: id,
          success: false
        });
      }
    };

    self.onerror = function(error) {
      console.error('Worker error:', error);
    };
    `;
  }

  /**
   * 处理Worker消息
   */
  private handleWorkerMessage(event: MessageEvent<WorkerResponse>) {
    const { type, data, id, success } = event.data;
    
    if (id && this.pendingMessages.has(id)) {
      const { resolve, reject } = this.pendingMessages.get(id)!;
      this.pendingMessages.delete(id);
      
      if (success) {
        resolve(data);
      } else {
        reject(new Error(data.error || 'Worker operation failed'));
      }
    }
  }

  /**
   * 处理Worker错误
   */
  private handleWorkerError(error: ErrorEvent) {
    console.error('Worker error:', error);
    // 拒绝所有待处理的消息
    this.pendingMessages.forEach(({ reject }) => {
      reject(new Error('Worker error: ' + error.message));
    });
    this.pendingMessages.clear();
  }

  /**
   * 发送消息到Worker
   */
  private sendMessage(type: string, data?: any): Promise<any> {
    if (!this.isInitialized || !this.worker) {
      return Promise.reject(new Error('Worker not initialized'));
    }

    return new Promise((resolve, reject) => {
      const id = `msg_${++this.messageId}`;
      
      this.pendingMessages.set(id, { resolve, reject });
      
      // 设置超时
      setTimeout(() => {
        if (this.pendingMessages.has(id)) {
          this.pendingMessages.delete(id);
          reject(new Error('Worker message timeout'));
        }
      }, 10000); // 10秒超时
      
      this.worker!.postMessage({ type, data, id });
    });
  }

  /**
   * 设置联系人数据
   */
  async setContacts(contacts: ContactInfo[]): Promise<void> {
    await this.sendMessage('SET_CONTACTS', contacts);
  }

  /**
   * 获取组织结构
   */
  async getOrganizations(): Promise<Record<string, string[]>> {
    return await this.sendMessage('GET_ORGANIZATIONS');
  }

  /**
   * 获取分组数据
   */
  async getGroupedContacts(): Promise<any> {
    return await this.sendMessage('GET_GROUPED_CONTACTS');
  }

  /**
   * 执行搜索
   */
  async search(filters: SearchFilters): Promise<ContactInfo[]> {
    return await this.sendMessage('SEARCH', filters);
  }

  /**
   * 获取统计信息
   */
  async getStatistics(): Promise<any> {
    return await this.sendMessage('GET_STATISTICS');
  }

  /**
   * 获取部门选项
   */
  async getDepartmentOptions(organizationFilter?: string): Promise<Array<{id: string; label: string; count: number}>> {
    return await this.sendMessage('GET_DEPARTMENT_OPTIONS', { organizationFilter });
  }

  /**
   * 获取岗位选项
   */
  async getRoleOptions(organizationFilter?: string, departmentFilter?: string): Promise<Array<{id: string; label: string; count: number}>> {
    return await this.sendMessage('GET_ROLE_OPTIONS', { organizationFilter, departmentFilter });
  }

  /**
   * 检查Worker是否可用
   */
  isAvailable(): boolean {
    return this.isInitialized && this.worker !== null;
  }

  /**
   * 销毁Worker
   */
  destroy() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    this.pendingMessages.clear();
    this.isInitialized = false;
  }
}

// 创建单例实例
export const contactWorkerManager = new ContactWorkerManager(); 