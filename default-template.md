---
name: "{{ name }}"
avatar: "{{ avatar }}"
branch: "{{ branch }}"
department: "{{ department }}"
title: "{{ title }}"
phone: "{{ phone }}"
email: "{{ email }}"
mobile: "{{ mobile }}"
room: "{{ room }}"
car: "{{ car }}"
address: "{{ address }}"
tags: "{{ tags }}"
cssclasses:
- Contact_Page
---

````tabs
tab: Info
```dataviewjs
const p = dv.page("");
if (p.avatar) {
  dv.paragraph(`!${p.avatar}`);
}
dv.paragraph(p.name ?? "");
dv.paragraph(`"${p.phone ?? ''}"`);
dv.paragraph(p.mobile ?? "");
dv.paragraph(`\`${p.email ?? ''}\``);
dv.paragraph(`${p.branch ?? ''}, ${p.department ?? ''}`);
dv.paragraph(p.title ?? "");
dv.paragraph(p.room ?? "");
tab: Trait
```chat-transcript
> me
[me=grey]
{header=h6, mw=90}
xxx
(TimeStamper) me:text 
(TimeStamper) ${dv.current().name ?? '联系人'}:text 
xxx
(TimeStamper) me:text 
(TimeStamper) ${dv.current().name ?? '联系人'}:text 
```
tab: More
用户名                    |  备注                      
------------------|------------------
username              |   请输入备注            
密码                        |   链接                
password               |   请输入链接

---

用户名                    |  备注                      
------------------|------------------
username              |   请输入备注            
密码                        |   链接                
password               |   请输入链接
````