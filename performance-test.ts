// performance-test.ts - 性能测试工具

import { ContactInfo } from './types';
import { contactWorkerManager } from './contact-worker-manager';

interface PerformanceMetrics {
  operation: string;
  duration: number;
  itemCount: number;
  memoryUsage?: number;
}

export class PerformanceTester {
  private metrics: PerformanceMetrics[] = [];

  /**
   * 生成测试用的联系人数据
   */
  generateTestContacts(count: number): ContactInfo[] {
    const contacts: ContactInfo[] = [];
    const branches = ['总行', '分行A', '分行B', '分行C', '分行D'];
    const departments = ['办公室', '人力资源部', '财务部', '信息技术部', '风险管理部', '业务部'];
    const titles = ['主管', '经理', '副经理', '专员', '助理'];

    for (let i = 0; i < count; i++) {
      contacts.push({
        name: `测试联系人${i + 1}`,
        phone: `1380000${String(i).padStart(4, '0')}`,
        email: `test${i + 1}@example.com`,
        branch: branches[i % branches.length],
        department: departments[i % departments.length],
        title: titles[i % titles.length],
        path: `contacts/test-${i + 1}.md`,
        content: `# 测试联系人${i + 1}\n\n这是测试数据`,
        avatar: i % 3 === 0 ? `avatars/test-${i + 1}.jpg` : undefined
      });
    }

    return contacts;
  }

  /**
   * 测试数据分组性能（传统方式）
   */
  async testTraditionalGrouping(contacts: ContactInfo[]): Promise<number> {
    const startTime = performance.now();
    
    const orgDeptMap: Record<string, Set<string>> = {};
    
    contacts.forEach(contact => {
      const org = contact.branch || '未知机构';
      const dept = contact.department || '未知部门';
      
      if (!orgDeptMap[org]) {
        orgDeptMap[org] = new Set();
      }
      orgDeptMap[org].add(dept);
    });
    
    // 转换为普通对象
    const result: Record<string, string[]> = {};
    Object.keys(orgDeptMap).forEach(org => {
      result[org] = Array.from(orgDeptMap[org]);
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.metrics.push({
      operation: 'Traditional Grouping',
      duration,
      itemCount: contacts.length,
      memoryUsage: this.getMemoryUsage()
    });
    
    return duration;
  }

  /**
   * 测试Worker数据分组性能
   */
  async testWorkerGrouping(contacts: ContactInfo[]): Promise<number> {
    const startTime = performance.now();
    
    // 设置联系人数据到Worker
    await contactWorkerManager.setContacts(contacts);
    
    // 获取组织结构
    await contactWorkerManager.getOrganizations();
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.metrics.push({
      operation: 'Worker Grouping',
      duration,
      itemCount: contacts.length,
      memoryUsage: this.getMemoryUsage()
    });
    
    return duration;
  }

  /**
   * 测试传统搜索性能
   */
  async testTraditionalSearch(contacts: ContactInfo[], queries: string[]): Promise<number> {
    const startTime = performance.now();
    
    for (const query of queries) {
      const normalizedQuery = query.toLowerCase().trim();
      const isPhoneQuery = /^6\d*$/.test(normalizedQuery);
      
      contacts.filter(contact => {
        if (isPhoneQuery) {
          return contact.phone && contact.phone.includes(normalizedQuery);
        }
        
        const fieldsToSearch = ['name', 'phone', 'email', 'branch', 'department', 'title'];
        return fieldsToSearch.some(field => {
          const value = contact[field];
          return value && String(value).toLowerCase().includes(normalizedQuery);
        });
      });
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.metrics.push({
      operation: 'Traditional Search',
      duration,
      itemCount: queries.length,
      memoryUsage: this.getMemoryUsage()
    });
    
    return duration;
  }

  /**
   * 测试Worker搜索性能
   */
  async testWorkerSearch(contacts: ContactInfo[], queries: string[]): Promise<number> {
    // 确保数据已设置
    await contactWorkerManager.setContacts(contacts);
    
    const startTime = performance.now();
    
    for (const query of queries) {
      await contactWorkerManager.search({ query });
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.metrics.push({
      operation: 'Worker Search',
      duration,
      itemCount: queries.length,
      memoryUsage: this.getMemoryUsage()
    });
    
    return duration;
  }

  /**
   * 测试DOM渲染性能（模拟）
   */
  async testDOMRendering(contacts: ContactInfo[]): Promise<number> {
    const startTime = performance.now();
    
    // 创建测试容器
    const container = document.createElement('div');
    container.style.cssText = 'position: absolute; top: -9999px; left: -9999px;';
    document.body.appendChild(container);
    
    try {
      // 模拟创建联系人卡片
      contacts.slice(0, Math.min(50, contacts.length)).forEach((contact, index) => {
        const card = document.createElement('div');
        card.className = 'contact-item';
        card.innerHTML = `
          <div class="contact-preview">
            <div class="contact-preview-avatar">
              ${contact.avatar 
                ? `<img src="${contact.avatar}" alt="${contact.name}" loading="lazy">` 
                : `<span>${contact.name?.charAt(0) || '?'}</span>`
              }
            </div>
            <div class="contact-preview-title">${contact.name || '未命名联系人'}</div>
            <div class="contact-preview-phone">${contact.phone || ''}</div>
            <div class="contact-preview-email">${contact.email || ''}</div>
            <div class="contact-preview-org">
              ${[contact.branch, contact.department].filter(Boolean).join(' - ')}
            </div>
          </div>
        `;
        container.appendChild(card);
      });
      
      // 强制布局
      container.offsetHeight;
      
    } finally {
      document.body.removeChild(container);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    this.metrics.push({
      operation: 'DOM Rendering',
      duration,
      itemCount: Math.min(50, contacts.length),
      memoryUsage: this.getMemoryUsage()
    });
    
    return duration;
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return 0;
  }

  /**
   * 运行完整的性能测试套件
   */
  async runFullTestSuite(contactCounts: number[] = [100, 500, 1000, 2000]): Promise<void> {
    console.log('🚀 开始性能测试...');
    
    for (const count of contactCounts) {
      console.log(`\n📊 测试 ${count} 个联系人的性能:`);
      
      const contacts = this.generateTestContacts(count);
      const queries = ['张', '138', '总行', '信息技术部', 'test'];
      
      try {
        // 测试数据分组
        const traditionalGroupingTime = await this.testTraditionalGrouping(contacts);
        const workerGroupingTime = await this.testWorkerGrouping(contacts);
        
        console.log(`  🔗 数据分组:`);
        console.log(`    传统方式: ${traditionalGroupingTime.toFixed(2)}ms`);
        console.log(`    Worker方式: ${workerGroupingTime.toFixed(2)}ms`);
        console.log(`    性能提升: ${((traditionalGroupingTime - workerGroupingTime) / traditionalGroupingTime * 100).toFixed(1)}%`);
        
        // 测试搜索
        const traditionalSearchTime = await this.testTraditionalSearch(contacts, queries);
        const workerSearchTime = await this.testWorkerSearch(contacts, queries);
        
        console.log(`  🔍 搜索性能:`);
        console.log(`    传统方式: ${traditionalSearchTime.toFixed(2)}ms`);
        console.log(`    Worker方式: ${workerSearchTime.toFixed(2)}ms`);
        console.log(`    性能提升: ${((traditionalSearchTime - workerSearchTime) / traditionalSearchTime * 100).toFixed(1)}%`);
        
        // 测试DOM渲染
        const domRenderingTime = await this.testDOMRendering(contacts);
        console.log(`  🎨 DOM渲染: ${domRenderingTime.toFixed(2)}ms`);
        
      } catch (error) {
        console.error(`❌ 测试 ${count} 个联系人时出错:`, error);
      }
    }
    
    console.log('\n📈 性能测试完成！');
    this.printSummary();
  }

  /**
   * 打印测试总结
   */
  printSummary(): void {
    console.log('\n📋 测试总结:');
    console.table(this.metrics);
    
    // 计算平均性能提升
    const traditionalGrouping = this.metrics.filter(m => m.operation === 'Traditional Grouping');
    const workerGrouping = this.metrics.filter(m => m.operation === 'Worker Grouping');
    const traditionalSearch = this.metrics.filter(m => m.operation === 'Traditional Search');
    const workerSearch = this.metrics.filter(m => m.operation === 'Worker Search');
    
    if (traditionalGrouping.length > 0 && workerGrouping.length > 0) {
      const avgTraditionalGrouping = traditionalGrouping.reduce((sum, m) => sum + m.duration, 0) / traditionalGrouping.length;
      const avgWorkerGrouping = workerGrouping.reduce((sum, m) => sum + m.duration, 0) / workerGrouping.length;
      const groupingImprovement = ((avgTraditionalGrouping - avgWorkerGrouping) / avgTraditionalGrouping * 100);
      
      console.log(`\n🎯 数据分组平均性能提升: ${groupingImprovement.toFixed(1)}%`);
    }
    
    if (traditionalSearch.length > 0 && workerSearch.length > 0) {
      const avgTraditionalSearch = traditionalSearch.reduce((sum, m) => sum + m.duration, 0) / traditionalSearch.length;
      const avgWorkerSearch = workerSearch.reduce((sum, m) => sum + m.duration, 0) / workerSearch.length;
      const searchImprovement = ((avgTraditionalSearch - avgWorkerSearch) / avgTraditionalSearch * 100);
      
      console.log(`🎯 搜索平均性能提升: ${searchImprovement.toFixed(1)}%`);
    }
  }

  /**
   * 清除测试结果
   */
  clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * 获取测试结果
   */
  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }
}

// 创建全局实例
export const performanceTester = new PerformanceTester(); 