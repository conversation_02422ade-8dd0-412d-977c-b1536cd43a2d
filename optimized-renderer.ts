// optimized-renderer.ts - 优化的联系人渲染器

import { ContactInfo } from './types';

interface RenderItem {
  id: string;
  element: HTMLElement;
  contact?: ContactInfo;
  type: 'contact' | 'department-header' | 'branch-header' | 'new-contact';
  isVisible: boolean;
  top: number;
  height: number;
}

interface RenderOptions {
  containerHeight: number;
  itemHeight: number;
  bufferSize: number;
  scrollTop: number;
}

export class OptimizedContactRenderer {
  private container: HTMLElement;
  private viewport: HTMLElement;
  private items: RenderItem[] = [];
  private visibleItems: Set<string> = new Set();
  private itemPool: Map<string, HTMLElement[]> = new Map();
  private intersectionObserver: IntersectionObserver | null = null;
  
  // 渲染配置
  private readonly ITEM_HEIGHT = 200; // 联系人卡片高度
  private readonly HEADER_HEIGHT = 60; // 头部高度
  private readonly BUFFER_SIZE = 5; // 缓冲区大小
  
  // 性能优化
  private renderFrame: number | null = null;
  private lastScrollTop = 0;
  private isScrolling = false;
  private scrollTimeout: NodeJS.Timeout | null = null;

  constructor(container: HTMLElement) {
    this.container = container;
    this.setupVirtualScrolling();
    this.setupIntersectionObserver();
  }

  /**
   * 设置虚拟滚动容器
   */
  private setupVirtualScrolling() {
    // 创建视口容器
    this.viewport = this.container.createEl('div', {
      cls: 'contact-viewport',
      attr: {
        style: `
          height: 100%;
          overflow-y: auto;
          position: relative;
        `
      }
    });

    // 监听滚动事件
    this.viewport.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
  }

  /**
   * 设置交叉观察器
   */
  private setupIntersectionObserver() {
    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          const itemId = entry.target.getAttribute('data-item-id');
          if (itemId) {
            if (entry.isIntersecting) {
              this.visibleItems.add(itemId);
            } else {
              this.visibleItems.delete(itemId);
            }
          }
        });
      },
      {
        root: this.viewport,
        rootMargin: `${this.BUFFER_SIZE * this.ITEM_HEIGHT}px`,
        threshold: 0
      }
    );
  }

  /**
   * 处理滚动事件
   */
  private handleScroll(event: Event) {
    const target = event.target as HTMLElement;
    const scrollTop = target.scrollTop;
    
    // 防抖处理
    this.isScrolling = true;
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
    this.scrollTimeout = setTimeout(() => {
      this.isScrolling = false;
    }, 150);

    // 只在滚动距离足够大时才更新
    if (Math.abs(scrollTop - this.lastScrollTop) > 50) {
      this.lastScrollTop = scrollTop;
      this.updateVisibleItems(scrollTop);
    }
  }

  /**
   * 更新可见项目
   */
  private updateVisibleItems(scrollTop: number) {
    if (this.renderFrame) {
      cancelAnimationFrame(this.renderFrame);
    }

    this.renderFrame = requestAnimationFrame(() => {
      const containerHeight = this.viewport.clientHeight;
      const startIndex = Math.floor(scrollTop / this.ITEM_HEIGHT);
      const endIndex = Math.min(
        startIndex + Math.ceil(containerHeight / this.ITEM_HEIGHT) + this.BUFFER_SIZE,
        this.items.length
      );

      // 隐藏不在可视范围内的项目
      this.items.forEach((item, index) => {
        const shouldBeVisible = index >= startIndex - this.BUFFER_SIZE && index <= endIndex;
        
        if (shouldBeVisible && !item.isVisible) {
          this.showItem(item);
        } else if (!shouldBeVisible && item.isVisible) {
          this.hideItem(item);
        }
      });
    });
  }

  /**
   * 显示项目
   */
  private showItem(item: RenderItem) {
    if (item.element && !item.isVisible) {
      item.element.style.display = '';
      item.element.style.transform = `translateY(${item.top}px)`;
      item.isVisible = true;
      
      if (this.intersectionObserver) {
        this.intersectionObserver.observe(item.element);
      }
    }
  }

  /**
   * 隐藏项目
   */
  private hideItem(item: RenderItem) {
    if (item.element && item.isVisible) {
      item.element.style.display = 'none';
      item.isVisible = false;
      
      if (this.intersectionObserver) {
        this.intersectionObserver.unobserve(item.element);
      }
      
      // 回收到对象池
      this.recycleElement(item);
    }
  }

  /**
   * 回收元素到对象池
   */
  private recycleElement(item: RenderItem) {
    if (!this.itemPool.has(item.type)) {
      this.itemPool.set(item.type, []);
    }
    
    const pool = this.itemPool.get(item.type)!;
    if (pool.length < 20) { // 限制池大小
      pool.push(item.element);
    }
  }

  /**
   * 从对象池获取元素
   */
  private getElementFromPool(type: string): HTMLElement | null {
    const pool = this.itemPool.get(type);
    return pool && pool.length > 0 ? pool.pop()! : null;
  }

  /**
   * 创建联系人卡片
   */
  private createContactCard(contact: ContactInfo): HTMLElement {
    const pooledElement = this.getElementFromPool('contact');
    
    if (pooledElement) {
      this.updateContactCard(pooledElement, contact);
      return pooledElement;
    }

    // 创建新的联系人卡片
    const card = document.createElement('div');
    card.className = 'contact-item optimized-contact-item';
    card.setAttribute('data-contact-path', contact.path);
    card.style.cssText = `
      position: absolute;
      width: 100%;
      height: ${this.ITEM_HEIGHT}px;
      will-change: transform;
      contain: layout style paint;
    `;

    this.updateContactCard(card, contact);
    return card;
  }

  /**
   * 更新联系人卡片内容
   */
  private updateContactCard(element: HTMLElement, contact: ContactInfo) {
    // 使用innerHTML更新，这里可以进一步优化为增量更新
    element.innerHTML = `
      <div class="contact-preview">
        <div class="contact-preview-avatar">
          ${contact.avatar 
            ? `<img src="${contact.avatar}" alt="${contact.name}" loading="lazy">` 
            : `<span>${this.getFirstChar(contact.name || '')}</span>`
          }
        </div>
        <div class="contact-preview-title">${contact.name || '未命名联系人'}</div>
        <div class="contact-preview-phone">${contact.phone || ''}</div>
        <div class="contact-preview-email">${contact.email || ''}</div>
        <div class="contact-preview-org">
          ${[contact.branch, contact.department].filter(Boolean).join(' - ')}
        </div>
      </div>
    `;

    // 添加事件监听器
    this.addContactEventListeners(element, contact);
  }

  /**
   * 添加联系人事件监听器
   */
  private addContactEventListeners(element: HTMLElement, contact: ContactInfo) {
    // 移除旧的事件监听器
    const newElement = element.cloneNode(true) as HTMLElement;
    element.parentNode?.replaceChild(newElement, element);

    // 添加点击事件
    newElement.addEventListener('click', () => {
      this.openContact(contact);
    });

    // 添加悬停效果
    newElement.addEventListener('mouseenter', () => {
      newElement.style.transform += ' translateY(-5px)';
    });

    newElement.addEventListener('mouseleave', () => {
      newElement.style.transform = newElement.style.transform.replace(' translateY(-5px)', '');
    });
  }

  /**
   * 获取首字符
   */
  private getFirstChar(name: string): string {
    if (!name) return '?';
    // 如果是中文，返回第一个字符
    if (/[\u4e00-\u9fa5]/.test(name)) {
      return name.charAt(0);
    }
    // 如果是英文，返回第一个字母的大写
    return name.charAt(0).toUpperCase();
  }

  /**
   * 打开联系人
   */
  private openContact(contact: ContactInfo) {
    // 这里应该调用外部的打开联系人方法
    console.log('Opening contact:', contact.name);
    // 可以通过事件系统或回调函数来处理
  }

  /**
   * 创建部门头部
   */
  private createDepartmentHeader(departmentName: string, contactCount: number): HTMLElement {
    const header = document.createElement('div');
    header.className = 'department-title-container optimized-header';
    header.style.cssText = `
      position: absolute;
      width: 100%;
      height: ${this.HEADER_HEIGHT}px;
      will-change: transform;
      contain: layout style paint;
    `;

    header.innerHTML = `
      <div class="department-title">
        <div class="department-name">${departmentName}</div>
        <div class="department-stats">${contactCount} 位联系人</div>
      </div>
    `;

    return header;
  }

  /**
   * 渲染联系人列表（使用虚拟滚动）
   */
  async renderContacts(groupedContacts: any, searchQuery?: string) {
    // 清空现有内容
    this.viewport.empty();
    this.items = [];
    
    let currentTop = 0;
    let totalHeight = 0;

    // 处理分组数据
    for (const [branchName, departments] of Object.entries(groupedContacts)) {
      // 添加机构头部
      if (!searchQuery) {
        const branchHeader = this.createDepartmentHeader(branchName, 0);
        const branchItem: RenderItem = {
          id: `branch-${branchName}`,
          element: branchHeader,
          type: 'branch-header',
          isVisible: false,
          top: currentTop,
          height: this.HEADER_HEIGHT
        };
        
        this.items.push(branchItem);
        currentTop += this.HEADER_HEIGHT;
      }

      // 处理部门
      for (const [deptName, contacts] of Object.entries(departments as any)) {
        const contactList = contacts as ContactInfo[];
        
        if (contactList.length === 0) continue;

        // 添加部门头部
        if (!searchQuery) {
          const deptHeader = this.createDepartmentHeader(deptName, contactList.length);
          const deptItem: RenderItem = {
            id: `dept-${branchName}-${deptName}`,
            element: deptHeader,
            type: 'department-header',
            isVisible: false,
            top: currentTop,
            height: this.HEADER_HEIGHT
          };
          
          this.items.push(deptItem);
          currentTop += this.HEADER_HEIGHT;
        }

        // 添加联系人
        for (const contact of contactList) {
          const contactCard = this.createContactCard(contact);
          const contactItem: RenderItem = {
            id: `contact-${contact.path}`,
            element: contactCard,
            contact,
            type: 'contact',
            isVisible: false,
            top: currentTop,
            height: this.ITEM_HEIGHT
          };
          
          this.items.push(contactItem);
          currentTop += this.ITEM_HEIGHT;
        }
      }
    }

    totalHeight = currentTop;

    // 设置容器总高度
    const spacer = this.viewport.createEl('div', {
      attr: {
        style: `height: ${totalHeight}px; position: relative;`
      }
    });

    // 将所有元素添加到容器中
    this.items.forEach(item => {
      spacer.appendChild(item.element);
      item.element.style.display = 'none'; // 初始隐藏
    });

    // 初始化可见项目
    this.updateVisibleItems(0);
  }

  /**
   * 增量更新联系人
   */
  updateContact(contact: ContactInfo) {
    const item = this.items.find(item => 
      item.type === 'contact' && item.contact?.path === contact.path
    );

    if (item && item.element) {
      this.updateContactCard(item.element, contact);
      item.contact = contact;
    }
  }

  /**
   * 移除联系人
   */
  removeContact(contactPath: string) {
    const index = this.items.findIndex(item => 
      item.type === 'contact' && item.contact?.path === contactPath
    );

    if (index !== -1) {
      const item = this.items[index];
      
      // 从DOM中移除
      if (item.element.parentNode) {
        item.element.parentNode.removeChild(item.element);
      }

      // 从数组中移除
      this.items.splice(index, 1);

      // 重新计算位置
      this.recalculatePositions(index);
    }
  }

  /**
   * 重新计算位置
   */
  private recalculatePositions(fromIndex: number) {
    let currentTop = fromIndex > 0 ? this.items[fromIndex - 1].top + this.items[fromIndex - 1].height : 0;

    for (let i = fromIndex; i < this.items.length; i++) {
      this.items[i].top = currentTop;
      currentTop += this.items[i].height;
    }

    // 更新可见项目
    this.updateVisibleItems(this.lastScrollTop);
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats() {
    return {
      totalItems: this.items.length,
      visibleItems: this.visibleItems.size,
      pooledElements: Array.from(this.itemPool.values()).reduce((sum, pool) => sum + pool.length, 0),
      isScrolling: this.isScrolling
    };
  }

  /**
   * 清理资源
   */
  destroy() {
    if (this.renderFrame) {
      cancelAnimationFrame(this.renderFrame);
    }

    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }

    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }

    this.items = [];
    this.visibleItems.clear();
    this.itemPool.clear();
  }
} 