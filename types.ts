import { HotkeySetting } from 'obsidian';

// 搜索标签接口
export interface SearchTag {
  id: string;
  label: string;
  level: number;
  type: 'organization' | 'department' | 'role';
  isDefault?: boolean;
}

// 折叠状态接口
export interface CollapseState {
  [key: string]: boolean; // key为机构或部门的唯一标识，value为是否折叠
}

// 搜索状态接口
export interface SearchState {
  selectedActions: SearchTag[];
  searchQuery: string;
  lastUsed: number;
  // 新增界面状态
  collapseStates: CollapseState;
  scrollPosition: number;
  selectedOrganization: string;
  selectedDepartment: string;
  selectedRole: string;
}

// YAML字段显示配置接口
export interface YamlFieldConfig {
  key: string;
  label: string;
  enabled: boolean;
  order: number;
  isInline?: boolean; // 是否与其他字段在同一行显示
  isRequired?: boolean; // 是否为必需字段，必需字段始终显示且不可关闭
}

// 模板字段设置接口
export interface TemplateFieldSettings {
  fields: YamlFieldConfig[];
  templatePath: string;
  lastModified: number;
}

export interface ContactPluginSettings {
  contactFolderPath: string;
  contactTemplatePath: string;
  excludeFolders: string[]; // 排除的文件夹路径列表
  openContactsHotkey: HotkeySetting | null;
  recentSearches: RecentSearchItem[];
  contactAvatars: Record<string, string>;
  lastSearchState: SearchState | null;
  templateFieldSettings: TemplateFieldSettings | null;
}

export interface RecentSearchItem {
  id: string;
  name: string;
  phone: string;
  timestamp: number;
  isDraft?: boolean;
  draftData?: {
    name: string;
    organization: string;
    department: string;
    position: string;
    mobile: string;
    phone: string;
    email: string;
    address: string;
    tags: string;
    draftDate: string;
  };
}

export interface ContactInfo {
  name: string;
  path: string;
  content: string;
  phone?: string;
  mobile?: string;
  email?: string;
  branch?: string;
  department?: string;
  title?: string;
  room?: string;
  avatar?: string;
  isDraft?: boolean;
  draftData?: any;
  draftDate?: string;
  isTemporary?: boolean; // 标记是否为临时联系人对象（基于最近搜索创建）
  [key: string]: any;
}

export const DEFAULT_SETTINGS: ContactPluginSettings = {
  contactFolderPath: "Contacts",
  contactTemplatePath: "src/default-template.md",
  excludeFolders: [],
  openContactsHotkey: null,
  recentSearches: [],
  contactAvatars: {},
  lastSearchState: null,
  templateFieldSettings: null
}; 