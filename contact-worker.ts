// contact-worker.ts - Contact数据处理Web Worker

interface ContactInfo {
  name?: string;
  phone?: string;
  email?: string;
  branch?: string;
  department?: string;
  title?: string;
  avatar?: string;
  path: string;
  content?: string;
  [key: string]: any;
}

interface WorkerMessage {
  type: string;
  data: any;
  id?: string;
}

interface GroupedContacts {
  [branch: string]: {
    [department: string]: ContactInfo[];
  };
}

interface SearchFilters {
  organization?: string;
  department?: string;
  role?: string;
  query?: string;
}

class ContactDataProcessor {
  private contacts: ContactInfo[] = [];
  private organizations: Record<string, string[]> = {};
  private groupedContacts: GroupedContacts = {};
  private searchCache: Map<string, ContactInfo[]> = new Map();

  /**
   * 设置联系人数据
   */
  setContacts(contacts: ContactInfo[]) {
    this.contacts = contacts;
    this.clearCache();
    this.extractOrganizations();
    this.groupContacts();
  }

  /**
   * 清理缓存
   */
  private clearCache() {
    this.searchCache.clear();
  }

  /**
   * 提取组织结构
   */
  private extractOrganizations() {
    const orgs: Record<string, Set<string>> = {};
    
    this.contacts.forEach(contact => {
      const branch = contact.branch || '未分类';
      const department = contact.department || '未分类';
      
      if (!orgs[branch]) {
        orgs[branch] = new Set();
      }
      orgs[branch].add(department);
    });

    // 转换为最终格式
    this.organizations = {};
    Object.keys(orgs).forEach(branch => {
      this.organizations[branch] = Array.from(orgs[branch]).sort();
    });

    // 如果没有数据，添加默认值
    if (Object.keys(this.organizations).length === 0) {
      this.organizations["总行"] = ["办公室", "人力资源部", "财务部", "信息技术部", "风险管理部"];
    }
  }

  /**
   * 按部门分组联系人
   */
  private groupContacts() {
    this.groupedContacts = {};
    
    this.contacts.forEach(contact => {
      const branch = contact.branch || '未分类';
      const department = contact.department || '未分类';
      
      if (!this.groupedContacts[branch]) {
        this.groupedContacts[branch] = {};
      }
      
      if (!this.groupedContacts[branch][department]) {
        this.groupedContacts[branch][department] = [];
      }
      
      this.groupedContacts[branch][department].push(contact);
    });
    
    // 对每个部门的联系人进行排序
    Object.keys(this.groupedContacts).forEach(branch => {
      Object.keys(this.groupedContacts[branch]).forEach(department => {
        this.groupedContacts[branch][department].sort((a, b) => {
          const nameA = (a.name || '').toLowerCase();
          const nameB = (b.name || '').toLowerCase();
          return nameA.localeCompare(nameB);
        });
      });
    });
  }

  /**
   * 搜索匹配检查
   */
  private searchMatchesContact(contact: ContactInfo, query: string): boolean {
    if (!query.trim()) return true;
    
    const normalizedQuery = query.toLowerCase().trim();
    const isPhoneQuery = /^6\d*$/.test(normalizedQuery);
    
    if (isPhoneQuery) {
      return contact.phone && contact.phone.includes(normalizedQuery);
    }
    
    // 检查各个字段
    const fieldsToSearch = ['name', 'phone', 'email', 'branch', 'department', 'title'];
    return fieldsToSearch.some(field => {
      const value = contact[field];
      return value && String(value).toLowerCase().includes(normalizedQuery);
    });
  }

  /**
   * 应用过滤器
   */
  private applyFilters(contacts: ContactInfo[], filters: SearchFilters): ContactInfo[] {
    return contacts.filter(contact => {
      // 组织过滤
      if (filters.organization && filters.organization !== 'all') {
        if (contact.branch !== filters.organization) return false;
      }
      
      // 部门过滤
      if (filters.department && filters.department !== 'all') {
        if (filters.department === '未分类') {
          if (contact.department && contact.department.trim()) return false;
        } else {
          if (contact.department !== filters.department) return false;
        }
      }
      
      // 岗位过滤
      if (filters.role && filters.role !== 'all') {
        if (contact.title !== filters.role) return false;
      }
      
      // 搜索查询过滤
      if (filters.query) {
        return this.searchMatchesContact(contact, filters.query);
      }
      
      return true;
    });
  }

  /**
   * 执行搜索
   */
  search(filters: SearchFilters): ContactInfo[] {
    const cacheKey = JSON.stringify(filters);
    
    // 检查缓存
    if (this.searchCache.has(cacheKey)) {
      return this.searchCache.get(cacheKey)!;
    }
    
    const results = this.applyFilters(this.contacts, filters);
    
    // 缓存结果（限制缓存大小）
    if (this.searchCache.size > 100) {
      const firstKey = this.searchCache.keys().next().value;
      this.searchCache.delete(firstKey);
    }
    this.searchCache.set(cacheKey, results);
    
    return results;
  }

  /**
   * 获取组织结构
   */
  getOrganizations(): Record<string, string[]> {
    return this.organizations;
  }

  /**
   * 获取分组数据
   */
  getGroupedContacts(): GroupedContacts {
    return this.groupedContacts;
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    const stats = {
      totalContacts: this.contacts.length,
      totalBranches: Object.keys(this.organizations).length,
      totalDepartments: Object.values(this.organizations).reduce((sum, depts) => sum + depts.length, 0),
      branchStats: {} as Record<string, { departments: number; contacts: number }>
    };

    Object.keys(this.groupedContacts).forEach(branch => {
      const departments = this.groupedContacts[branch];
      const contactCount = Object.values(departments).reduce((sum, contacts) => sum + contacts.length, 0);
      
      stats.branchStats[branch] = {
        departments: Object.keys(departments).length,
        contacts: contactCount
      };
    });

    return stats;
  }

  /**
   * 获取部门选项
   */
  getDepartmentOptions(organizationFilter?: string): Array<{id: string; label: string; count: number}> {
    const departments: Record<string, number> = {};
    
    // 收集部门和计数
    this.contacts.forEach(contact => {
      if (organizationFilter && organizationFilter !== 'all' && contact.branch !== organizationFilter) {
        return;
      }
      
      const dept = contact.department || '未分类';
      departments[dept] = (departments[dept] || 0) + 1;
    });
    
    return Object.entries(departments)
      .map(([dept, count]) => ({ id: dept, label: dept, count }))
      .sort((a, b) => b.count - a.count); // 按联系人数量排序
  }

  /**
   * 获取岗位选项
   */
  getRoleOptions(organizationFilter?: string, departmentFilter?: string): Array<{id: string; label: string; count: number}> {
    const roles: Record<string, number> = {};
    
    this.contacts.forEach(contact => {
      if (organizationFilter && organizationFilter !== 'all' && contact.branch !== organizationFilter) {
        return;
      }
      
      if (departmentFilter && departmentFilter !== 'all') {
        if (departmentFilter === '未分类') {
          if (contact.department && contact.department.trim()) return;
        } else {
          if (contact.department !== departmentFilter) return;
        }
      }
      
      const role = contact.title || '未指定';
      roles[role] = (roles[role] || 0) + 1;
    });
    
    return Object.entries(roles)
      .map(([role, count]) => ({ id: role, label: role, count }))
      .sort((a, b) => b.count - a.count);
  }
}

// Worker实例
const processor = new ContactDataProcessor();

// 消息处理
self.onmessage = function(e: MessageEvent<WorkerMessage>) {
  const { type, data, id } = e.data;
  
  try {
    let result: any;
    
    switch (type) {
      case 'SET_CONTACTS':
        processor.setContacts(data);
        result = { success: true };
        break;
        
      case 'GET_ORGANIZATIONS':
        result = processor.getOrganizations();
        break;
        
      case 'GET_GROUPED_CONTACTS':
        result = processor.getGroupedContacts();
        break;
        
      case 'SEARCH':
        result = processor.search(data);
        break;
        
      case 'GET_STATISTICS':
        result = processor.getStatistics();
        break;
        
      case 'GET_DEPARTMENT_OPTIONS':
        result = processor.getDepartmentOptions(data.organizationFilter);
        break;
        
      case 'GET_ROLE_OPTIONS':
        result = processor.getRoleOptions(data.organizationFilter, data.departmentFilter);
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
    
    // 发送成功响应
    self.postMessage({
      type: `${type}_RESPONSE`,
      data: result,
      id,
      success: true
    });
    
  } catch (error) {
    // 发送错误响应
    self.postMessage({
      type: `${type}_RESPONSE`,
      data: { error: error.message },
      id,
      success: false
    });
  }
};

// 错误处理
self.onerror = function(error) {
  console.error('Worker error:', error);
};

export {}; // 确保这是一个模块 