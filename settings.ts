import { App, PluginSettingTab, Setting, Notice, TFolder, TFile } from 'obsidian';
import ContactPlugin from './main';
import { TemplateFileSelector } from './components/template-file-selector';
import { ContactModal } from './modal';
import { YamlFieldConfig, TemplateFieldSettings } from './types';

export class ContactSettingTab extends PluginSettingTab {
  plugin: ContactPlugin;

  constructor(app: App, plugin: ContactPlugin) {
    super(app, plugin);
    this.plugin = plugin;
  }

  display(): void {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h2", { text: "ContactNotes 设置" });
    
    // 联系人文件夹路径设置
    new Setting(containerEl)
      .setName("联系人文件夹路径")
      .setDesc("设置存放联系人笔记的文件夹路径")
      .addText(text => text
        .setPlaceholder("例如: Contacts")
        .setValue(this.plugin.settings.contactFolderPath)
        .onChange(async (value) => {
          this.plugin.settings.contactFolderPath = value;
          await this.plugin.saveSettings();
          // 更改路径后刷新缓存
          await this.plugin.refreshContactCache();
        }));
    

    
    // 联系人模板路径设置
    new Setting(containerEl)
      .setName("联系人模板路径")
      .setDesc("设置新建联系人时使用的模板文件路径（留空则使用默认模板 default-template.md）")
      .addText(text => text
        .setPlaceholder("例如: Templates/Contact Template.md")
        .setValue(this.plugin.settings.contactTemplatePath)
        .onChange(async (value) => {
          this.plugin.settings.contactTemplatePath = value || "src/default-template.md";
          await this.plugin.saveSettings();
          // 模板路径改变时，重新载入YAML字段配置
          await this.loadTemplateFieldSettings();
          this.display(); // 刷新设置界面
        }));
    
    // 排除文件夹设置
    new Setting(containerEl)
      .setName("排除文件夹")
      .setDesc("设置要排除的文件夹路径，多个路径用逗号分隔（例如: Templates,Archive）")
      .addText(text => text
        .setPlaceholder("例如: Templates,Archive,Drafts")
        .setValue(this.plugin.settings.excludeFolders.join(','))
        .onChange(async (value) => {
          // 将输入的字符串按逗号分割，并去除空白
          this.plugin.settings.excludeFolders = value
            .split(',')
            .map(path => path.trim())
            .filter(path => path.length > 0);
          await this.plugin.saveSettings();
          // 更改排除路径后刷新缓存
          await this.plugin.refreshContactCache();
        }));

    
    // 添加YAML字段配置区域
    this.createYamlFieldSettingsSection(containerEl);
    
    // 快捷键设置
    new Setting(containerEl)
      .setName("打开 ContactNotes 快捷键")
      .setDesc("设置打开 ContactNotes 的快捷键")
      .addHotkey(hotkey => {
        hotkey.setPlaceholder("未设置")
          .setValue(this.plugin.settings.openContactsHotkey)
          .onChange(async (value) => {
            this.plugin.settings.openContactsHotkey = value;
            await this.plugin.saveSettings();
            
            // 更新命令的快捷键
            const commandId = this.plugin.manifest.id + ":open-contact-manager";
            if (this.app.commands.commands[commandId]) {
              this.app.commands.commands[commandId].hotkeys = 
                value ? [value] : [];
            }
          });
      });
    
    // 测试联系人文件夹按钮
    new Setting(containerEl)
      .setName("测试联系人文件夹")
      .setDesc("检查联系人文件夹是否存在并包含笔记")
      .addButton(button => button
        .setButtonText("测试")
        .onClick(async () => {
          const files = await this.plugin.getContactFiles();
          if (files.length > 0) {
            new Notice(`成功! 找到 ${files.length} 个联系人笔记。`);
          } else {
            new Notice(`未找到联系人笔记。请检查文件夹路径是否正确。`);
          }
        }));
    
    // 打开 ContactNotes 按钮
    new Setting(containerEl)
      .setName("打开 ContactNotes")
      .setDesc("直接从设置中打开 ContactNotes")
      .addButton(button => button
        .setButtonText("打开")
        .onClick(() => {
          new ContactModal(this.app, this.plugin).open();
        }));
    
    // 搜索状态管理
    containerEl.createEl("h3", { text: "搜索状态管理" });
    
    // 显示当前保存的搜索状态
    const lastState = this.plugin.settings.lastSearchState;
    if (lastState) {
      const stateInfo = containerEl.createEl("div", { cls: "setting-item-description" });
      
      // 构建标签信息
      const tagInfo = lastState.selectedActions.map(action => 
        `${action.label}(${action.type})`
      ).join(', ');
      
      // 构建折叠状态信息
      const collapseInfo = lastState.collapseStates ? 
        Object.keys(lastState.collapseStates).length : 0;
      
      stateInfo.innerHTML = `
        <strong>当前保存的搜索状态：</strong><br>
        • 选中标签：${lastState.selectedActions.length} 个 ${tagInfo ? `(${tagInfo})` : ''}<br>
        • 搜索关键词：${lastState.searchQuery || '无'}<br>
        • 筛选条件：机构=${lastState.selectedOrganization || '无'}, 部门=${lastState.selectedDepartment || '无'}, 岗位=${lastState.selectedRole || '无'}<br>
        • 折叠状态：${collapseInfo} 个项目<br>
        • 滚动位置：${lastState.scrollPosition || 0}px<br>
        • 最后使用：${new Date(lastState.lastUsed).toLocaleString()}
      `;
    } else {
      containerEl.createEl("div", { 
        cls: "setting-item-description",
        text: "当前没有保存的搜索状态"
      });
    }
    
    // 清除搜索状态按钮
    new Setting(containerEl)
      .setName("清除搜索状态")
      .setDesc("清除保存的搜索标签和关键词，下次打开将使用默认状态")
      .addButton(button => button
        .setButtonText("清除")
        .setWarning()
        .onClick(async () => {
          this.plugin.settings.lastSearchState = null;
          await this.plugin.saveSettings();
          new Notice("搜索状态已清除");
          this.display(); // 刷新设置界面
        }));
  }
  
  /**
   * 解析模板文件中的YAML字段
   */
  private async parseTemplateYamlFields(templatePath: string): Promise<YamlFieldConfig[]> {
    try {
      const templateFile = this.app.vault.getAbstractFileByPath(templatePath);
      if (!(templateFile instanceof TFile)) {
        // 如果文件不存在，直接返回预定义的字段
        return this.getDefaultTemplateFields();
      }
      
      const content = await this.app.vault.read(templateFile as TFile);
      const frontmatterMatch = content.match(/^---\s*\n([\s\S]*?)\n---\s*\n/);
      
      if (!frontmatterMatch) {
        return this.getDefaultTemplateFields();
      }
      
      const frontmatterContent = frontmatterMatch[1];
      const frontmatterLines = frontmatterContent.split('\n');
      const fields: YamlFieldConfig[] = [];
      let order = 0;
      let isInCssclassesArray = false;
      
      for (let i = 0; i < frontmatterLines.length; i++) {
        const line = frontmatterLines[i];
        const trimmedLine = line.trim();
        
        // 检查是否进入或退出cssclasses数组
        if (trimmedLine.startsWith('cssclasses:')) {
          isInCssclassesArray = true;
          continue;
        }
        
        // 如果在cssclasses数组中，跳过以-开头的行
        if (isInCssclassesArray) {
          if (trimmedLine.startsWith('-')) {
            continue;
          } else if (trimmedLine !== '' && !trimmedLine.startsWith(' ')) {
            // 遇到新的顶级字段，退出cssclasses数组
            isInCssclassesArray = false;
          } else {
            continue;
          }
        }
        
        // 处理顶级字段（不含缩进的key:value格式）
        const topLevelMatch = line.match(/^([^:\s]+):\s*(.*)$/);
        if (topLevelMatch) {
          const [_, key, value] = topLevelMatch;
          const cleanKey = key.trim();
          
          // 排除cssclasses字段
          if (cleanKey.toLowerCase() === 'cssclasses') {
            isInCssclassesArray = true;
            continue;
          }
          
          // 只处理非空的有效字段名
          if (cleanKey.trim() !== '') {
            // 定义必需的基础字段
            const requiredFields = ['name', 'avatar', 'phone', 'mobile', 'email'];
            const isRequired = requiredFields.includes(cleanKey.toLowerCase());
            
            fields.push({
              key: cleanKey,
              label: this.getFieldDisplayName(cleanKey),
              enabled: isRequired ? true : false, // 必需字段默认开启
              order: order++,
              isInline: this.isInlineField(cleanKey),
              isRequired: isRequired // 标记必需字段
            });
          }
        }
      }
      
      return fields.length > 0 ? fields : this.getDefaultTemplateFields();
    } catch (error) {
      console.error('解析模板YAML字段失败:', error);
      return this.getDefaultTemplateFields();
    }
  }
  
  /**
   * 获取默认模板字段配置
   */
  private getDefaultTemplateFields(): YamlFieldConfig[] {
    const defaultFields = [
      'name', 'avatar', 'branch', 'department', 'title', 
      'phone', 'email', 'mobile', 'room', 'car', 'address', 'tags'
    ];
    
    // 定义必需的基础字段
    const requiredFields = ['name', 'avatar', 'phone', 'mobile', 'email'];
    
    return defaultFields.map((field, index) => ({
      key: field,
      label: this.getFieldDisplayName(field),
      enabled: requiredFields.includes(field) ? true : false, // 必需字段默认开启
      order: index,
      isInline: this.isInlineField(field),
      isRequired: requiredFields.includes(field) // 标记必需字段
    }));
  }
  
  /**
   * 获取字段显示名称
   */
  private getFieldDisplayName(key: string): string {
    const displayNames: Record<string, string> = {
      'name': '姓名',
      'avatar': '头像',
      'phone': '电话',
      'mobile': '手机',
      'email': '邮箱',
      'branch': '机构',
      'department': '部门',
      'title': '职位',
      'position': '职位',
      'room': '办公室',
      'office': '办公室',
      'car': '车牌号',
      'created': '创建日期',
      'date': '日期',
      'tags': '标签',
      'organization': '机构',
      'company': '公司',
      'address': '地址',
      'location': '位置',
      'wechat': '微信',
      'qq': 'QQ',
      'website': '网站',
      'notes': '备注',
      'remark': '备注'
    };
    
    return displayNames[key.toLowerCase()] || key;
  }
  
  /**
   * 判断字段是否应该内联显示
   */
  private isInlineField(key: string): boolean {
    const inlineFields = ['branch', 'department', 'organization', 'company'];
    return inlineFields.includes(key.toLowerCase());
  }
  
  /**
   * 创建YAML字段设置区域
   */
  private createYamlFieldSettingsSection(containerEl: HTMLElement) {
    // 创建字段配置标题
    containerEl.createEl("h3", { text: "联系人卡片显示字段配置" });
    
    const descEl = containerEl.createEl("div", { cls: "setting-item-description" });
    descEl.innerHTML = `
      <p>配置在联系人卡片中显示的字段。可以通过开关控制字段是否显示，通过拖拽调整显示顺序。</p>
      <p><strong>基础字段：</strong>姓名、头像、座机、手机、邮箱为必需字段，始终显示且不可关闭。</p>
      <p><strong>注意：</strong>branch（机构）和department（部门）会显示在同一行。</p>
    `;
    
    // 创建字段列表容器
    const fieldsContainer = containerEl.createEl("div", { cls: "yaml-fields-container" });
    
    // 如果没有字段配置，尝试加载
    if (!this.plugin.settings.templateFieldSettings || 
        this.plugin.settings.templateFieldSettings.templatePath !== this.plugin.settings.contactTemplatePath) {
      this.loadTemplateFieldSettings().then(() => {
        this.renderYamlFieldsList(fieldsContainer);
      });
    } else {
      this.renderYamlFieldsList(fieldsContainer);
    }
    
    // 添加重新加载按钮
    new Setting(containerEl)
      .setName("重新加载模板字段")
      .setDesc("重新从默认模板文件中读取YAML字段配置")
      .addButton(button => button
        .setButtonText("重新加载")
        .onClick(async () => {
          await this.loadTemplateFieldSettings();
          this.display(); // 刷新整个设置界面
        }));
  }
  
  /**
   * 加载模板字段设置
   */
  private async loadTemplateFieldSettings() {
    const templatePath = this.plugin.settings.contactTemplatePath;
    if (!templatePath) {
      this.plugin.settings.templateFieldSettings = null;
      await this.plugin.saveSettings();
      return;
    }
    
    const fields = await this.parseTemplateYamlFields(templatePath);
    
    this.plugin.settings.templateFieldSettings = {
      fields: fields,
      templatePath: templatePath,
      lastModified: Date.now()
    };
    
    await this.plugin.saveSettings();
  }
  
  /**
   * 渲染YAML字段列表
   */
  private renderYamlFieldsList(container: HTMLElement) {
    container.empty();
    
    const fieldSettings = this.plugin.settings.templateFieldSettings;
    if (!fieldSettings || fieldSettings.fields.length === 0) {
      container.createEl("div", { 
        text: "未找到YAML字段，请检查模板文件格式",
        cls: "setting-item-description"
      });
      return;
    }
    
    // 分离必需字段和可编辑字段
    const requiredFields = fieldSettings.fields.filter(field => field.isRequired);
    const editableFields = fieldSettings.fields.filter(field => !field.isRequired);
    
    // 必需字段按固定顺序排序
    const sortedRequiredFields = requiredFields.sort((a, b) => {
      const requiredOrder = ['name', 'avatar', 'phone', 'mobile', 'email'];
      const aIndex = requiredOrder.indexOf(a.key.toLowerCase());
      const bIndex = requiredOrder.indexOf(b.key.toLowerCase());
      return aIndex - bIndex;
    });
    
    // 可编辑字段按order排序
    const sortedEditableFields = editableFields.sort((a, b) => a.order - b.order);
    
    // 先渲染必需字段区域
    if (sortedRequiredFields.length > 0) {
      const requiredSection = container.createEl("div", { cls: "yaml-fields-section" });
      requiredSection.createEl("h4", { 
        text: "必需字段（固定顺序）",
        cls: "yaml-section-title"
      });
      
      sortedRequiredFields.forEach((field) => {
        const fieldEl = requiredSection.createEl("div", { cls: "yaml-field-item yaml-field-required" });
        
        // 必需字段不显示拖拽手柄
        const lockIcon = fieldEl.createEl("div", { 
          cls: "yaml-field-lock-icon",
          attr: { "aria-label": "必需字段，不可排序" }
        });
        lockIcon.innerHTML = "🔒";
        
        // 创建字段信息
        const fieldInfo = fieldEl.createEl("div", { cls: "yaml-field-info" });
        fieldInfo.createEl("span", { 
          text: field.label,
          cls: "yaml-field-label"
        });
        fieldInfo.createEl("span", { 
          text: `(${field.key})`,
          cls: "yaml-field-key"
        });
        
        if (field.isInline) {
          fieldInfo.createEl("span", { 
            text: "内联显示",
            cls: "yaml-field-inline-tag"
          });
        }
        
        fieldInfo.createEl("span", { 
          text: "必需",
          cls: "yaml-field-required-tag"
        });
        
        // 创建开关（禁用状态）
        const toggleEl = fieldEl.createEl("div", { cls: "yaml-field-toggle" });
        const checkbox = toggleEl.createEl("input", { 
          type: "checkbox",
          cls: "yaml-field-checkbox"
        }) as HTMLInputElement;
        checkbox.checked = true; // 必需字段始终开启
        checkbox.disabled = true;
        
        // 不显示移动按钮
        fieldEl.createEl("div", { 
          cls: "yaml-field-no-move",
          text: "固定位置"
        });
      });
    }
    
    // 再渲染可编辑字段区域
    if (sortedEditableFields.length > 0) {
      const editableSection = container.createEl("div", { cls: "yaml-fields-section" });
      editableSection.createEl("h4", { 
        text: "可编辑字段（可排序）",
        cls: "yaml-section-title"
      });
      
      sortedEditableFields.forEach((field, index) => {
        const fieldEl = editableSection.createEl("div", { cls: "yaml-field-item yaml-field-editable" });
        
        // 创建拖拽手柄
        const dragHandle = fieldEl.createEl("div", { 
          cls: "yaml-field-drag-handle",
          attr: { "aria-label": "拖拽排序" }
        });
        dragHandle.innerHTML = "⋮⋮";
        
        // 创建字段信息
        const fieldInfo = fieldEl.createEl("div", { cls: "yaml-field-info" });
        fieldInfo.createEl("span", { 
          text: field.label,
          cls: "yaml-field-label"
        });
        fieldInfo.createEl("span", { 
          text: `(${field.key})`,
          cls: "yaml-field-key"
        });
        
        if (field.isInline) {
          fieldInfo.createEl("span", { 
            text: "内联显示",
            cls: "yaml-field-inline-tag"
          });
        }
        
        // 创建开关
        const toggleEl = fieldEl.createEl("div", { cls: "yaml-field-toggle" });
        const checkbox = toggleEl.createEl("input", { 
          type: "checkbox",
          cls: "yaml-field-checkbox"
        }) as HTMLInputElement;
        checkbox.checked = field.enabled;
        
        checkbox.addEventListener('change', async () => {
          field.enabled = checkbox.checked;
          await this.plugin.saveSettings();
        });
        
        // 创建上下移动按钮（只在可编辑字段中有效）
        const moveButtons = fieldEl.createEl("div", { cls: "yaml-field-move-buttons" });
        
        const upButton = moveButtons.createEl("button", { 
          text: "↑",
          cls: "yaml-field-move-button"
        });
        upButton.disabled = index === 0;
        upButton.addEventListener('click', () => this.moveEditableField(field.key, 'up'));
        
        const downButton = moveButtons.createEl("button", { 
          text: "↓",
          cls: "yaml-field-move-button"
        });
        downButton.disabled = index === sortedEditableFields.length - 1;
        downButton.addEventListener('click', () => this.moveEditableField(field.key, 'down'));
      });
    }
    
    // 添加样式
    this.addYamlFieldsStyle();
  }
  
  /**
   * 移动可编辑字段位置（只影响非必需字段）
   */
  private async moveEditableField(fieldKey: string, direction: 'up' | 'down') {
    const fieldSettings = this.plugin.settings.templateFieldSettings;
    if (!fieldSettings) return;
    
    // 只处理可编辑字段
    const editableFields = fieldSettings.fields.filter(field => !field.isRequired);
    const currentIndex = editableFields.findIndex(f => f.key === fieldKey);
    if (currentIndex === -1) return;
    
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (targetIndex < 0 || targetIndex >= editableFields.length) return;
    
    // 交换order值
    const currentField = editableFields[currentIndex];
    const targetField = editableFields[targetIndex];
    
    const currentOrder = currentField.order;
    const targetOrder = targetField.order;
    
    currentField.order = targetOrder;
    targetField.order = currentOrder;
    
    await this.plugin.saveSettings();
    this.display(); // 刷新设置界面
  }
  
  /**
   * 添加YAML字段样式
   */
  private addYamlFieldsStyle() {
    const styleId = 'yaml-fields-style';
    if (document.getElementById(styleId)) return;
    
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      .yaml-fields-container {
        border: 1px solid var(--background-modifier-border);
        border-radius: 6px;
        padding: 10px;
        margin: 10px 0;
      }
      
      .yaml-fields-section {
        margin-bottom: 20px;
      }
      
      .yaml-fields-section:last-child {
        margin-bottom: 0;
      }
      
      .yaml-section-title {
        margin: 0 0 10px 0;
        font-size: 14px;
        font-weight: 600;
        color: var(--text-normal);
        padding-bottom: 5px;
        border-bottom: 1px solid var(--background-modifier-border);
      }
      
      .yaml-field-item {
        display: flex;
        align-items: center;
        padding: 8px;
        margin: 4px 0;
        border: 1px solid var(--background-modifier-border-hover);
        border-radius: 4px;
        background: var(--background-secondary);
      }
      
      .yaml-field-required {
        background: var(--background-modifier-form-field);
        border-color: var(--background-modifier-border);
      }
      
      .yaml-field-editable {
        background: var(--background-secondary);
      }
      
      .yaml-field-drag-handle {
        cursor: grab;
        margin-right: 8px;
        color: var(--text-muted);
        font-size: 12px;
        user-select: none;
      }
      
      .yaml-field-lock-icon {
        margin-right: 8px;
        color: var(--text-muted);
        font-size: 14px;
        user-select: none;
      }
      
      .yaml-field-info {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .yaml-field-label {
        font-weight: 500;
      }
      
      .yaml-field-key {
        color: var(--text-muted);
        font-size: 0.9em;
      }
      
      .yaml-field-inline-tag {
        background: var(--color-accent);
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 0.8em;
      }
      
      .yaml-field-toggle {
        margin: 0 12px;
      }
      
      .yaml-field-checkbox {
        transform: scale(1.2);
      }
      
      .yaml-field-move-buttons {
        display: flex;
        gap: 4px;
      }
      
      .yaml-field-move-button {
        padding: 4px 8px;
        border: 1px solid var(--background-modifier-border);
        background: var(--background-primary);
        border-radius: 4px;
        cursor: pointer;
      }
      
      .yaml-field-move-button:hover:not(:disabled) {
        background: var(--background-modifier-hover);
      }
      
      .yaml-field-move-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      
      .yaml-field-required-tag {
        background: var(--color-red);
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 0.8em;
      }
      
      .yaml-field-checkbox:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
      
      .yaml-field-no-move {
        color: var(--text-muted);
        font-size: 0.8em;
        font-style: italic;
        padding: 4px 8px;
      }
    `;
    
    document.head.appendChild(style);
  }
} 