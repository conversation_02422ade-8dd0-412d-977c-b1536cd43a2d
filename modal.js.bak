import { __awaiter } from "tslib";
import { Modal, Notice, TFile, TFolder } from 'obsidian';
export class ContactModal extends Modal {
    constructor(app, plugin) {
        super(app);
        this.contactFiles = [];
        this.contactInfos = [];
        this.filteredContacts = [];
        this.organizations = {};
        this.selectedOrganization = '';
        this.selectedDepartment = '';
        this.selectedRole = '';
        // 定义优先显示的部门顺序
        this.priorityDepts = [
            '零售业务部',
            '零售管理部/消保护管理部',
            '渠道运营部/安全保卫部',
            '资产负债部/计划财务部',
            '法律合规部',
            '风险管理部',
            '资产负债部',
            '资产管理部'
        ];
        // 保存模态框打开前的body样式
        this.modalBodyStyle = '';
        this.plugin = plugin;
        // 初始化缓存数据
        this.initCacheData(plugin);
        // 从联系人信息中提取机构和部门数据
        this.organizations = this.extractOrganizationsAndDepartments();
    }
    // 初始化缓存数据
    initCacheData(plugin) {
        // 防止缓存为空导致错误
        if (!plugin.contactCache || !plugin.contactCache.files) {
            plugin.contactCache = {
                files: [],
                infos: [],
                lastUpdated: Date.now()
            };
        }
        this.contactFiles = plugin.contactCache.files || [];
        this.contactInfos = plugin.contactCache.infos || [];
        this.filteredContacts = [...this.contactInfos];
    }
    // 设置模态框样式
    setModalStyles() {
        this.modalEl.style.width = '1322px';
        this.modalEl.style.height = '80vh';
        this.modalEl.style.maxWidth = '1800px';
        this.modalEl.style.padding = '0';
        // 移除自定义定位，恢复默认位置
        this.modalEl.style.left = '';
        this.modalEl.style.transform = '';
    }
    onOpen() {
        return __awaiter(this, void 0, void 0, function* () {
            // 保存当前body样式
            this.modalBodyStyle = document.body.getAttribute('style') || '';
            const { contentEl } = this;
            contentEl.addClass('Contact_Page');
            // 设置弹窗样式
            this.setModalStyles();
            // 添加防止页面下移的样式
            this.fixContentShift();
            contentEl.createEl('h2', { text: '联系人管理' });
            // 创建搜索栏
            this.createSearchBar(contentEl);
            // 创建结果容器
            this.resultContainer = contentEl.createEl('div', { cls: 'contact-results' });
            // 设置默认初始状态 - 选中总行
            this.selectedOrganization = '总行';
            // 筛选出总行的联系人
            this.filteredContacts = this.contactInfos.filter(contact => contact.branch === '总行');
            // 渲染联系人
            this.renderContacts();
            // 自动聚焦到搜索框
            setTimeout(() => {
                if (this.searchInput) {
                    this.searchInput.focus();
                }
            }, 10);
        });
    }
    // 创建搜索栏 - 基本框架
    createSearchBar(container) {
        const searchContainer = container.createEl('div', { cls: 'contact-search-container' });
        // 创建机构选择下拉列表
        this.createOrgSelect(searchContainer);
        // 创建部门选择下拉列表
        this.createDeptSelect(searchContainer);
        // 创建角色选择下拉列表
        this.createRoleSelect(searchContainer);
        // 创建搜索框
        this.searchInput = searchContainer.createEl('input', {
            type: 'text',
            placeholder: '搜索联系人...',
            cls: 'contact-search-input'
        });
        // 设置搜索框的键盘事件监听
        this.searchInput.addEventListener('keydown', (e) => {
            e.stopPropagation(); // 阻止事件冒泡，确保快捷键不会被其他监听器捕获
        });
        // 搜索内容变化事件
        this.searchInput.addEventListener('input', () => {
            this.filterContacts();
        });
        // 创建重置按钮
        this.resetButton = searchContainer.createEl('button', {
            text: '重置',
            cls: 'reset-button'
        });
        // 重置按钮点击事件
        this.resetButton.addEventListener('click', () => {
            this.resetFilters();
        });
    }
    // 从联系人信息中提取机构和部门信息
    extractOrganizationsAndDepartments() {
        const orgs = {};
        // 遍历所有联系人信息
        this.contactInfos.forEach(contact => {
            const branch = contact.branch;
            const department = contact.department;
            if (branch && department) {
                // 如果机构不存在，创建一个新的部门数组
                if (!orgs[branch]) {
                    orgs[branch] = new Set();
                }
                // 添加部门到对应机构的集合中
                orgs[branch].add(department);
            }
        });
        // 将 Set 转换为数组并排序
        const result = {};
        Object.keys(orgs).forEach(branch => {
            result[branch] = Array.from(orgs[branch]).sort();
        });
        // 如果没有数据，添加默认值
        if (Object.keys(result).length === 0) {
            result["总行"] = ["办公室", "人力资源部", "财务部", "信息技术部", "风险管理部"];
        }
        return result;
    }
    // 创建机构选择下拉列表 - 基本框架
    createOrgSelect(container) {
        const orgSelectContainer = container.createEl('div', { cls: 'custom-select-container organization-select' });
        // 创建触发器 - 默认选中总行
        const trigger = orgSelectContainer.createEl('div', {
            cls: 'custom-select-trigger selected',
            text: '总行'
        });
        // 创建选项容器
        const optionsContainer = orgSelectContainer.createEl('div', { cls: 'custom-select-options' });
        this.orgSelectTrigger = trigger;
        this.orgSelectOptions = optionsContainer;
    }
    // 创建部门选择下拉列表 - 基本框架
    createDeptSelect(container) {
        const deptSelectContainer = container.createEl('div', { cls: 'custom-select-container department-select' });
        // 创建触发器
        const trigger = deptSelectContainer.createEl('div', {
            cls: 'custom-select-trigger unselected',
            text: '选择部门'
        });
        // 创建选项容器
        const optionsContainer = deptSelectContainer.createEl('div', { cls: 'custom-select-options' });
        this.deptSelectTrigger = trigger;
        this.deptSelectOptions = optionsContainer;
    }
    // 创建角色选择下拉列表 - 基本框架
    createRoleSelect(container) {
        const roleSelectContainer = container.createEl('div', { cls: 'custom-select-container role-select' });
        // 创建触发器
        const trigger = roleSelectContainer.createEl('div', {
            cls: 'custom-select-trigger unselected',
            text: '选择角色'
        });
        // 创建选项容器
        const optionsContainer = roleSelectContainer.createEl('div', { cls: 'custom-select-options' });
        this.roleSelectTrigger = trigger;
        this.roleSelectOptions = optionsContainer;
    }
    // 渲染联系人 - 基本框架
    renderContacts() {
        this.resultContainer.empty();
        // 获取搜索关键词
        const query = this.searchInput.value.trim();
        // 搜索结果为空时的提示
        if (this.filteredContacts.length === 0) {
            this.resultContainer.createEl("p", { text: "未找到匹配的联系人" });
            return;
        }
        // 创建总容器
        const contactsContainer = this.resultContainer.createEl("div", { cls: "contacts-container" });
        // 只有在未输入搜索内容时才显示最近搜索
        this.renderRecentSearches(contactsContainer, query);
        // 如果输入了搜索内容，不按部门分组，直接显示搜索结果
        if (query) {
            // 创建搜索结果列表
            contactsContainer.createEl("h3", {
                text: `搜索结果 (${this.filteredContacts.length})`,
                cls: "search-results-title"
            });
            const contactList = contactsContainer.createEl("div", { cls: "contact-list all-contacts" });
            this.filteredContacts.forEach(contact => {
                this.createContactCard(contactList, contact);
            });
            return;
        }
        // 按部门分组
        this.renderContactsByDepartment(contactsContainer);
    }
    // 提取常用的联系人筛选逻辑
    filterContacts() {
        const query = this.searchInput.value.toLowerCase().trim();
        // 检查是否是6开头的数字查询
        const isPhoneQuery = /^6\d*$/.test(query);
        // 应用筛选条件
        this.filteredContacts = this.contactInfos.filter((contact) => {
            // 应用所有筛选器
            return this.matchesOrgFilter(contact) &&
                this.matchesDeptFilter(contact) &&
                this.matchesRoleFilter(contact) &&
                this.matchesSearchQuery(contact, query, isPhoneQuery);
        });
        // 检查是否有查询内容且结果唯一
        if (query && this.filteredContacts.length === 1) {
            // 所有查询导致的唯一结果都加入最近搜索
            this.plugin.addRecentSearch(this.filteredContacts[0]);
        }
        this.renderContacts();
    }
    // 修复内容下移问题
    fixContentShift() {
        const styleEl = document.createElement('style');
        styleEl.id = 'contact-modal-fix';
        styleEl.innerHTML = `
      /* 只针对当前模态框的修复 */
      .modal-container .Contact_Page {
        overflow-y: auto !important;
      }
      
      /* 确保模态框内部滚动正常 */
      .Contact_Page .contact-results {
        overflow-y: auto !important;
      }
      
      /* 防止模态框影响全局样式 */
      .workspace-split.mod-root {
        position: relative !important;
      }
      
      /* 防止body样式被干扰 */
      body {
        position: static !important;
        transform: none !important;
      }
    `;
        document.head.appendChild(styleEl);
    }
    onClose() {
        const { contentEl } = this;
        contentEl.empty();
        // 移除修复样式
        const fixStyle = document.getElementById('contact-modal-fix');
        if (fixStyle) {
            fixStyle.remove();
        }
        // 恢复模态框打开前的body样式
        if (this.modalBodyStyle) {
            document.body.setAttribute('style', this.modalBodyStyle);
        }
        else {
            document.body.removeAttribute('style');
        }
        // 修复任何样式问题
        this.plugin.fixStyleIssues();
    }
    // 快速辅助方法
    // 检查是否匹配机构筛选
    matchesOrgFilter(contact) {
        return !this.selectedOrganization || contact.branch === this.selectedOrganization;
    }
    // 检查是否匹配部门筛选
    matchesDeptFilter(contact) {
        return !this.selectedDepartment || contact.department === this.selectedDepartment;
    }
    // 检查是否匹配角色筛选
    matchesRoleFilter(contact) {
        if (!this.selectedRole)
            return true;
        const isDepartmentHead = contact.title && contact.title.includes('部门负责人');
        const isSectionHead = contact.title && contact.title.includes('科室负责人');
        if (this.selectedRole === '部门负责人')
            return isDepartmentHead;
        if (this.selectedRole === '科室负责人')
            return isSectionHead;
        return true;
    }
    // 检查是否匹配搜索关键词
    matchesSearchQuery(contact, query, isPhoneQuery) {
        if (!query)
            return true;
        // 如果是6开头的数字，只搜索电话号码字段
        if (isPhoneQuery) {
            return contact.phone && contact.phone.includes(query);
        }
        // 普通搜索，排除隐藏字段
        return Object.entries(contact).some(([key, value]) => {
            // 跳过content和path字段
            if (key === 'content' || key === 'path')
                return false;
            return value && String(value).toLowerCase().includes(query);
        });
    }
    // 以下是界面渲染相关的基本方法框架
    // 按部门分组显示联系人
    renderContactsByDepartment(container) {
        // 部门分组的基本框架 - 详细实现在完整版中
    }
    // 渲染最近搜索记录
    renderRecentSearches(container, query) {
        // 最近搜索渲染基本框架 - 详细实现在完整版中
    }
    // 创建单个联系人卡片
    createContactCard(container, contact, isRecentSearch = false) {
        const contactEl = container.createEl("div", { cls: "contact-item" });
        contactEl.setAttribute('data-contact-name', contact.name || '');
        // 创建联系人预览容器
        const previewEl = contactEl.createEl("div", { cls: "contact-preview" });
        // 如果是最近搜索项，添加删除按钮
        if (isRecentSearch) {
            this.addDeleteButton(previewEl, contact);
        }
        // 添加联系人基本信息
        this.addContactAvatar(previewEl, contact);
        this.addContactName(previewEl, contact);
        this.addContactPhoneInfo(previewEl, contact);
        this.addContactDetails(previewEl, contact);
        // 点击联系人打开对应的笔记
        contactEl.addEventListener("click", () => this.openContactNote(contact));
        // 添加拖拽事件处理
        this.setupAvatarDragAndDrop(previewEl, contact);
    }
    // 添加删除按钮
    addDeleteButton(container, contact) {
        const deleteBtn = container.createEl("div", {
            cls: "contact-delete-btn",
            attr: { 'title': '从最近搜索中移除' }
        });
        // 添加垃圾桶SVG图标
        deleteBtn.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="trash-icon">
        <polyline points="3 6 5 6 21 6"></polyline>
        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
        <line x1="10" y1="11" x2="10" y2="17"></line>
        <line x1="14" y1="11" x2="14" y2="17"></line>
      </svg>
    `;
        // 阻止冒泡，避免点击删除按钮时打开联系人
        deleteBtn.addEventListener("click", (e) => {
            e.stopPropagation();
            e.preventDefault();
            // 从最近搜索中移除该联系人
            this.removeFromRecentSearches(contact.path);
            // 重新渲染联系人列表
            this.renderContacts();
        });
    }
    // 从最近搜索中移除联系人
    removeFromRecentSearches(contactPath) {
        const recentSearches = this.plugin.getRecentSearches();
        const index = recentSearches.findIndex((item) => item.id === contactPath);
        if (index !== -1) {
            recentSearches.splice(index, 1);
            this.plugin.saveSettings();
        }
    }
    // 添加联系人头像
    addContactAvatar(container, contact) {
        const avatarEl = container.createEl("div", { cls: "contact-preview-avatar" });
        // 如果有自定义头像，则尝试显示图片
        if (contact.avatar) {
            try {
                const img = avatarEl.createEl("img", {
                    attr: {
                        src: this.plugin.app.vault.adapter.getResourcePath(contact.avatar),
                        alt: contact.name || "Avatar"
                    }
                });
                // 图片加载失败时显示首字母
                img.onerror = () => {
                    this.replaceWithFirstChar(img, avatarEl, contact);
                    // 清除失效的头像路径
                    if (this.plugin.settings.contactAvatars[contact.path]) {
                        delete this.plugin.settings.contactAvatars[contact.path];
                        this.plugin.saveSettings();
                        // 更新联系人对象
                        contact.avatar = null;
                        // 找到并更新缓存中的联系人信息
                        const cacheContactIndex = this.contactInfos.findIndex(c => c.path === contact.path);
                        if (cacheContactIndex !== -1) {
                            this.contactInfos[cacheContactIndex].avatar = null;
                        }
                    }
                };
            }
            catch (e) {
                console.error("加载头像失败:", e);
                // 失败时显示首字母
                this.showFirstChar(avatarEl, contact);
            }
        }
        else {
            // 如果没有自定义头像，则显示首字母
            this.showFirstChar(avatarEl, contact);
        }
    }
    // 移除图片并显示首字母
    replaceWithFirstChar(imgElement, avatarEl, contact) {
        if (imgElement && imgElement.parentNode) {
            imgElement.remove();
        }
        this.showFirstChar(avatarEl, contact);
    }
    // 显示联系人姓氏首字母
    showFirstChar(avatarEl, contact) {
        avatarEl.empty();
        const firstName = this.getFirstName(contact.name || "");
        const firstChar = firstName ? firstName.charAt(0) : "?";
        avatarEl.createEl("span", { text: firstChar });
    }
    // 获取联系人的姓氏
    getFirstName(fullName) {
        if (!fullName)
            return "";
        // 处理中文姓名：通常姓在前，取第一个字
        if (/[\u4e00-\u9fa5]/.test(fullName)) {
            return fullName.charAt(0);
        }
        // 处理西方姓名：通常名在前姓在后，尝试分割并取姓氏首字母
        const nameParts = fullName.trim().split(/\s+/);
        if (nameParts.length > 1) {
            // 假设最后一部分是姓
            return nameParts[nameParts.length - 1];
        }
        // 如果无法判断，则返回第一个字符
        return fullName;
    }
    // 添加联系人姓名
    addContactName(container, contact) {
        container.createEl("div", {
            text: contact.name || "未命名联系人",
            cls: "contact-preview-title"
        });
    }
    // 添加联系人电话信息
    addContactPhoneInfo(container, contact) {
        // 添加电话
        if (contact.phone && contact.phone !== "-") {
            const isLightTheme = document.body.classList.contains('theme-light');
            const phoneIcon = isLightTheme ? '📞' : '☎️';
            container.createEl("div", {
                text: `${phoneIcon} "${contact.phone}"`,
                cls: "contact-preview-phone"
            });
        }
        // 添加手机
        if (contact.mobile) {
            container.createEl("div", {
                text: contact.mobile,
                cls: "contact-preview-mobile"
            });
        }
        // 添加邮箱
        if (contact.email) {
            container.createEl("div", {
                text: contact.email,
                cls: "contact-preview-email"
            });
        }
    }
    // 添加联系人详细信息
    addContactDetails(container, contact) {
        // 添加组织信息
        if (contact.branch || contact.department) {
            let orgText = "";
            if (contact.branch)
                orgText += contact.branch;
            if (contact.branch && contact.department)
                orgText += ", ";
            if (contact.department)
                orgText += contact.department;
            container.createEl("div", {
                text: orgText,
                cls: "contact-preview-org"
            });
        }
        // 添加职位信息
        if (contact.title) {
            container.createEl("div", {
                text: contact.title,
                cls: "contact-preview-subtitle"
            });
        }
        // 添加位置信息
        if (contact.room) {
            container.createEl("div", {
                text: contact.room,
                cls: "contact-preview-location"
            });
        }
    }
    // 打开联系人笔记
    openContactNote(contact) {
        // 如果有路径，就打开相应的笔记
        if (contact.path) {
            const file = this.plugin.app.vault.getAbstractFileByPath(contact.path);
            if (file && file instanceof TFile) {
                // 关闭当前模态框
                this.close();
                // 打开联系人笔记
                this.plugin.app.workspace.getLeaf().openFile(file);
            }
            else {
                new Notice(`找不到联系人文件: ${contact.path}`, 3000);
            }
        }
        else {
            new Notice("此联系人没有关联的笔记文件", 3000);
        }
    }
    // 以下是拖放头像功能的方法
    // 设置头像拖拽处理
    setupAvatarDragAndDrop(container, contact) {
        // 获取联系人头像元素
        const avatarEl = container.querySelector('.contact-preview-avatar');
        if (!avatarEl)
            return;
        // 添加拖拽相关事件
        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.stopPropagation();
            container.addClass('contact-avatar-dropzone');
        });
        container.addEventListener('dragleave', (e) => {
            e.preventDefault();
            e.stopPropagation();
            container.removeClass('contact-avatar-dropzone');
        });
        container.addEventListener('dragenter', (e) => {
            e.preventDefault();
            e.stopPropagation();
        });
        container.addEventListener('drop', (e) => __awaiter(this, void 0, void 0, function* () {
            var _a;
            e.preventDefault();
            e.stopPropagation();
            container.removeClass('contact-avatar-dropzone');
            // 阻止编辑器接收这个拖放事件
            e.stopImmediatePropagation();
            // 处理拖拽的文件
            const files = (_a = e.dataTransfer) === null || _a === void 0 ? void 0 : _a.files;
            if (files && files.length > 0) {
                yield this.handleAvatarDrop(files[0], contact);
            }
        }));
    }
    // 处理头像拖拽上传
    handleAvatarDrop(file, contact) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 检查是否是图片文件
                if (!file.type.startsWith('image/')) {
                    new Notice('只能上传图片文件作为头像', 3000);
                    return;
                }
                // 显示头像确认对话框
                const confirmResult = yield this.showAvatarConfirmDialog(contact.name);
                if (!confirmResult)
                    return;
                // 加载图片
                const reader = new FileReader();
                // 读取文件为 DataURL
                const loadImagePromise = new Promise((resolve, reject) => {
                    reader.onload = (e) => { var _a; return resolve((_a = e.target) === null || _a === void 0 ? void 0 : _a.result); };
                    reader.onerror = (e) => reject(e);
                    reader.readAsDataURL(file);
                });
                const dataUrl = yield loadImagePromise;
                // 调整图片尺寸
                const resizedImageData = yield this.resizeImage(dataUrl, 30, 30);
                // 保存图片到联系人文件夹/avatars目录
                const imagePath = yield this.saveAvatarImage(resizedImageData, contact);
                // 更新联系人头像路径
                this.plugin.settings.contactAvatars[contact.path] = imagePath;
                yield this.plugin.saveSettings();
                // 更新当前联系人的头像信息
                contact.avatar = imagePath;
                // 更新联系人缓存
                // 在contactInfos和filteredContacts中找到并更新联系人信息
                const cacheContactIndex = this.contactInfos.findIndex(c => c.path === contact.path);
                if (cacheContactIndex !== -1) {
                    this.contactInfos[cacheContactIndex].avatar = imagePath;
                }
                const filteredContactIndex = this.filteredContacts.findIndex(c => c.path === contact.path);
                if (filteredContactIndex !== -1) {
                    this.filteredContacts[filteredContactIndex].avatar = imagePath;
                }
                // 添加CSS规则
                this.addContactCssRule(contact.name, imagePath);
                // 刷新显示
                this.renderContacts();
                new Notice(`联系人"${contact.name}"头像已更新`, 2000);
            }
            catch (error) {
                console.error('处理头像上传失败:', error);
                new Notice('头像上传失败: ' + error.message, 3000);
            }
        });
    }
    // 显示头像确认对话框
    showAvatarConfirmDialog(contactName) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve) => {
                // 创建确认对话框
                const modalEl = document.createElement('div');
                modalEl.className = 'avatar-confirm-dialog';
                document.body.appendChild(modalEl);
                // 添加标题
                const titleEl = document.createElement('h3');
                titleEl.textContent = `确认将此图片用作"联系人 ${contactName} 的头像"？`;
                modalEl.appendChild(titleEl);
                // 添加按钮容器
                const buttonContainer = document.createElement('div');
                buttonContainer.className = 'avatar-confirm-buttons';
                modalEl.appendChild(buttonContainer);
                // 添加确认按钮
                const confirmButton = document.createElement('button');
                confirmButton.textContent = '确定';
                confirmButton.className = 'mod-cta';
                buttonContainer.appendChild(confirmButton);
                // 添加取消按钮
                const cancelButton = document.createElement('button');
                cancelButton.textContent = '取消';
                buttonContainer.appendChild(cancelButton);
                // 添加背景遮罩
                const overlay = document.createElement('div');
                overlay.className = 'avatar-confirm-overlay';
                document.body.appendChild(overlay);
                // 确认按钮点击事件
                confirmButton.addEventListener('click', () => {
                    document.body.removeChild(modalEl);
                    document.body.removeChild(overlay);
                    resolve(true);
                });
                // 取消按钮点击事件
                cancelButton.addEventListener('click', () => {
                    document.body.removeChild(modalEl);
                    document.body.removeChild(overlay);
                    resolve(false);
                });
                // 点击遮罩关闭
                overlay.addEventListener('click', () => {
                    document.body.removeChild(modalEl);
                    document.body.removeChild(overlay);
                    resolve(false);
                });
            });
        });
    }
    // 调整图片尺寸
    resizeImage(dataUrl, width, height) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve, reject) => {
                try {
                    const img = document.createElement('img');
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        canvas.width = width;
                        canvas.height = height;
                        const ctx = canvas.getContext('2d');
                        if (!ctx) {
                            reject(new Error('无法创建画布上下文'));
                            return;
                        }
                        ctx.drawImage(img, 0, 0, width, height);
                        // 转换为圆形头像
                        ctx.globalCompositeOperation = 'destination-in';
                        ctx.beginPath();
                        ctx.arc(width / 2, height / 2, width / 2, 0, Math.PI * 2);
                        ctx.closePath();
                        ctx.fill();
                        // 恢复默认操作模式
                        ctx.globalCompositeOperation = 'source-over';
                        // 返回调整后的图片数据
                        resolve(canvas.toDataURL('image/png'));
                    };
                    img.onerror = () => {
                        reject(new Error('图片加载失败'));
                    };
                    img.src = dataUrl;
                }
                catch (e) {
                    reject(e);
                }
            });
        });
    }
    // 保存头像图片
    saveAvatarImage(dataUrl, contact) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // 获取联系人文件夹
                const contactFolder = this.plugin.app.vault.getAbstractFileByPath(this.plugin.settings.contactFolderPath);
                if (!contactFolder || !(contactFolder instanceof TFolder)) {
                    throw new Error('找不到联系人文件夹');
                }
                // 检查avatars子目录是否存在，不存在则创建
                let avatarsDir;
                const avatarsDirPath = `${this.plugin.settings.contactFolderPath}/avatars`;
                avatarsDir = this.plugin.app.vault.getAbstractFileByPath(avatarsDirPath);
                if (!avatarsDir) {
                    avatarsDir = yield this.plugin.app.vault.createFolder(avatarsDirPath);
                }
                // 直接使用联系人姓名作为文件名，处理特殊字符
                let safeName = contact.name || "未命名联系人";
                // 移除不安全的文件名字符
                safeName = safeName.replace(/[\\/:*?"<>|]/g, "_");
                // 创建文件名
                const fileName = `${safeName}.png`;
                const filePath = `${avatarsDirPath}/${fileName}`;
                // 检查是否已存在同名文件，如果存在则删除
                const existingFile = this.plugin.app.vault.getAbstractFileByPath(filePath);
                if (existingFile && existingFile instanceof TFile) {
                    yield this.plugin.app.vault.delete(existingFile);
                }
                // 将base64数据转换为二进制
                const base64Data = dataUrl.replace(/^data:image\/\w+;base64,/, '');
                const binaryData = this.base64ToBinary(base64Data);
                // 保存文件
                yield this.plugin.app.vault.createBinary(filePath, binaryData);
                return filePath;
            }
            catch (error) {
                console.error('保存头像失败:', error);
                throw error;
            }
        });
    }
    // base64 转二进制数据
    base64ToBinary(base64) {
        const raw = window.atob(base64);
        const rawLength = raw.length;
        const array = new Uint8Array(new ArrayBuffer(rawLength));
        for (let i = 0; i < rawLength; i++) {
            array[i] = raw.charCodeAt(i);
        }
        return array;
    }
    // 添加联系人CSS规则
    addContactCssRule(contactName, imagePath) {
        // 查找或创建样式元素
        let styleElement = document.getElementById('contact-avatars-css');
        if (!styleElement) {
            styleElement = document.createElement('style');
            styleElement.id = 'contact-avatars-css';
            document.head.appendChild(styleElement);
        }
        // 创建CSS选择器（使用联系人名称作为选择器）
        const selector = `.contact-item[data-contact-name="${contactName}"] .contact-preview-avatar`;
        // 创建CSS规则
        const cssRule = `
      ${selector} {
        background-image: url('${this.plugin.app.vault.adapter.getResourcePath(imagePath)}');
        background-size: cover;
        background-position: center;
      }
      ${selector} span {
        display: none;
      }
    `;
        // 添加到样式表
        const htmlStyleElement = styleElement;
        if (htmlStyleElement.sheet) {
            try {
                const sheet = htmlStyleElement.sheet;
                sheet.insertRule(cssRule, sheet.cssRules.length);
            }
            catch (e) {
                console.error('添加CSS规则失败:', e);
            }
        }
        else {
            // 备用方法：直接添加到style元素内容中
            styleElement.textContent += cssRule;
        }
    }
    // 重置所有过滤器
    resetFilters() {
        // 重置组织筛选
        this.selectedOrganization = '';
        if (this.orgSelectTrigger) {
            this.orgSelectTrigger.textContent = '全部机构';
        }
        // 重置部门筛选
        this.selectedDepartment = '';
        if (this.deptSelectTrigger) {
            this.deptSelectTrigger.textContent = '全部部门';
        }
        // 重置角色筛选
        this.selectedRole = '';
        if (this.roleSelectTrigger) {
            this.roleSelectTrigger.textContent = '全部角色';
        }
        // 更新部门选项
        this.updateDepartmentOptions();
        // 重新筛选并显示联系人
        this.filterContacts();
    }
    // 更新部门选项
    updateDepartmentOptions() {
        if (!this.deptSelectOptions)
            return;
        // 清空现有选项
        this.deptSelectOptions.empty();
        // 添加"全部部门"选项
        const allDeptOption = this.deptSelectOptions.createEl('div', {
            cls: 'select-option',
            text: '全部部门'
        });
        // 点击选择全部部门
        allDeptOption.addEventListener('click', () => {
            this.selectedDepartment = '';
            this.deptSelectTrigger.textContent = '全部部门';
            this.deptSelectOptions.style.display = 'none';
            this.filterContacts();
        });
        // 如果选择了特定组织，只显示该组织下的部门
        if (this.selectedOrganization && this.organizations[this.selectedOrganization]) {
            // 获取当前组织下的部门列表
            const departments = this.organizations[this.selectedOrganization];
            // 添加各部门选项
            departments.forEach(dept => {
                const deptOption = this.deptSelectOptions.createEl('div', {
                    cls: 'select-option',
                    text: dept
                });
                // 点击选择部门
                deptOption.addEventListener('click', () => {
                    this.selectedDepartment = dept;
                    this.deptSelectTrigger.textContent = dept;
                    this.deptSelectOptions.style.display = 'none';
                    this.filterContacts();
                });
            });
        }
        else {
            // 如果没有选择特定组织，则显示所有部门
            const allDepartments = new Set();
            // 收集所有部门
            Object.values(this.organizations).forEach(depts => {
                depts.forEach(dept => allDepartments.add(dept));
            });
            // 添加各部门选项
            Array.from(allDepartments).sort().forEach(dept => {
                const deptOption = this.deptSelectOptions.createEl('div', {
                    cls: 'select-option',
                    text: dept
                });
                // 点击选择部门
                deptOption.addEventListener('click', () => {
                    this.selectedDepartment = dept;
                    this.deptSelectTrigger.textContent = dept;
                    this.deptSelectOptions.style.display = 'none';
                    this.filterContacts();
                });
            });
        }
    }
}
//# sourceMappingURL=data:application/json;base64,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