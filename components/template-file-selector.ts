import { App, Modal, TFile, TFolder } from 'obsidian';

export class TemplateFileSelector extends Modal {
  templateFiles: string[] = [];
  onSelect: (templatePath: string) => void;

  constructor(app: App, onSelect: (templatePath: string) => void) {
    super(app);
    this.onSelect = onSelect;
  }

  onOpen() {
    const { contentEl } = this;
    contentEl.createEl("h2", { text: "选择联系人模板文件" });

    // 获取所有markdown文件
    this.loadTemplateFiles();

    // 创建文件列表
    const fileList = contentEl.createEl("div", { cls: "template-file-list" });

    // 添加"无模板"选项
    const noTemplateOption = fileList.createEl("div", { cls: "template-file-option" });
    noTemplateOption.createEl("span", { text: "无模板（使用默认模板）" });
    noTemplateOption.addEventListener("click", () => {
      this.onSelect("");
      this.close();
    });

    // 添加所有模板文件选项
    for (const templateFile of this.templateFiles) {
      const fileOption = fileList.createEl("div", { cls: "template-file-option" });
      fileOption.createEl("span", { text: templateFile });
      fileOption.addEventListener("click", () => {
        this.onSelect(templateFile);
        this.close();
      });
    }

    // 如果没有找到任何模板文件
    if (this.templateFiles.length === 0) {
      const noFilesMessage = fileList.createEl("div", { cls: "no-templates-message" });
      noFilesMessage.createEl("span", { text: "未找到任何 .md 文件" });
    }
  }

  loadTemplateFiles() {
    this.templateFiles = [];

    const processFile = (file: any, path = "") => {
      const filePath = path ? `${path}/${file.name}` : file.name;
      
      if (file instanceof TFile && file.extension === 'md') {
        this.templateFiles.push(filePath);
      }
    };

    const processFolder = (folder: any, path = "") => {
      const folderPath = path ? `${path}/${folder.name}` : folder.name;
      
      folder.children.forEach(child => {
        if (child instanceof TFile) {
          processFile(child, folderPath);
        } else if (child.children) {
          processFolder(child, folderPath);
        }
      });
    };

    // 处理根目录下的所有文件和文件夹
    this.app.vault.root.children.forEach(child => {
      if (child instanceof TFile) {
        processFile(child);
      } else if (child instanceof TFolder) {
        processFolder(child);
      }
    });

    // 按字母顺序排序
    this.templateFiles.sort();
  }

  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
} 