import { App, Modal, Notice, TFile } from 'obsidian';
import ContactPlugin from '../main';
import { RecentSearchItem } from '../types';

export class AddContactModal extends Modal {
  plugin: ContactPlugin;
  
  // 表单元素
  private nameInput: HTMLInputElement;
  private organizationInput: HTMLInputElement;
  private departmentInput: HTMLInputElement;
  private positionInput: HTMLInputElement;
  private mobileInput: HTMLInputElement;
  private phoneInput: HTMLInputElement;
  private emailInput: HTMLInputElement;
  private addressInput: HTMLInputElement;
  private autoParseTextarea: HTMLTextAreaElement;
  private submitBtn: HTMLButtonElement;
  private resetAllBtn: HTMLButtonElement;
  private uploadBtn: HTMLLabelElement;
  private imageUploadInput: HTMLInputElement;
  private customTagsInput: HTMLInputElement;
  private saveDraftBtn: HTMLButtonElement;
  private tagsPoolContainer: HTMLElement;
  private availableTags: string[] = [];
  private selectedTags: Set<string> = new Set();
  
  // 状态标记
  private isAdjusting: boolean = false;
  private currentDraftId: string | null = null; // 当前编辑的草稿ID
  
  constructor(app: App, plugin: ContactPlugin) {
    super(app);
    this.plugin = plugin;
  }
  
  onOpen() {
    const { contentEl } = this;
    contentEl.addClass('contact-obcm-add--contact-form');
    
    // 清除草稿ID（如果是新建联系人）
    this.currentDraftId = null;
    
    this.createFormStructure();
    this.setupEventListeners();
    
    // 在窗口打开时检查网络状态并显示提示
    this.checkAndShowNetworkStatus();
    
    // 监听草稿数据填充事件
    this.setupDraftDataListener();
  }
  
  private createFormStructure() {
    const { contentEl } = this;
    
    // 设置模态框标题到header中
    const modalHeader = this.modalEl.querySelector('.modal-header');
    if (modalHeader) {
      const titleEl = modalHeader.querySelector('.modal-title');
      if (titleEl) {
        titleEl.innerHTML = `
          <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="8.5" cy="7" r="4"></circle>
            <line x1="20" y1="8" x2="20" y2="14"></line>
            <line x1="23" y1="11" x2="17" y2="11"></line>
          </svg>
          新增联系人
        `;
      } else {
        // 如果没有title元素，创建一个
        const newTitleEl = document.createElement('div');
        newTitleEl.className = 'modal-title';
        newTitleEl.innerHTML = `
          <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="8.5" cy="7" r="4"></circle>
            <line x1="20" y1="8" x2="20" y2="14"></line>
            <line x1="23" y1="11" x2="17" y2="11"></line>
          </svg>
          新增联系人
        `;
        modalHeader.appendChild(newTitleEl);
      }
    }
    
    // 创建组合表单区域
    const combinedSections = contentEl.createEl('div', { cls: 'contact-obcm-add--combined-sections' });
    
    // 基本信息区域
    this.createBasicInfoSection(combinedSections);
    
    // 联系方式区域
    this.createContactInfoSection(combinedSections);
    
    // 智能识别区域
    this.createAutoParseSection(contentEl);
    
    // 自定义标签区域
    this.createCustomTagsSection(contentEl);
    
    // 表单操作区域
    this.createFormActions(contentEl);
  }
  
  private createBasicInfoSection(container: HTMLElement) {
    const section = container.createEl('div', { cls: 'contact-obcm-add--form-section' });
    
    const title = section.createEl('h4', { cls: 'contact-obcm-add--form-section-title' });
    title.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <path d="M12 8v4"></path>
        <path d="M12 16h.01"></path>
      </svg>
      基本信息
    `;
    
    const formGroup = section.createEl('div', { cls: 'contact-obcm-add--form-group' });
    
    // 姓名字段
    this.createFormField(formGroup, '姓名', 'name', '请输入联系人姓名', true);
    
    // 机构字段
    this.createFormField(formGroup, '机构', 'organization', '请输入所属机构');
    
    // 部门字段
    this.createFormField(formGroup, '部门', 'department', '请输入所属部门');
    
    // 岗位字段
    this.createFormField(formGroup, '岗位', 'position', '请输入岗位职称');
  }
  
  private createContactInfoSection(container: HTMLElement) {
    const section = container.createEl('div', { cls: 'contact-obcm-add--form-section' });
    
    const title = section.createEl('h4', { cls: 'contact-obcm-add--form-section-title' });
    title.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
      </svg>
      联系方式
    `;
    
    const formGroup = section.createEl('div', { cls: 'contact-obcm-add--form-group' });
    
    // 手机号字段
    this.createFormField(formGroup, '手机号', 'mobile', '请输入手机号码', true);
    
    // 办公电话字段
    this.createFormField(formGroup, '办公电话', 'phone', '请输入办公电话');
    
    // 邮箱字段
    this.createFormField(formGroup, '邮箱', 'email', '请输入电子邮箱', false, 'email');
    
    // 地址字段
    this.createFormField(formGroup, '地址', 'address', '请输入地址信息');
  }
  
  private createFormField(
    container: HTMLElement, 
    labelText: string, 
    fieldId: string, 
    placeholder: string, 
    required: boolean = false,
    type: string = 'text'
  ) {
    const label = container.createEl('label');
    
    const span = label.createEl('span');
    const labelTextSpan = span.createEl('span', { cls: 'contact-obcm-add--label-text', text: labelText });
    
    if (required) {
      const requiredField = span.createEl('span', { 
        cls: 'contact-obcm-add--required-field',
        text: '*'
      });
      requiredField.style.color = '#ff6464';
      requiredField.style.marginLeft = '5px';
      requiredField.style.fontSize = '14px';
      requiredField.style.display = 'none';
    }
    
    const input = label.createEl('input', {
      type: type,
      attr: { id: fieldId, placeholder: placeholder }
    });
    
    if (required) {
      input.setAttribute('required', '');
    }
    
    // 保存输入元素的引用
    switch (fieldId) {
      case 'name': this.nameInput = input; break;
      case 'organization': this.organizationInput = input; break;
      case 'department': this.departmentInput = input; break;
      case 'position': this.positionInput = input; break;
      case 'mobile': this.mobileInput = input; break;
      case 'phone': this.phoneInput = input; break;
      case 'email': this.emailInput = input; break;
      case 'address': this.addressInput = input; break;
    }
  }
  
  private createAutoParseSection(container: HTMLElement) {
    const section = container.createEl('div', { cls: 'contact-obcm-add--auto-parse-section' });
    
    // 创建智能识别标题
    const title = section.createEl('h4', { cls: 'contact-obcm-add--form-section-title' });
    title.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
      </svg>
      智能识别
    `;
    
    const parseContainer = section.createEl('div', { cls: 'contact-obcm-add--auto-parse-container' });
    
    // 创建文本区域
    this.autoParseTextarea = parseContainer.createEl('textarea', {
      attr: { 
        id: 'auto-parse-text',
        placeholder: '支持图片/文本，可粘贴名片、邮件签名、通讯录截图,将自动识别并填充'
      }
    });
    this.autoParseTextarea.style.height = '60px';
    
    // 创建按钮容器
    const buttonsContainer = parseContainer.createEl('div', { cls: 'contact-obcm-add--buttons-container' });
    
    // 创建上传按钮
    this.uploadBtn = buttonsContainer.createEl('label', { 
      cls: 'contact-obcm-add--upload-btn',
      attr: { for: 'imageUpload' }
    });
    this.uploadBtn.innerHTML = `
      <svg class="contact-obcm-add--button-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 6px;">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
        <circle cx="8.5" cy="8.5" r="1.5"></circle>
        <polyline points="21 15 16 10 5 21"></polyline>
      </svg>
      上传图片
    `;
    
    // 创建隐藏的文件输入
    this.imageUploadInput = buttonsContainer.createEl('input', {
      type: 'file',
      attr: { 
        id: 'imageUpload',
        accept: 'image/*'
      }
    });
    this.imageUploadInput.style.display = 'none';
  }
  
  private createCustomTagsSection(container: HTMLElement) {
    const section = container.createEl('div', { cls: 'contact-obcm-add--custom-tags-section' });
    
    // 创建自定义标签标题
    const title = section.createEl('h4', { cls: 'contact-obcm-add--form-section-title' });
    title.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        <path d="M20.59 13.41l-7.17 7.17a2 2 0 01-2.83 0L2 12V2h10l8.59 8.59a2 2 0 010 2.82z"></path>
        <line x1="7" y1="7" x2="7.01" y2="7"></line>
      </svg>
      自定义标签
    `;
    
    const formGroup = section.createEl('div', { cls: 'contact-obcm-add--form-group' });
    
    // 创建统一的标签池容器（包含标签和输入框）
    const tagsPoolContainer = formGroup.createEl('div', { cls: 'contact-obcm-add--unified-tags-pool' });
    
    // 创建输入框（将嵌入在标签池中）
    this.customTagsInput = tagsPoolContainer.createEl('input', {
      type: 'text',
      attr: { 
        id: 'custom-tags',
        placeholder: '点击此处添加新标签...'
      },
      cls: 'contact-obcm-add--inline-tag-input'
    });
    
    // 设置统一标签池功能
    this.setupUnifiedTagsPool(tagsPoolContainer);
  }
  
  private createFormActions(container: HTMLElement) {
    const actionsContainer = container.createEl('div', { cls: 'contact-obcm-add--form-actions' });
    
    // 提示文本
    const tipDiv = actionsContainer.createEl('div', { 
      cls: 'contact-obcm-add--form-tip',
      text: ''
    });
    
    // 按钮组
    const buttonsGroup = actionsContainer.createEl('div', { cls: 'contact-obcm-add--buttons-group' });
    
    // 保存到草稿按钮
    this.saveDraftBtn = buttonsGroup.createEl('button', { 
      cls: 'contact-obcm-add--save-draft-btn',
      attr: { id: 'saveDraftBtn' }
    });
    this.saveDraftBtn.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
        <polyline points="17,21 17,13 7,13 7,21"></polyline>
        <polyline points="7,3 7,8 15,8"></polyline>
      </svg> 保存到草稿
    `;
    
    // 重置按钮
    this.resetAllBtn = buttonsGroup.createEl('button', { 
      cls: 'contact-obcm-add--reset-all-btn',
      attr: { id: 'resetAllBtn' }
    });
    this.resetAllBtn.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path>
        <path d="M3 3v5h5"></path>
      </svg> 重置
    `;
    
    // 提交按钮
    this.submitBtn = buttonsGroup.createEl('button', { 
      cls: 'contact-obcm-add--submit-btn',
      attr: { id: 'submitBtn' }
    });
    this.submitBtn.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
        <polyline points="22 4 12 14.01 9 11.01"></polyline>
      </svg> 确认添加
    `;
  }
  
  private setupEventListeners() {
    // 设置表单验证和按钮状态检查
    this.setupFormValidation();
    
    // 设置文本区域自动调整
    this.setupTextareaAutoResize();
    
    // 设置图片上传
    this.setupImageUpload();
    
    // 设置拖拽功能
    this.setupDragAndDrop();
    
    // 设置按钮事件
    this.setupButtonEvents();
    
    // 设置粘贴事件
    this.setupPasteEvents();
    
    // 设置输入框焦点效果
    this.setupInputFocusEffects();
  }
  
  private setupFormValidation() {
    const allInputs = [
      this.nameInput, this.organizationInput, this.departmentInput, 
      this.positionInput, this.mobileInput, this.phoneInput, 
      this.emailInput, this.addressInput
    ].filter(input => input);
    
    allInputs.forEach(input => {
      input.addEventListener('input', () => {
        // 清除错误状态
        if (input.value.trim().length > 0) {
          input.classList.remove('contact-obcm-add--invalid');
          input.style.borderColor = '';
          
          const requiredField = input.parentElement?.querySelector('.contact-obcm-add--required-field') as HTMLElement;
          if (requiredField) {
            requiredField.style.display = 'none';
            requiredField.classList.remove('contact-obcm-add--visible');
          }
          
          const labelText = input.parentElement?.querySelector('.contact-obcm-add--label-text') as HTMLElement;
          if (labelText) {
            labelText.classList.remove('contact-obcm-add--invalid');
            labelText.style.color = '';
          }
        }
        
        this.checkRequiredFields();
      });
      
      input.addEventListener('focus', () => {
        input.classList.remove('contact-obcm-add--invalid');
        input.style.borderColor = 'var(--interactive-accent)';
        
        const labelText = input.parentElement?.querySelector('.contact-obcm-add--label-text') as HTMLElement;
        if (labelText) {
          labelText.classList.remove('contact-obcm-add--invalid');
          labelText.style.color = 'var(--interactive-accent)';
        }
      });
      
      input.addEventListener('blur', () => {
        input.style.borderColor = '';
        
        const labelText = input.parentElement?.querySelector('.contact-obcm-add--label-text') as HTMLElement;
        if (labelText) {
          labelText.classList.remove('contact-obcm-add--invalid');
          labelText.style.color = '';
        }
      });
    });
  }
  
  private checkRequiredFields() {
    const hasName = this.nameInput && this.nameInput.value.trim();
    const hasMobile = this.mobileInput && this.mobileInput.value.trim();
    
    if (hasName && hasMobile) {
      this.submitBtn.classList.add('contact-obcm-add--active');
    } else {
      this.submitBtn.classList.remove('contact-obcm-add--active');
    }
  }
  
  private setupTextareaAutoResize() {
    if (!this.autoParseTextarea) return;
    
    const autoResize = () => {
      if (this.isAdjusting) return;
      
      this.isAdjusting = true;
      
      const scrollPos = window.scrollY;
      this.autoParseTextarea.style.height = 'auto';
      const minHeight = 60;
      const newHeight = Math.max(this.autoParseTextarea.scrollHeight, minHeight);
      this.autoParseTextarea.style.height = newHeight + 'px';
      
      window.scrollTo(0, scrollPos);
      
      this.isAdjusting = false;
    };
    
    this.autoParseTextarea.addEventListener('input', () => {
      autoResize();
      
      if (this.autoParseTextarea.value.trim().length > 0) {
        this.autoParseTextarea.classList.add('contact-obcm-add--has-content');
      } else {
        this.autoParseTextarea.classList.remove('contact-obcm-add--has-content');
      }
    });
    
    this.autoParseTextarea.addEventListener('focus', autoResize);
    window.addEventListener('resize', autoResize);
    
    // 初始化
    autoResize();
  }
  
  private setupImageUpload() {
    if (!this.imageUploadInput) return;
    
    this.imageUploadInput.addEventListener('change', (event) => {
      this.handleImageUpload(event);
    });
  }
  
  private setupDragAndDrop() {
    const dropArea = this.autoParseTextarea?.parentElement;
    if (!dropArea) return;
    
    // 防止浏览器默认行为
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      dropArea.addEventListener(eventName, (e) => {
        e.preventDefault();
        e.stopPropagation();
      });
    });
    
    // 拖拽进入和悬停状态
    dropArea.addEventListener('dragenter', (e) => {
      e.preventDefault();
      dropArea.classList.add('contact-obcm-add--drag-over');
      if (this.autoParseTextarea) {
        this.autoParseTextarea.placeholder = "松开鼠标上传图片...";
      }
    });
    
    dropArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      e.dataTransfer!.dropEffect = 'copy';
    });
    
    // 拖拽离开状态
    dropArea.addEventListener('dragleave', (e) => {
      e.preventDefault();
      // 确保真正离开了拖拽区域
      const rect = dropArea.getBoundingClientRect();
      const x = e.clientX;
      const y = e.clientY;
      
      if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
        dropArea.classList.remove('contact-obcm-add--drag-over');
        if (this.autoParseTextarea) {
          this.autoParseTextarea.placeholder = "支持图片/文本，可粘贴名片、邮件签名、通讯录截图,将自动识别并填充";
        }
      }
    });
    
    // 拖拽放下事件
    dropArea.addEventListener('drop', (e) => {
      e.preventDefault();
      dropArea.classList.remove('contact-obcm-add--drag-over');
      if (this.autoParseTextarea) {
        this.autoParseTextarea.placeholder = "支持图片/文本，可粘贴名片、邮件签名、通讯录截图,将自动识别并填充";
      }
      
      const files = e.dataTransfer?.files;
      if (files && files.length > 0) {
        this.processDroppedFiles(files);
      }
    });
  }
  
  private async processDroppedFiles(files: FileList) {
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      if (file.type.match('image.*')) {
        console.log('处理拖拽的图片文件:', file.name, file.type);
        
        // 简单检查网络状态
        if (!navigator.onLine) {
          new Notice("⚠️ 当前网络离线，无法处理图片", 5000);
          this.setTextareaValueAndAdjust("网络连接异常，无法处理拖拽的图片。\n请连接网络后重试。");
          return;
        }
        
        const event = { target: { files: [file] } };
        this.handleImageUpload(event);
        break; // 只处理第一个图片文件
      }
    }
  }
  
  private setupButtonEvents() {
    // 保存到草稿按钮
    this.saveDraftBtn?.addEventListener('click', () => {
      this.saveToDraft();
    });
    
    // 重置按钮
    this.resetAllBtn?.addEventListener('click', () => {
      this.resetAllData();
    });
    
    // 提交按钮
    this.submitBtn?.addEventListener('click', () => {
      this.handleSubmit();
    });
  }
  
  private setupPasteEvents() {
    this.autoParseTextarea?.addEventListener('paste', async (e) => {
      const clipboardData = e.clipboardData;
      if (!clipboardData) return;
      
      // 检查是否有图片文件
      const items = Array.from(clipboardData.items);
      const imageItem = items.find(item => item.type.startsWith('image/'));
      
      if (imageItem) {
        e.preventDefault();
        console.log('检测到粘贴的图片');
        
        const file = imageItem.getAsFile();
        if (file) {
          console.log('粘贴的图片文件:', file.type, '大小:', (file.size / 1024).toFixed(2) + 'KB');
          
          // 简单检查网络状态
          if (!navigator.onLine) {
            new Notice("⚠️ 当前网络离线，无法处理图片", 5000);
            this.setTextareaValueAndAdjust("网络连接异常，无法处理粘贴的图片。\n请连接网络后重试。");
            return;
          }
          
          this.handleImageUpload(file);
        }
        return;
      }
      
      // 处理文本粘贴
      setTimeout(() => {
        if (this.autoParseTextarea && this.autoParseTextarea.value.trim().length > 10) {
          this.autoParseTextarea.classList.add('contact-obcm-add--has-content');
          this.parseContactInfo();
        }
      }, 100);
    });
  }
  
  private setupUnifiedTagsPool(tagsPoolContainer: HTMLElement) {
    this.tagsPoolContainer = tagsPoolContainer;
    
    // 加载现有标签并显示
    this.loadAndDisplayTagsPool();
    
    // 设置输入框事件
    this.setupInlineTagInputEvents();
  }
  
  private async loadAndDisplayTagsPool() {
    const allTags = new Set<string>();
    
    try {
      // 确保联系人数据已加载
      await this.plugin.ensureContactsLoaded();
      
      console.log('联系人缓存信息数量:', this.plugin.contactCache.infos.length);
      
      // 从所有联系人文件中提取标签
      for (const contactInfo of this.plugin.contactCache.infos) {
        console.log('检查联系人:', contactInfo.name, '标签:', contactInfo.tags);
        if (contactInfo.tags && Array.isArray(contactInfo.tags)) {
          contactInfo.tags.forEach((tag: string) => {
            if (tag && tag.trim()) {
              console.log('添加标签:', tag.trim());
              allTags.add(tag.trim());
            }
          });
        }
      }
    } catch (error) {
      console.warn('获取联系人标签失败:', error);
    }
    
    // 保存可用标签
    this.availableTags = Array.from(allTags).sort();
    console.log('最终提取的标签:', this.availableTags);
    
    // 显示标签池
    this.updateTagsPoolDisplay();
  }
  
  private setupInlineTagInputEvents() {
    if (!this.customTagsInput) return;
    
    // 点击输入框时聚焦
    this.customTagsInput.addEventListener('focus', () => {
      this.customTagsInput.style.minWidth = '200px';
    });
    
    // 失焦时缩小
    this.customTagsInput.addEventListener('blur', () => {
      if (!this.customTagsInput.value.trim()) {
        this.customTagsInput.style.minWidth = '120px';
      }
    });
    
    // 键盘事件
    this.customTagsInput.addEventListener('keydown', (e) => {
      const inputValue = this.customTagsInput.value.trim();
      
      if (e.key === 'Enter') {
        e.preventDefault();
        if (inputValue) {
          this.addNewTag(inputValue);
        }
      }
    });
    
    // 点击容器时聚焦输入框
    this.tagsPoolContainer.addEventListener('click', (e) => {
      if (e.target === this.tagsPoolContainer) {
        this.customTagsInput.focus();
      }
    });
  }
  
  private addNewTag(tagText: string) {
    if (!tagText) {
      return;
    }
    
    // 添加到可用标签列表（如果不存在）
    if (!this.availableTags.includes(tagText)) {
      this.availableTags.push(tagText);
      this.availableTags.sort();
    }
    
    // 自动选中新添加的标签
    this.selectedTags.add(tagText);
    
    // 清空输入框
    this.customTagsInput.value = '';
    this.customTagsInput.style.minWidth = '120px';
    
    // 更新标签池显示
    this.updateUnifiedTagsPoolDisplay();
    
    // 显示成功提示
    new Notice(`已添加标签: ${tagText}`);
  }
  
  private toggleTag(tagText: string) {
    if (this.selectedTags.has(tagText)) {
      this.selectedTags.delete(tagText);
    } else {
      this.selectedTags.add(tagText);
    }
    
    // 更新标签池显示
    this.updateUnifiedTagsPoolDisplay();
  }
  
  private updateUnifiedTagsPoolDisplay() {
    if (!this.tagsPoolContainer) return;
    
    // 保存输入框引用
    const inputElement = this.customTagsInput;
    
    // 清空容器但保留输入框
    const allChildren = Array.from(this.tagsPoolContainer.children);
    allChildren.forEach(child => {
      if (child !== inputElement) {
        child.remove();
      }
    });
    
    if (this.availableTags.length === 0) {
      // 如果没有标签，在输入框前插入提示
      const emptyMsg = this.tagsPoolContainer.createEl('div', {
        cls: 'contact-obcm-add--tags-pool-empty-inline'
      });
      emptyMsg.textContent = '暂无标签';
      this.tagsPoolContainer.insertBefore(emptyMsg, inputElement);
      return;
    }
    
    // 在输入框前插入所有标签
    this.availableTags.forEach(tag => {
      const isSelected = this.selectedTags.has(tag);
      const tagElement = this.createInlineTagElement(tag, isSelected);
      this.tagsPoolContainer.insertBefore(tagElement, inputElement);
    });
  }
  
  private updateTagsPoolDisplay() {
    // 兼容性方法，调用新的统一显示方法
    this.updateUnifiedTagsPoolDisplay();
  }
  
  private createInlineTagElement(tagText: string, isSelected: boolean): HTMLElement {
    const tagEl = document.createElement('div');
    tagEl.className = `contact-obcm-add--tag-pool-item ${isSelected ? 'selected' : 'unselected'}`;
    
    const tagContent = tagEl.appendChild(document.createElement('span'));
    tagContent.className = 'contact-obcm-add--tag-text';
    tagContent.textContent = tagText;
    
    if (isSelected) {
      const checkIcon = tagEl.appendChild(document.createElement('span'));
      checkIcon.className = 'contact-obcm-add--tag-check';
      checkIcon.innerHTML = `
        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="20,6 9,17 4,12"></polyline>
        </svg>
      `;
    }
    
    // 添加点击事件
    tagEl.addEventListener('click', () => {
      this.toggleTag(tagText);
    });
    
    return tagEl;
  }
  

  
  private setupInputFocusEffects() {
    const inputs = [
      this.nameInput, this.organizationInput, this.departmentInput,
      this.positionInput, this.mobileInput, this.phoneInput,
      this.emailInput, this.addressInput, this.autoParseTextarea
    ].filter(input => input);
    
    inputs.forEach(input => {
      input.addEventListener('focus', () => {
        const labelText = input.parentElement?.querySelector('.contact-obcm-add--label-text') as HTMLElement;
        if (labelText) {
          labelText.style.color = 'var(--interactive-accent)';
          labelText.style.fontWeight = '600';
        }
      });
      
      input.addEventListener('blur', () => {
        const labelText = input.parentElement?.querySelector('.contact-obcm-add--label-text') as HTMLElement;
        if (labelText) {
          labelText.style.color = '';
          labelText.style.fontWeight = '';
        }
      });
    });
  }
  
  private async handleImageUpload(event: any) {
    const file = event.target?.files?.[0] || event;
    
    if (!file || !file.type.match('image.*')) {
      console.error('无效的图片文件');
      return;
    }
    
    console.log('开始处理图片:', file.name, file.type, '大小:', (file.size / 1024 / 1024).toFixed(2) + 'MB');
    
    // 检查文件大小 (限制为10MB)
    if (file.size > 10 * 1024 * 1024) {
      this.setTextareaValueAndAdjust("图片文件太大，请选择小于10MB的图片");
      return;
    }
    
    // 简单检查网络状态
    if (!navigator.onLine) {
      new Notice("⚠️ 当前网络离线，无法使用图片识别功能", 5000);
      this.setTextareaValueAndAdjust("网络连接异常，无法处理图片。\n请连接网络后重试，或手动输入联系人信息。");
      return;
    }
    
    this.setTextareaValueAndAdjust("正在处理图片，提取文本内容...\n这可能需要几秒钟时间。");
    
    // 尝试使用OCR识别
    this.performOCR(file).catch((error) => {
      console.error('OCR识别失败:', error);
      this.mockOCRProcess(file);
    });
  }
  
  /**
   * 检查网络状态
   * @returns {boolean} 是否有网络连接
   */
  private checkNetworkStatus(): boolean {
    // 检查navigator.onLine状态
    if (!navigator.onLine) {
      console.log('网络状态检测：离线状态');
      return false;
    }
    
    console.log('网络状态检测：在线状态');
    return true;
  }
  
  /**
   * 显示网络状态相关的用户提示
   */
  private showNetworkStatusMessage(): void {
    // 显示醒目的通知
    new Notice("❌ 网络连接异常！无法使用智能识别功能", 10000);
    
    // 在控制台输出详细信息用于调试
    console.log('=== 网络状态检测失败 ===');
    console.log('navigator.onLine:', navigator.onLine);
    console.log('当前时间:', new Date().toLocaleString());
    console.log('用户代理:', navigator.userAgent);
    
    // 更新文本区域显示
    this.setTextareaValueAndAdjust(
      "❌ 网络连接异常，无法使用智能识别功能\n\n" +
      "🔍 故障排除步骤：\n" +
      "1. 确认设备已连接到网络（WiFi或移动数据）\n" +
      "2. 检查网络连接是否稳定，尝试访问其他网站\n" +
      "3. 确认防火墙或代理设置不影响网络访问\n" +
      "4. 如果使用企业网络，请联系IT部门检查网络限制\n" +
      "5. 稍后重试，或使用手动输入模式\n\n" +
      "📝 手动输入联系人信息模板：\n\n" +
      "姓名: \n" +
      "机构: \n" +
      "部门: \n" +
      "职位: \n" +
      "手机: \n" +
      "邮箱: \n" +
      "地址: \n\n" +
      "💡 提示: 您可以复制粘贴名片文本信息，系统会自动解析填充字段"
    );
    
    // 添加重试按钮的提示
    setTimeout(() => {
      new Notice("您也可以稍后重新上传图片，系统会重新检测网络状态", 8000);
    }, 2000);
  }
  
  private async performOCR(file: File): Promise<void> {
    try {
      // 确保Tesseract库已加载
      await this.ensureTesseractLoaded();
      
      const fileUrl = URL.createObjectURL(file);
      
      try {
        const { data: { text } } = await (window as any).Tesseract.recognize(
          fileUrl,
          'chi_sim+eng',
          {
            logger: (m: any) => {
              if (m.status === 'recognizing text') {
                const progress = Math.round(m.progress * 100);
                this.setTextareaValueAndAdjust(`正在识别文本: ${progress}%\n请稍候...`);
              }
            }
          }
        );
        
        console.log('OCR识别完成，原始文本长度:', text.length);
        
        if (text.trim().length === 0) {
          this.setTextareaValueAndAdjust("未能从图片中识别出文本内容\n请尝试更清晰的图片或手动输入信息");
          return;
        }
        
        const processedText = this.preprocessOCRText(text);
        console.log('处理后文本长度:', processedText.length);
        
        this.setTextareaValueAndAdjust(processedText);
        
        // 延迟一下再解析，让用户看到识别的文本
        setTimeout(() => {
          this.enhancedParseContactInfo(processedText);
        }, 500);
        
      } finally {
        URL.revokeObjectURL(fileUrl);
      }
      
    } catch (error) {
      console.error('OCR处理出错:', error);
      throw error;
    }
  }
  
  private async ensureTesseractLoaded(): Promise<void> {
    if (typeof (window as any).Tesseract !== 'undefined') {
      return Promise.resolve();
    }
    
    return new Promise((resolve, reject) => {
      console.log('开始加载Tesseract.js库...');
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/tesseract.js@2.1.5/dist/tesseract.min.js';
      
      script.onload = () => {
        console.log('Tesseract.js库加载成功');
        resolve();
      };
      
      script.onerror = () => {
        console.error('Tesseract.js库加载失败');
        reject(new Error('无法加载OCR库，请检查网络连接'));
      };
      
      // 设置超时
      setTimeout(() => {
        if (typeof (window as any).Tesseract === 'undefined') {
          script.remove();
          reject(new Error('OCR库加载超时，请检查网络连接'));
        }
      }, 15000); // 15秒超时
      
      document.head.appendChild(script);
    });
  }
  
  private mockOCRProcess(file: File) {
    console.log('使用模拟OCR处理:', file.name);
    
    // 模拟处理进度
    this.setTextareaValueAndAdjust("OCR库加载失败，正在使用备用方案...\n请稍候...");
    
    setTimeout(() => {
      this.setTextareaValueAndAdjust(`已上传图片: ${file.name}\n\n由于OCR库不可用，请手动输入联系人信息：\n\n例如：\n姓名: 张三\n机构: ABC公司\n手机: 13800138000\n邮箱: <EMAIL>`);
      
      // 添加提示样式
      if (this.autoParseTextarea) {
        this.autoParseTextarea.classList.add('contact-obcm-add--has-content');
      }
    }, 1000);
  }
  
  private setTextareaValueAndAdjust(value: string) {
    if (this.isAdjusting || !this.autoParseTextarea) return;
    
    this.isAdjusting = true;
    
    try {
      this.autoParseTextarea.value = value;
      this.autoResizeTextarea();
      
      if (value.trim().length > 0) {
        this.autoParseTextarea.classList.add('contact-obcm-add--has-content');
      } else {
        this.autoParseTextarea.classList.remove('contact-obcm-add--has-content');
      }
    } finally {
      this.isAdjusting = false;
    }
  }
  
  private autoResizeTextarea() {
    if (!this.autoParseTextarea) return;
    
    const scrollPos = window.scrollY;
    this.autoParseTextarea.style.height = 'auto';
    const minHeight = 60;
    const newHeight = Math.max(this.autoParseTextarea.scrollHeight, minHeight);
    this.autoParseTextarea.style.height = newHeight + 'px';
    window.scrollTo(0, scrollPos);
  }
  
  private preprocessOCRText(text: string): string {
    // 处理OCR结果，移除中文字符之间的额外空格
    let processedText = text;
    
    let prevText = '';
    while (prevText !== processedText) {
      prevText = processedText;
      processedText = processedText
        .replace(/([\u4e00-\u9fa5])\s+([\u4e00-\u9fa5])/g, '$1$2')
        .replace(/([\u4e00-\u9fa5])\s+([,，。、：:；;!！?？])/g, '$1$2')
        .replace(/([,，。、：:；;!！?？])\s+([\u4e00-\u9fa5])/g, '$1$2');
    }
    
    processedText = processedText.replace(/\n\n+/g, '\n');
    
    return this.preprocessTextForDisplay(processedText);
  }
  
  private preprocessTextForDisplay(text: string): string {
    let processedText = text;
    
    // 清理重复的标签前缀
    processedText = processedText
      .replace(/(?:姓名:\s*)+/g, "姓名: ")
      .replace(/(?:机构:\s*)+/g, "机构: ")
      .replace(/(?:部门:\s*)+/g, "部门: ")
      .replace(/(?:职位:\s*)+/g, "职位: ")
      .replace(/(?:手机:\s*)+/g, "手机: ")
      .replace(/(?:电话:\s*)+/g, "电话: ")
      .replace(/(?:邮箱:\s*)+/g, "邮箱: ")
      .replace(/(?:地址:\s*)+/g, "地址: ");
    
    // 处理复合行
    processedText = processedText.replace(
      /([\u4e00-\u9fa5]{1,4})([\u4e00-\u9fa5]{2,8}(?:银行|公司|集团|企业))([\u4e00-\u9fa5]{2,10}(?:部|中心|处|科|团队))(邮箱:[\s]*[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,})/g,
      '$1\n$2\n$3\n$4'
    );
    
    const lines = processedText.split('\n');
    const formattedLines = [];
    
    for (let i = 0; i < lines.length; i++) {
      let line = lines[i].trim();
      if (!line) continue;
      formattedLines.push(line);
    }
    
    return formattedLines.join('\n');
  }
  
  private parseContactInfo() {
    const text = this.autoParseTextarea?.value;
    if (!text?.trim()) {
      if (this.autoParseTextarea) {
        this.autoParseTextarea.style.animation = 'none';
        setTimeout(() => {
          this.autoParseTextarea.style.animation = 'shake 0.5s';
          this.autoParseTextarea.placeholder = "请先粘贴联系人信息...";
        }, 10);
      }
      return;
    }
    
    // 进行智能邮箱修正并更新显示文本
    const correctedText = this.performEmailCorrection(text);
    
    // 如果文本有修正，更新文本区域
    if (correctedText !== text) {
      this.setTextareaValueAndAdjust(correctedText);
    }
    
    this.enhancedParseContactInfo(correctedText);
  }
  
  private enhancedParseContactInfo(text: string) {
    if (!text?.trim()) {
      new Notice("没有可识别的文本内容");
      return;
    }
    
    console.log("开始增强解析OCR文本:", text);
    
    // 预处理文本
    let processedText = this.preprocessOCRText(text);
    
    // 进行智能邮箱修正并更新显示文本
    processedText = this.performEmailCorrection(processedText);
    
    // 更新显示的文本区域
    if (this.autoParseTextarea) {
      const formattedText = this.preprocessTextForDisplay(processedText);
      this.setTextareaValueAndAdjust(formattedText);
    }
    
    // 解析联系人信息
    const contactInfo = this.parseContactFields(processedText);
    
    // 填充表单
    this.fillFormFields(contactInfo);
  }
  
  /**
   * 对文本中的邮箱进行智能修正，并返回修正后的文本
   */
  private performEmailCorrection(text: string): string {
    let correctedText = text;
    const emailPattern = /([a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,})/g;
    const corrections: { original: string; corrected: string }[] = [];
    
    // 找到所有邮箱并进行修正
    let match;
    while ((match = emailPattern.exec(text)) !== null) {
      const originalEmail = match[1];
      const correctionResult = this.smartCorrectEmail(originalEmail);
      
      if (correctionResult.isChanged) {
        corrections.push({
          original: originalEmail,
          corrected: correctionResult.corrected
        });
      }
    }
    
    // 应用所有修正到文本中
    corrections.forEach(({ original, corrected }) => {
      correctedText = correctedText.replace(new RegExp(original.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), corrected);
      console.log(`文本中邮箱修正: ${original} -> ${corrected}`);
    });
    
    // 如果有修正，显示提示
    if (corrections.length > 0) {
      const correctionSummary = corrections.map(c => `${c.original} → ${c.corrected}`).join(', ');
      new Notice(`已智能修正 ${corrections.length} 个邮箱: ${correctionSummary}`, 5000);
    }
    
    return correctedText;
  }
  
  private parseContactFields(text: string): any {
    const contactInfo = {
      name: [],
      organization: [],
      department: [],
      position: [],
      phone: [],
      mobile: [],
      email: [],
      address: []
    };
    
    // 实现详细的解析逻辑，类似111.html中的解析功能
    const lines = text.split(/\n/).filter(line => line.trim().length > 0);
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // 解析带标签的信息
      this.parseTaggedInfo(trimmedLine, contactInfo);
      
      // 自动识别模式
      this.autoRecognizeInfo(trimmedLine, contactInfo);
    }
    
    // 进行更复杂的解析
    this.advancedParsing(text, contactInfo);
    
    return this.selectBestCandidates(contactInfo);
  }
  
  private parseTaggedInfo(line: string, contactInfo: any) {
    const tagPatterns = [
      { pattern: /^(?:姓名|名字|联系人)[:：]\s*(.+)/, field: 'name' },
      { pattern: /^(?:机构|公司|单位|组织)[:：]\s*(.+)/, field: 'organization' },
      { pattern: /^(?:部门|科室|处室)[:：]\s*(.+)/, field: 'department' },
      { pattern: /^(?:职位|岗位|职务|职称)[:：]\s*(.+)/, field: 'position' },
      { pattern: /^(?:手机|移动电话|手机号|手机号码)[:：]\s*(.+)/, field: 'mobile' },
      { pattern: /^(?:电话|办公电话|固话|座机)[:：]\s*(.+)/, field: 'phone' },
      { pattern: /^(?:邮箱|电子邮箱|邮件|email|Email|E-mail)[:：]\s*(.+)/, field: 'email' },
      { pattern: /^(?:地址|住址|通讯地址|联系地址)[:：]\s*(.+)/, field: 'address' }
    ];
    
    for (const { pattern, field } of tagPatterns) {
      const match = line.match(pattern);
      if (match && match[1]) {
        let value = match[1].trim();
        let confidence = 0.9;
        
        // 对邮箱字段应用智能修正
        if (field === 'email') {
          const correctionResult = this.smartCorrectEmail(value);
          value = correctionResult.corrected;
          if (correctionResult.isChanged) {
            confidence = 0.85; // 修正过的邮箱置信度稍低
            console.log(`标签邮箱智能修正: ${match[1].trim()} -> ${value}`);
          }
        }
        
        if (value && !this.isDuplicate(contactInfo[field], value)) {
          contactInfo[field].push({ value, confidence });
        }
      }
    }
  }
  
  private autoRecognizeInfo(line: string, contactInfo: any) {
    // 识别手机号（中国大陆）
    const mobilePattern = /(?:^|\s)(1[3-9]\d{9})(?:\s|$)/;
    const mobileMatch = line.match(mobilePattern);
    if (mobileMatch && !this.isDuplicate(contactInfo.mobile, mobileMatch[1])) {
      contactInfo.mobile.push({ value: mobileMatch[1], confidence: 0.85 });
    }
    
    // 识别固定电话
    const phonePattern = /(?:^|\s)(\d{3,4}-?\d{7,8})(?:\s|$)/;
    const phoneMatch = line.match(phonePattern);
    if (phoneMatch && !this.isDuplicate(contactInfo.phone, phoneMatch[1])) {
      contactInfo.phone.push({ value: phoneMatch[1], confidence: 0.8 });
    }
    
    // 识别邮箱 - 使用智能修正
    const emailPattern = /([a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,})/;
    const emailMatch = line.match(emailPattern);
    if (emailMatch) {
      const originalEmail = emailMatch[1];
      const correctionResult = this.smartCorrectEmail(originalEmail);
      const finalEmail = correctionResult.corrected;
      
      if (!this.isDuplicate(contactInfo.email, finalEmail)) {
        const confidence = correctionResult.isChanged ? 0.85 : 0.9; // 修正过的邮箱置信度稍低
        contactInfo.email.push({ value: finalEmail, confidence });
        
        if (correctionResult.isChanged) {
          console.log(`邮箱智能修正: ${originalEmail} -> ${finalEmail}`);
        }
      }
    }
    
    // 识别中文姓名（2-4个中文字符，排除常见词汇）
    const namePattern = /^[\u4e00-\u9fa5]{2,4}$/;
    const excludeWords = ['公司', '银行', '集团', '企业', '部门', '中心', '处室', '科室'];
    if (namePattern.test(line) && !excludeWords.some(word => line.includes(word))) {
      if (!this.isDuplicate(contactInfo.name, line)) {
        contactInfo.name.push({ value: line, confidence: 0.7 });
      }
    }
    
    // 识别机构名称
    const orgPatterns = [
      /[\u4e00-\u9fa5]+(?:银行|公司|集团|企业|有限公司|股份有限公司|科技|投资|控股)/,
      /[\u4e00-\u9fa5]+(?:大学|学院|研究院|医院|政府|局|委|办)/
    ];
    
    for (const pattern of orgPatterns) {
      const orgMatch = line.match(pattern);
      if (orgMatch && !this.isDuplicate(contactInfo.organization, orgMatch[0])) {
        contactInfo.organization.push({ value: orgMatch[0], confidence: 0.8 });
      }
    }
    
    // 识别部门
    const deptPattern = /[\u4e00-\u9fa5]+(?:部|中心|处|科|室|组|队|院|所)/;
    const deptMatch = line.match(deptPattern);
    if (deptMatch && !this.isDuplicate(contactInfo.department, deptMatch[0])) {
      contactInfo.department.push({ value: deptMatch[0], confidence: 0.75 });
    }
    
    // 识别职位
    const positionPatterns = [
      /[\u4e00-\u9fa5]*(?:经理|主管|总监|总裁|董事|主任|副主任|秘书|助理|专员|顾问|工程师|分析师)/,
      /[\u4e00-\u9fa5]*(?:总经理|副总经理|首席|高级|资深|初级|中级|高级)/
    ];
    
    for (const pattern of positionPatterns) {
      const posMatch = line.match(pattern);
      if (posMatch && !this.isDuplicate(contactInfo.position, posMatch[0])) {
        contactInfo.position.push({ value: posMatch[0], confidence: 0.7 });
      }
    }
  }
  
  /**
   * 智能邮箱修正函数 - 优化版本
   * 功能：对OCR识别的邮箱进行精确修正，解决常见的字符混淆问题
   */
  private smartCorrectEmail(email: string): { corrected: string; isChanged: boolean } {
    if (!email || typeof email !== 'string') {
      return { corrected: email, isChanged: false };
    }
    
    // 原始邮箱保存用于比较
    const originalEmail = email;
    
    // 1. 预处理：统一处理格式问题
    let processedEmail = email
      .replace(/\s*@\s*/g, '@')  // 处理@前后的空格
      .replace(/[\[（\(]at[\]）\)]/gi, '@')  // 处理[at]或(at)格式
      .replace(/(\w+)\s*[\[（\(]\s*@\s*[\]）\)]/g, '$1@')  // 处理name [@]
      .replace(/(\w+)\s*[＠]\s*(\w+)/g, '$1@$2')  // 处理全角@
      .replace(/(\w+)\s*[#]\s*(\w+)/g, '$1@$2')   // 处理#号误识别为@
      .replace(/\s+/g, '')  // 移除所有空格
      .replace(/[Ιι]/g, 'i')  // 希腊字母规范化
      .replace(/＠/g, '@')  // 全角@符号替换
      .replace(/#/g, '@')  // #号误识别修正
      .replace(/[．。]/g, '.')  // 标点符号规范化
      .toLowerCase();  // 全部转小写
    
    // 如果不符合基本邮箱格式则返回原值
    if (!processedEmail.includes('@') || !processedEmail.includes('.')) {
      return { corrected: email, isChanged: false };
    }
    
    // 分割用户名和域名部分
    const parts = processedEmail.split('@');
    if (parts.length !== 2) {
      return { corrected: email, isChanged: false };
    }
    
    let [username, domain] = parts;
    let isChanged = false;
    
    // 2. 域名修正
    const originalDomain = domain;
    domain = this.correctDomain(domain);
    if (domain !== originalDomain) {
      isChanged = true;
      console.log(`修复邮箱域名: ${originalDomain} -> ${domain}`);
    }
    
    // 3. 用户名修正
    const originalUsername = username;
    username = this.correctUsername(username, domain);
    if (username !== originalUsername) {
      isChanged = true;
      console.log(`修复邮箱用户名: ${originalUsername} -> ${username}`);
    }
    
    // 4. 重组修正后的邮箱
    const correctedEmail = `${username}@${domain}`;
    
    // 5. 最终验证
    if (!/^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$/.test(correctedEmail)) {
      return { corrected: email, isChanged: false };
    }
    
    return {
      corrected: correctedEmail,
      isChanged: isChanged || correctedEmail !== originalEmail
    };
  }
  
  /**
   * 域名修正函数
   */
  private correctDomain(domain: string): string {
    // 预处理域名格式问题
    let correctedDomain = domain
      .replace(/\s*\.\s*/g, '.')  // 移除点号周围的空格
      .replace(/\s+/g, '');  // 移除所有空格
    
    // 0. 字符混淆预修正 - 先处理常见的字符识别错误
    correctedDomain = this.fixCharacterConfusion(correctedDomain);
    
    // 1. 银行邮箱特殊模式处理
    const bankPatterns = [
      { from: /^klb\.con\.cn$/i, to: 'klb.com.cn' },
      { from: /^klb\.c[o0]n\.cn$/i, to: 'klb.com.cn' },
      { from: /^klb\.corn\.cn$/i, to: 'klb.com.cn' },
      { from: /^klb\.com\.c[mn]$/i, to: 'klb.com.cn' },
      { from: /^klb\.c[o0]m\.c[mn]$/i, to: 'klb.com.cn' },
      { from: /^kib\.con\.cn$/i, to: 'klb.com.cn' },  // kib -> klb
      { from: /^kib\.c[o0]n\.cn$/i, to: 'klb.com.cn' },
      { from: /^kib\.corn\.cn$/i, to: 'klb.com.cn' },
      { from: /^kib\.com\.cn$/i, to: 'klb.com.cn' },
      { from: /^kib\.c[o0]m\.c[mn]$/i, to: 'klb.com.cn' },
      { from: /^icbc\.con\.cn$/i, to: 'icbc.com.cn' },
      { from: /^icbc\.c[o0]n\.cn$/i, to: 'icbc.com.cn' },
      { from: /^icbc\.corn\.cn$/i, to: 'icbc.com.cn' },
      { from: /^icbc\.com\.c[mn]$/i, to: 'icbc.com.cn' },
      { from: /^ccb\.con$/i, to: 'ccb.com' },
      { from: /^ccb\.c[o0]n$/i, to: 'ccb.com' },
      { from: /^ccb\.c[o0]m$/i, to: 'ccb.com' },
      { from: /^abc\.con$/i, to: 'abchina.com' },
      { from: /^abc\.c[o0]n$/i, to: 'abchina.com' },
      { from: /^boc\.cm$/i, to: 'boc.cn' },
      { from: /^boc\.c[mn]$/i, to: 'boc.cn' },
      { from: /^bankcomm\.con$/i, to: 'bankcomm.com' },
      { from: /^bankcomm\.c[o0]n$/i, to: 'bankcomm.com' },
      { from: /^cmbchina\.con$/i, to: 'cmbchina.com' },
      { from: /^cmbchina\.c[o0]n$/i, to: 'cmbchina.com' },
      { from: /^spdb\.con\.cn$/i, to: 'spdb.com.cn' },
      { from: /^spdb\.c[o0]n\.cn$/i, to: 'spdb.com.cn' },
      { from: /^spdb\.corn\.cn$/i, to: 'spdb.com.cn' },
      { from: /^spdb\.com\.c[mn]$/i, to: 'spdb.com.cn' },
      { from: /^cib\.con\.cn$/i, to: 'cib.com.cn' },
      { from: /^cib\.c[o0]n\.cn$/i, to: 'cib.com.cn' },
      { from: /^cib\.corn\.cn$/i, to: 'cib.com.cn' },
      { from: /^cib\.com\.c[mn]$/i, to: 'cib.com.cn' },
      { from: /^citicbank\.con$/i, to: 'citicbank.com' },
      { from: /^citicbank\.c[o0]n$/i, to: 'citicbank.com' },
      { from: /^psbc\.con$/i, to: 'psbc.com' },
      { from: /^psbc\.c[o0]n$/i, to: 'psbc.com' }
    ];
    
    for (const { from, to } of bankPatterns) {
      if (from.test(correctedDomain)) {
        console.log(`银行域名修正: ${correctedDomain} -> ${to}`);
        return to;
      }
    }
    
    // 2. 通用域名修正
    const commonDomains: { [key: string]: RegExp[] } = {
      // 国际邮箱
      'gmail.com': [
        /^g[mn]ail\.c[o0][mn]$/i, 
        /^gmail\.c[o0][mn]$/i,
        /^gmail\.c[o0]rn$/i,
        /^g[mn]ail\.c[o0]rn$/i
      ],
      'hotmail.com': [
        /^h[o0]tmail\.c[o0][mn]$/i, 
        /^hotmail\.c[o0][mn]$/i,
        /^h[o0]tmail\.c[o0]rn$/i,
        /^hotmail\.c[o0]rn$/i
      ],
      'outlook.com': [
        /^[o0]utl[o0][o0]k\.c[o0][mn]$/i, 
        /^outlook\.c[o0][mn]$/i,
        /^[o0]utl[o0][o0]k\.c[o0]rn$/i,
        /^outlook\.c[o0]rn$/i
      ],
      'yahoo.com': [
        /^yah[o0][o0]\.c[o0][mn]$/i, 
        /^yahoo\.c[o0][mn]$/i,
        /^yah[o0][o0]\.c[o0]rn$/i,
        /^yahoo\.c[o0]rn$/i
      ],
      // 国内邮箱
      'qq.com': [
        /^qq\.c[o0][mn]$/i,
        /^qq\.c[o0]rn$/i
      ],
      '163.com': [
        /^1[6g]3\.c[o0][mn]$/i, 
        /^163\.c[o0][mn]$/i,
        /^1[6g]3\.c[o0]rn$/i,
        /^163\.c[o0]rn$/i
      ],
      '126.com': [
        /^12[6g]\.c[o0][mn]$/i, 
        /^126\.c[o0][mn]$/i,
        /^12[6g]\.c[o0]rn$/i,
        /^126\.c[o0]rn$/i
      ],
      'foxmail.com': [
        /^f[o0]xmail\.c[o0][mn]$/i,
        /^f[o0]xmail\.c[o0]rn$/i
      ],
      'sina.com': [
        /^sina\.c[o0][mn]$/i,
        /^sina\.c[o0]rn$/i
      ],
      'sina.cn': [
        /^sina\.c[mn]$/i,
        /^sina\.c[no]$/i
      ]
    };
    
    for (const [correctDomain, patterns] of Object.entries(commonDomains)) {
      for (const pattern of patterns) {
        if (pattern.test(correctedDomain)) {
          console.log(`通用域名修正: ${correctedDomain} -> ${correctDomain}`);
          return correctDomain;
        }
      }
    }
    
    // 3. 增强的TLD修正 - 修正常见TLD错误
    correctedDomain = this.correctCommonTLDErrors(correctedDomain);
    
    return correctedDomain;
  }
  
  /**
   * 修正OCR中常见的字符混淆问题
   * 特别处理容易混淆的字符：l/i, 0/o, rn/m等
   */
  private fixCharacterConfusion(domain: string): string {
    let fixed = domain;
    
    // 银行域名特殊字符混淆修正
    const bankCharacterFixes = [
      // klb相关的字符混淆
      { from: /^kib\./i, to: 'klb.' },           // kib -> klb (i误识别为l)
      { from: /^k1b\./i, to: 'klb.' },           // k1b -> klb (1误识别为l)  
      { from: /^ktb\./i, to: 'klb.' },           // ktb -> klb (t误识别为l)
      { from: /^krb\./i, to: 'klb.' },           // krb -> klb (r误识别为l)
      { from: /^k[|l]b\./i, to: 'klb.' },        // k|b -> klb (|误识别为l)
      
      // icbc相关的字符混淆
      { from: /^[i1l]cbc\./i, to: 'icbc.' },     // 1cbc/lcbc -> icbc
      { from: /^ic[b6]c\./i, to: 'icbc.' },      // ic6c -> icbc
      { from: /^icb[c6]\./i, to: 'icbc.' },      // icb6 -> icbc
      
      // spdb相关的字符混淆
      { from: /^5pdb\./i, to: 'spdb.' },         // 5pdb -> spdb
      { from: /^sp[d6]b\./i, to: 'spdb.' },      // sp6b -> spdb
      { from: /^spd[b6]\./i, to: 'spdb.' },      // spd6 -> spdb
      
      // cib相关的字符混淆
      { from: /^c[i1l]b\./i, to: 'cib.' },       // c1b/clb -> cib
      { from: /^ci[b6]\./i, to: 'cib.' },        // ci6 -> cib
      
      // ccb相关的字符混淆
      { from: /^c[c6][b6]\./i, to: 'ccb.' },     // c6b/cc6 -> ccb
      { from: /^[c6]cb\./i, to: 'ccb.' },        // 6cb -> ccb
      
      // boc相关的字符混淆
      { from: /^[b6][o0]c\./i, to: 'boc.' },     // 6oc/b0c -> boc
      { from: /^bo[c6]\./i, to: 'boc.' },        // bo6 -> boc
      
      // psbc相关的字符混淆
      { from: /^p5bc\./i, to: 'psbc.' },         // p5bc -> psbc
      { from: /^ps[b6]c\./i, to: 'psbc.' },      // ps6c -> psbc
      { from: /^psb[c6]\./i, to: 'psbc.' }       // psb6 -> psbc
    ];
    
    // 通用字符混淆修正
    const generalCharacterFixes = [
      // 数字和字母混淆
      { from: /([a-z])1([a-z])/gi, to: '$1l$2' },     // 1 -> l (在字母之间)
      { from: /([a-z])0([a-z])/gi, to: '$1o$2' },     // 0 -> o (在字母之间)
      { from: /([a-z])5([a-z])/gi, to: '$1s$2' },     // 5 -> s (在字母之间)
      { from: /([a-z])6([a-z])/gi, to: '$1g$2' },     // 6 -> g (在字母之间)
      { from: /([a-z])8([a-z])/gi, to: '$1b$2' },     // 8 -> b (在字母之间)
      { from: /([a-z])9([a-z])/gi, to: '$1g$2' },     // 9 -> g (在字母之间)
      
      // 特殊字符组合混淆
      { from: /rn/gi, to: 'm' },                      // rn -> m (常见OCR错误)
      { from: /[|]/g, to: 'l' },                      // | -> l
      { from: /[Il]/g, to: 'l' },                     // I -> l (大写I混淆)
      
      // 开头的字符混淆（针对域名开头）
      { from: /^1([a-z])/i, to: 'l$1' },              // 开头的1 -> l
      { from: /^0([a-z])/i, to: 'o$1' },              // 开头的0 -> o
      { from: /^5([a-z])/i, to: 's$1' },              // 开头的5 -> s
      { from: /^6([a-z])/i, to: 'g$1' },              // 开头的6 -> g
      { from: /^8([a-z])/i, to: 'b$1' }               // 开头的8 -> b
    ];
    
    // 先应用银行特定修正
    for (const { from, to } of bankCharacterFixes) {
      if (from.test(fixed)) {
        const original = fixed;
        fixed = fixed.replace(from, to);
        console.log(`银行字符混淆修正: ${original} -> ${fixed}`);
        break; // 一旦匹配就停止，避免过度修正
      }
    }
    
    // 再应用通用字符修正
    for (const { from, to } of generalCharacterFixes) {
      const original = fixed;
      fixed = fixed.replace(from, to);
      if (fixed !== original) {
        console.log(`字符混淆修正: ${original} -> ${fixed}`);
      }
    }
    
    return fixed;
  }
  
  private advancedParsing(text: string, contactInfo: any) {
    // 处理名片格式的文本
    this.parseBusinessCardFormat(text, contactInfo);
    
    // 处理邮件签名格式
    this.parseEmailSignatureFormat(text, contactInfo);
    
    // 处理通讯录格式
    this.parseContactListFormat(text, contactInfo);
  }
  
  private parseBusinessCardFormat(text: string, contactInfo: any) {
    // 名片通常有固定的格式，姓名在前，职位和公司信息在中间，联系方式在后
    const lines = text.split('\n').map(line => line.trim()).filter(line => line);
    
    if (lines.length >= 2) {
      // 第一行通常是姓名
      const firstLine = lines[0];
      if (/^[\u4e00-\u9fa5]{2,4}$/.test(firstLine) && contactInfo.name.length === 0) {
        contactInfo.name.push({ value: firstLine, confidence: 0.9 });
      }
      
      // 查找职位和公司信息的组合行
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i];
        
        // 匹配 "职位 + 公司" 的格式
        const jobCompanyMatch = line.match(/([\u4e00-\u9fa5]+(?:经理|主管|总监|总裁|董事|主任|专员|工程师))\s*([\u4e00-\u9fa5]+(?:公司|银行|集团|企业))/);
        if (jobCompanyMatch) {
          if (!this.isDuplicate(contactInfo.position, jobCompanyMatch[1])) {
            contactInfo.position.push({ value: jobCompanyMatch[1], confidence: 0.85 });
          }
          if (!this.isDuplicate(contactInfo.organization, jobCompanyMatch[2])) {
            contactInfo.organization.push({ value: jobCompanyMatch[2], confidence: 0.85 });
          }
        }
      }
    }
  }
  
  private parseEmailSignatureFormat(text: string, contactInfo: any) {
    // 邮件签名通常包含分隔符和结构化信息
    const lines = text.split('\n').map(line => line.trim()).filter(line => line);
    
    // 查找可能的签名分隔符
    const separatorIndex = lines.findIndex(line => 
      line.includes('---') || line.includes('___') || line.includes('===')
    );
    
    if (separatorIndex !== -1) {
      // 处理签名部分
      const signatureLines = lines.slice(separatorIndex + 1);
      
      for (const line of signatureLines) {
        this.parseTaggedInfo(line, contactInfo);
        this.autoRecognizeInfo(line, contactInfo);
      }
    }
  }
  
  private parseContactListFormat(text: string, contactInfo: any) {
    // 处理通讯录导出格式
    const vcardPattern = /BEGIN:VCARD([\s\S]*?)END:VCARD/i;
    const vcardMatch = text.match(vcardPattern);
    
    if (vcardMatch) {
      const vcardContent = vcardMatch[1];
      
      // 解析vCard格式
      const fnMatch = vcardContent.match(/FN:(.*)/i);
      if (fnMatch && !this.isDuplicate(contactInfo.name, fnMatch[1].trim())) {
        contactInfo.name.push({ value: fnMatch[1].trim(), confidence: 0.95 });
      }
      
      const orgMatch = vcardContent.match(/ORG:(.*)/i);
      if (orgMatch && !this.isDuplicate(contactInfo.organization, orgMatch[1].trim())) {
        contactInfo.organization.push({ value: orgMatch[1].trim(), confidence: 0.95 });
      }
      
      const telMatches = vcardContent.match(/TEL[^:]*:(.*)/gi);
      if (telMatches) {
        telMatches.forEach(tel => {
          const number = tel.replace(/TEL[^:]*:/i, '').trim();
          if (number.match(/^1[3-9]\d{9}$/)) {
            if (!this.isDuplicate(contactInfo.mobile, number)) {
              contactInfo.mobile.push({ value: number, confidence: 0.95 });
            }
          } else {
            if (!this.isDuplicate(contactInfo.phone, number)) {
              contactInfo.phone.push({ value: number, confidence: 0.9 });
            }
          }
        });
      }
      
      const emailMatch = vcardContent.match(/EMAIL[^:]*:(.*)/i);
      if (emailMatch) {
        const originalEmail = emailMatch[1].trim();
        const correctionResult = this.smartCorrectEmail(originalEmail);
        const finalEmail = correctionResult.corrected;
        
        if (!this.isDuplicate(contactInfo.email, finalEmail)) {
          const confidence = correctionResult.isChanged ? 0.9 : 0.95;
          contactInfo.email.push({ value: finalEmail, confidence });
          
          if (correctionResult.isChanged) {
            console.log(`vCard邮箱智能修正: ${originalEmail} -> ${finalEmail}`);
          }
        }
      }
    }
  }
  
  private isDuplicate(array: any[], value: string): boolean {
    return array.some(item => item.value === value);
  }
  
  private selectBestCandidates(contactInfo: any): any {
    const result: any = {};
    
    Object.keys(contactInfo).forEach(field => {
      const candidates = contactInfo[field];
      if (candidates.length === 0) {
        result[field] = '';
        return;
      }
      
      // 按置信度排序
      candidates.sort((a: any, b: any) => b.confidence - a.confidence);
      result[field] = candidates[0].value;
    });
    
    return result;
  }
  
  private fillFormFields(contactInfo: any) {
    let foundInfo = false;
    
    this.clearErrorStates();
    
    const fieldMappings = {
      name: this.nameInput,
      organization: this.organizationInput,
      department: this.departmentInput,
      position: this.positionInput,
      mobile: this.mobileInput,
      phone: this.phoneInput,
      email: this.emailInput,
      address: this.addressInput
    };
    
    Object.keys(fieldMappings).forEach(key => {
      const element = fieldMappings[key as keyof typeof fieldMappings];
      
      if (element && contactInfo[key]) {
        foundInfo = true;
        element.value = contactInfo[key];
        
        // 添加视觉反馈
        element.style.animation = 'none';
        setTimeout(() => {
          element.classList.add('contact-obcm-add--field-highlight');
        }, 10);
        
        setTimeout(() => {
          element.classList.remove('contact-obcm-add--field-highlight');
        }, 1500);
      }
    });
    
    if (foundInfo) {
      new Notice("已成功识别并填写联系人信息");
      this.checkRequiredFields();
    } else {
      new Notice("未能识别有效的联系人信息");
    }
  }
  
  private clearErrorStates() {
    const allInputs = [
      this.nameInput, this.organizationInput, this.departmentInput,
      this.positionInput, this.mobileInput, this.phoneInput,
      this.emailInput, this.addressInput
    ].filter(input => input);
    
    allInputs.forEach(input => {
      input.classList.remove('contact-obcm-add--invalid');
      input.style.borderColor = '';
      
      const requiredField = input.parentElement?.querySelector('.contact-obcm-add--required-field') as HTMLElement;
      if (requiredField) {
        requiredField.style.display = 'none';
        requiredField.classList.remove('contact-obcm-add--visible');
      }
      
      const labelText = input.parentElement?.querySelector('.contact-obcm-add--label-text') as HTMLElement;
      if (labelText) {
        labelText.classList.remove('contact-obcm-add--invalid');
        labelText.style.color = '';
      }
    });
  }
  
  private resetAllData() {
    // 清除草稿ID，重置为新联系人模式
    this.currentDraftId = null;
    
    // 清空文本区域
    if (this.autoParseTextarea) {
      this.autoParseTextarea.value = '';
      this.autoParseTextarea.classList.remove('contact-obcm-add--has-content');
      this.autoParseTextarea.style.height = '60px';
    }
    
    // 清空所有表单字段
    const allInputs = [
      this.nameInput, this.organizationInput, this.departmentInput,
      this.positionInput, this.mobileInput, this.phoneInput,
      this.emailInput, this.addressInput, this.customTagsInput
    ].filter(input => input);
    
    allInputs.forEach(input => {
      input.value = '';
      input.classList.remove('contact-obcm-add--field-highlight', 'contact-obcm-add--invalid');
      input.style.borderColor = '';
    });
    
    // 重置标签选择状态
    this.selectedTags.clear();
    this.updateTagsPoolDisplay();
    
    this.clearErrorStates();
    this.checkRequiredFields();
    
    // 添加动画效果
    if (this.resetAllBtn) {
      this.resetAllBtn.style.backgroundColor = 'rgba(255, 100, 100, 0.1)';
      this.resetAllBtn.style.color = '#ff6464';
      
      setTimeout(() => {
        this.resetAllBtn.style.backgroundColor = '';
        this.resetAllBtn.style.color = '';
      }, 1500);
    }
  }
  
  private saveToDraft() {
    // 收集表单数据
    const draftData = {
      name: this.nameInput?.value.trim() || '',
      organization: this.organizationInput?.value.trim() || '',
      department: this.departmentInput?.value.trim() || '',
      position: this.positionInput?.value.trim() || '',
      mobile: this.mobileInput?.value.trim() || '',
      phone: this.phoneInput?.value.trim() || '',
      email: this.emailInput?.value.trim() || '',
      address: this.addressInput?.value.trim() || '',
      tags: Array.from(this.selectedTags).join(', '),
      isDraft: true,
      draftDate: new Date().toISOString()
    };
    
    // 检查是否有任何内容
    const hasContent = Object.values(draftData)
      .filter(value => typeof value === 'string')
      .some(value => value.length > 0);
    
    if (!hasContent) {
      new Notice('请先填写一些信息再保存草稿');
      return;
    }
    
    // 决定是更新现有草稿还是创建新草稿
    let draftId: string;
    let isNewDraft = false;
    
    if (this.currentDraftId) {
      // 更新现有草稿
      draftId = this.currentDraftId;
    } else {
      // 创建新草稿
      draftId = Date.now().toString();
      this.currentDraftId = draftId;
      isNewDraft = true;
    }
    
    const draftName = draftData.name || `草稿_${new Date().toLocaleDateString('zh-CN')}`;
    
    // 创建草稿联系人对象，用于在"最近联系"中显示
    const draftContact = {
      ...draftData,
      file: { path: `draft://${draftId}`, name: `${draftName}.draft` },
      path: `draft://${draftId}`,
      fileName: `${draftName}.draft`,
      isDraft: true,
      draftId: draftId
    };
    
    if (isNewDraft) {
      // 添加到最近搜索/联系中
      this.plugin.addRecentSearch(draftContact);
    } else {
      // 更新现有草稿
      this.updateExistingDraft(draftContact);
    }
    
    // 发送事件通知联系人模态框刷新最近联系列表
    this.notifyDraftSaved(draftContact);
    
    // 显示成功消息
    const action = isNewDraft ? '已保存草稿' : '已更新草稿';
    new Notice(`${action}: ${draftName}`);
    
    // 添加保存动画效果
    if (this.saveDraftBtn) {
      this.saveDraftBtn.style.backgroundColor = 'rgba(46, 160, 67, 0.1)';
      this.saveDraftBtn.style.color = '#2ea043';
      
      setTimeout(() => {
        this.saveDraftBtn.style.backgroundColor = '';
        this.saveDraftBtn.style.color = '';
      }, 1500);
    }
  }
  
  /**
   * 更新现有草稿
   */
  private updateExistingDraft(draftContact: any) {
    // 获取当前最近搜索列表
    const recentSearches = this.plugin.getRecentSearches();
    
    // 找到并更新对应的草稿
    const draftIndex = recentSearches.findIndex(item => 
      item.id === draftContact.path || item.id === `draft://${draftContact.draftId}`
    );
    
    if (draftIndex !== -1) {
      // 更新现有项目，保持时间戳以维持排序
      recentSearches[draftIndex] = {
        ...recentSearches[draftIndex],
        name: draftContact.name,
        phone: draftContact.phone || '',
        timestamp: Date.now(), // 更新时间戳使其排到最前面
        draftData: {
          name: draftContact.name,
          organization: draftContact.organization || '',
          department: draftContact.department || '',
          position: draftContact.position || '',
          mobile: draftContact.mobile || '',
          phone: draftContact.phone || '',
          email: draftContact.email || '',
          address: draftContact.address || '',
          tags: draftContact.tags || '',
          draftDate: draftContact.draftDate
        }
      };
      
      // 重新排序，最新的在最前面
      recentSearches.sort((a, b) => b.timestamp - a.timestamp);
      
      // 保存更新后的设置
      this.plugin.settings.recentSearches = recentSearches;
      this.plugin.saveSettings();
    } else {
      // 如果找不到现有草稿，则作为新草稿添加
      this.plugin.addRecentSearch(draftContact);
    }
  }
  
  /**
   * 通知草稿已保存，触发联系人模态框刷新
   */
  private notifyDraftSaved(draftContact: any) {
    // 发送自定义事件通知ContactModal刷新最近联系列表
    const event = new CustomEvent('draftSaved', {
      detail: draftContact
    });
    document.dispatchEvent(event);
  }

  /**
   * 将草稿转换为正常联系人记录
   * @param draftId 草稿ID
   * @param contactPath 新创建的联系人文件路径
   * @param formData 表单数据
   */
  private convertDraftToContact(draftId: string, contactPath: string, formData: any) {
    console.log('🔥 [AddContactModal] 开始草稿转换');
    console.log('🔥 [AddContactModal] 草稿ID:', draftId);
    console.log('🔥 [AddContactModal] 联系人路径:', contactPath);
    console.log('🔥 [AddContactModal] 表单数据:', formData);
    
    const recentSearches = this.plugin.getRecentSearches();
    const draftPath = `draft://${draftId}`;
    
    // 找到对应的草稿记录
    const draftIndex = recentSearches.findIndex(item => item.id === draftPath);
    console.log('🔥 [AddContactModal] 草稿记录索引:', draftIndex);
    
    if (draftIndex !== -1) {
      const draftRecord = recentSearches[draftIndex];
      console.log('🔥 [AddContactModal] 找到草稿记录:', draftRecord);
      
      // 创建新的正式联系人记录，保持在同样的位置
      const contactRecord: RecentSearchItem = {
        id: contactPath, // 🔥 关键：使用联系人文件路径作为ID
        name: formData.name,
        phone: formData.mobile || formData.phone || '', // 优先使用手机号
        timestamp: Date.now(), // 使用当前时间戳
        isDraft: false // 🔥 关键：设置为非草稿状态
      };
      
      console.log('🔥 [AddContactModal] 创建的正式联系人记录:', contactRecord);
      
      // 🔥 关键：就地替换草稿记录，而不是删除再添加
      recentSearches[draftIndex] = contactRecord;
      
      // 保存到设置
      this.plugin.settings.recentSearches = recentSearches;
      this.plugin.saveSettings();
      
      console.log('🔥 ✅ [AddContactModal] 草稿转换完成，保持在原位置');
      console.log('🔥 [AddContactModal] 转换后的最近搜索列表:', recentSearches);
    } else {
      console.error('🔥 ❌ [AddContactModal] 未找到对应的草稿记录:', draftPath);
    }
  }

  /**
   * 通知主窗口草稿已被转换为正式联系人
   * @param draftId 草稿ID
   * @param contactPath 新创建的联系人文件路径
   */
  private notifyDraftConverted(draftId: string, contactPath: string) {
    const event = new CustomEvent('draftConverted', { 
      detail: { draftId, contactPath } 
    });
    document.dispatchEvent(event);
  }

  /**
   * 将新创建的联系人添加到最近搜索中
   * @param contactPath 联系人文件路径
   * @param formData 表单数据
   */


  /**
   * 将新创建的联系人添加到最近搜索中
   * @param contactPath 联系人文件路径
   * @param formData 表单数据
   */
  private addNewContactToRecentSearches(contactPath: string, formData: any) {
    console.log('🔥 [AddContactModal] 添加新联系人到最近搜索');
    console.log('🔥 [AddContactModal] 联系人路径:', contactPath);
    console.log('🔥 [AddContactModal] 表单数据:', formData);
    console.log('🔥 [AddContactModal] 调用堆栈:', new Error().stack);
    
    // 创建完整的联系人对象用于添加到最近搜索
    // 确保path属性正确设置，这是最重要的标识符
    const contactForRecentSearch = {
      name: formData.name,
      path: contactPath, // 这是主要的标识符
      phone: formData.phone || '',
      mobile: formData.mobile || '',
      email: formData.email || '',
      organization: formData.organization || '',
      branch: formData.organization || '', // branch对应机构
      department: formData.department || '',
      position: formData.position || '',
      title: formData.position || '', // title对应职位
      address: formData.address || '',
      tags: formData.tags || '',
      room: formData.address || '', // room对应地址
      isDraft: false,
      // 添加创建时间戳
      createdTime: Date.now()
    };
    
    console.log('🔥 [AddContactModal] 准备传递给addRecentSearch的联系人对象:', contactForRecentSearch);
    
    // 添加到最近搜索
    this.plugin.addRecentSearch(contactForRecentSearch);
    
    // 验证是否添加成功
    const recentSearches = this.plugin.getRecentSearches();
    console.log('🔥 [AddContactModal] 添加后的最近搜索列表长度:', recentSearches.length);
    console.log('🔥 [AddContactModal] 添加后的最近搜索列表:', recentSearches);
    
    // 确保新联系人在最近搜索列表的最前面
    const newContactInRecent = recentSearches.find(item => item.id === contactPath);
    if (newContactInRecent) {
      console.log('🔥 ✅ [AddContactModal] 新联系人已成功添加到最近搜索列表顶部:', newContactInRecent.name);
      console.log('🔥 ✅ [AddContactModal] 最近搜索记录详情:', newContactInRecent);
    } else {
      console.error('🔥 ❌ [AddContactModal] 新联系人未能添加到最近搜索列表');
      console.error('🔥 ❌ [AddContactModal] 期望的路径:', contactPath);
      console.error('🔥 ❌ [AddContactModal] 实际的最近搜索列表:', recentSearches.map(item => ({ id: item.id, name: item.name })));
    }
  }

  /**
   * 通知主窗口新联系人已创建
   * @param contactPath 新创建的联系人文件路径
   */
  private notifyContactCreated(contactPath: string) {
    const event = new CustomEvent('contactCreated', { 
      detail: { contactPath } 
    });
    document.dispatchEvent(event);
  }

  /**
   * 检查指定的草稿是否存在于最近搜索中
   * @param draftId 草稿ID
   * @returns 是否存在
   */
  private isDraftExistsInRecentSearches(draftId: string): boolean {
    const recentSearches = this.plugin.getRecentSearches();
    const draftPath = `draft://${draftId}`;
    return recentSearches.some(item => item.id === draftPath && item.isDraft);
  }
  
  private async handleSubmit() {
    // 验证必填项
    let isValid = true;
    const name = this.nameInput?.value.trim();
    const mobile = this.mobileInput?.value.trim();
    
    if (!name) {
      isValid = false;
      this.showFieldError(this.nameInput);
    }
    
    if (!mobile) {
      isValid = false;
      this.showFieldError(this.mobileInput);
    }
    
    if (!isValid) {
      return;
    }
    
    // 收集表单数据
    const formData = {
      name: this.nameInput?.value.trim() || '',
      organization: this.organizationInput?.value.trim() || '',
      department: this.departmentInput?.value.trim() || '',
      position: this.positionInput?.value.trim() || '',
      mobile: this.mobileInput?.value.trim() || '',
      phone: this.phoneInput?.value.trim() || '',
      email: this.emailInput?.value.trim() || '',
      address: this.addressInput?.value.trim() || '',
      tags: Array.from(this.selectedTags).join(', ')
    };
    
    try {
      const contactsFolder = this.plugin.settings.contactFolderPath;
      const fileName = `${formData.name}.md`;
      const filePath = `${contactsFolder}/${fileName}`;
      
      await this.createContactFile(formData);
      
      // 检查是否是真正的草稿编辑（草稿在最近搜索中存在）
      console.log('🔥 [AddContactModal] 检查是否为草稿编辑');
      console.log('🔥 [AddContactModal] currentDraftId:', this.currentDraftId);
      
      // 🔥 强制检查：如果通过浮动按钮或拖拽创建，确保不会被误判为草稿编辑
      const isFromFloatingButton = !this.currentDraftId || this.currentDraftId === null;
      console.log('🔥 [AddContactModal] isFromFloatingButton:', isFromFloatingButton);
      
      const isRealDraftEdit = this.currentDraftId && this.isDraftExistsInRecentSearches(this.currentDraftId);
      console.log('🔥 [AddContactModal] isRealDraftEdit:', isRealDraftEdit);
      
      // 🔥 额外保护：如果 currentDraftId 为空或 null，强制走新建流程
      const shouldUseNewContactFlow = !this.currentDraftId || !isRealDraftEdit;
      console.log('🔥 [AddContactModal] shouldUseNewContactFlow:', shouldUseNewContactFlow);
      
      if (isRealDraftEdit && !shouldUseNewContactFlow) {
        console.log('🔥 [AddContactModal] 执行草稿转换流程');
        // 真正的草稿编辑：将草稿转换为正常联系人记录
        this.convertDraftToContact(this.currentDraftId!, filePath, formData);
        // 通知主窗口更新界面
        this.notifyDraftConverted(this.currentDraftId!, filePath);
      } else {
        console.log('🔥 [AddContactModal] 执行新建联系人流程 (强制新建模式)');
        // 全新创建的联系人：先添加到最近联系中，再通知主窗口更新界面
        this.addNewContactToRecentSearches(filePath, formData);
        
        // 额外等待一下，确保文件完全写入和缓存更新
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 再次刷新缓存，确保新联系人在缓存中
        await this.plugin.refreshContactCache();
        
        // 延迟通知，确保addRecentSearch和缓存刷新都完成
        setTimeout(() => {
          console.log('🔥 [AddContactModal] 发送contactCreated事件 (强制新建模式)');
          this.notifyContactCreated(filePath);
        }, 50);
      }
      
      new Notice('联系人创建成功！');
      this.close();
    } catch (error) {
      console.error('创建联系人失败:', error);
      new Notice('创建联系人失败: ' + (error as Error).message);
    }
  }
  
  private showFieldError(input: HTMLInputElement) {
    if (!input) return;
    
    input.classList.add('contact-obcm-add--invalid');
    input.style.borderColor = '#ff6464';
    
    const requiredField = input.parentElement?.querySelector('.contact-obcm-add--required-field') as HTMLElement;
    if (requiredField) {
      requiredField.style.display = 'inline';
      requiredField.classList.add('contact-obcm-add--visible');
    }
    
    const labelText = input.parentElement?.querySelector('.contact-obcm-add--label-text') as HTMLElement;
    if (labelText) {
      labelText.classList.add('contact-obcm-add--invalid');
      labelText.style.color = '#ff6464';
    }
    
    input.classList.add('contact-obcm-add--shake-animation');
    setTimeout(() => {
      input.classList.remove('contact-obcm-add--shake-animation');
    }, 500);
  }
  
  private async createContactFile(formData: any) {
    const contactsFolder = this.plugin.settings.contactFolderPath;
    
    // 确保联系人文件夹存在
    const folder = this.app.vault.getAbstractFileByPath(contactsFolder);
    if (!folder) {
      await this.app.vault.createFolder(contactsFolder);
    }
    
    // 生成文件名
    const fileName = `${formData.name}.md`;
    const filePath = `${contactsFolder}/${fileName}`;
    
    // 检查文件是否已存在
    const existingFile = this.app.vault.getAbstractFileByPath(filePath);
    if (existingFile) {
      throw new Error(`联系人 "${formData.name}" 已存在`);
    }
    
    // 生成文件内容（使用模板）
    const content = await this.generateContactContentFromTemplate(formData);
    
    // 创建文件
    const newFile = await this.app.vault.create(filePath, content);
    console.log('联系人文件创建成功:', newFile.path);
    
    // 等待一小段时间确保文件系统操作完成
    await new Promise(resolve => setTimeout(resolve, 50));
    
    // 强制刷新插件缓存
    console.log('开始刷新联系人缓存...');
    await this.plugin.refreshContactCache();
    console.log('联系人缓存刷新完成，缓存中的文件数量:', this.plugin.contactCache.files.length);
    
    // 验证新文件是否已被加载到缓存中
    const newContactInCache = this.plugin.contactCache.infos.find(info => info.path === filePath);
    if (newContactInCache) {
      console.log('新创建的联系人已成功加载到缓存:', newContactInCache.name);
    } else {
      console.warn('警告：新创建的联系人未在缓存中找到');
    }
  }
  
  /**
   * 根据模板生成联系人内容
   * @param formData 表单数据
   * @returns 生成的文件内容
   */
  private async generateContactContentFromTemplate(formData: any): Promise<string> {
    const templatePath = this.plugin.settings.contactTemplatePath;
    
    // 如果设置了模板路径，尝试使用模板文件
    if (templatePath && templatePath.trim()) {
      try {
        const templateFile = this.app.vault.getAbstractFileByPath(templatePath);
        if (templateFile && templateFile instanceof TFile) {
          // 读取模板文件内容
          const templateContent = await this.app.vault.read(templateFile as TFile);
          // 替换模板变量
          return this.replaceTemplateVariables(templateContent, formData);
        } else {
          console.warn(`模板文件不存在: ${templatePath}`);
          new Notice(`模板文件不存在，使用默认模板: ${templatePath}`);
        }
      } catch (error) {
        console.warn(`无法读取模板文件 ${templatePath}:`, error);
        new Notice(`模板文件读取失败，使用默认模板: ${templatePath}`);
      }
    }
    
    // 如果没有设置模板或模板文件不存在，使用默认模板
    return this.generateDefaultContactContent(formData);
  }
  
  /**
   * 替换模板中的变量
   * @param template 模板内容
   * @param formData 表单数据
   * @returns 替换后的内容
   */
  private replaceTemplateVariables(template: string, formData: any): string {
    let content = template;
    
    // 首先处理YAML frontmatter中的字段，移除空字段的整行
    content = this.processYamlFields(content, formData);
    
    // 定义变量映射 - 支持多种格式
    const variables = {
      // 双花括号格式
      '{{name}}': formData.name || '',
      '{{姓名}}': formData.name || '',
      '{{organization}}': formData.organization || '',
      '{{机构}}': formData.organization || '',
      '{{branch}}': formData.organization || '',  // branch对应机构
      '{{department}}': formData.department || '',
      '{{部门}}': formData.department || '',
      '{{position}}': formData.position || '',
      '{{职位}}': formData.position || '',
      '{{岗位}}': formData.position || '',
      '{{title}}': formData.position || '',  // title对应职位
      '{{mobile}}': formData.mobile || '',
      '{{手机}}': formData.mobile || '',
      '{{phone}}': formData.phone || '',
      '{{电话}}': formData.phone || '',
      '{{office_phone}}': formData.phone || '',
      '{{办公电话}}': formData.phone || '',
      '{{email}}': formData.email || '',
      '{{邮箱}}': formData.email || '',
      '{{address}}': formData.address || '',
      '{{地址}}': formData.address || '',
      '{{room}}': formData.address || '',  // room对应地址
      '{{tags}}': formData.tags || '',
      '{{标签}}': formData.tags || '',
      '{{tag}}': formData.tags || '',  // 单数形式
      '{{date}}': new Date().toLocaleDateString('zh-CN'),
      '{{日期}}': new Date().toLocaleDateString('zh-CN'),
      '{{time}}': new Date().toLocaleString('zh-CN'),
      '{{时间}}': new Date().toLocaleString('zh-CN'),
      '{{datetime}}': new Date().toLocaleString('zh-CN'),
      '{{日期时间}}': new Date().toLocaleString('zh-CN'),
      
      // 单花括号格式（默认模板使用的格式）
      '{ name }': formData.name || '',
      '{ 姓名 }': formData.name || '',
      '{ organization }': formData.organization || '',
      '{ 机构 }': formData.organization || '',
      '{ branch }': formData.organization || '',  // branch对应机构
      '{ department }': formData.department || '',
      '{ 部门 }': formData.department || '',
      '{ position }': formData.position || '',
      '{ 职位 }': formData.position || '',
      '{ 岗位 }': formData.position || '',
      '{ title }': formData.position || '',  // title对应职位
      '{ mobile }': formData.mobile || '',
      '{ 手机 }': formData.mobile || '',
      '{ phone }': formData.phone || '',
      '{ 电话 }': formData.phone || '',
      '{ office_phone }': formData.phone || '',
      '{ 办公电话 }': formData.phone || '',
      '{ email }': formData.email || '',
      '{ 邮箱 }': formData.email || '',
      '{ address }': formData.address || '',
      '{ 地址 }': formData.address || '',
      '{ room }': formData.address || '',  // room对应地址
      '{ tags }': formData.tags || '',
      '{ 标签 }': formData.tags || '',
      '{ tag }': formData.tags || '',  // 单数形式
      '{ avatar }': '',  // 头像暂时为空，后续可以扩展
      '{ car }': '',  // 车牌号暂时为空，后续可以扩展
      '{ date }': new Date().toLocaleDateString('zh-CN'),
      '{ 日期 }': new Date().toLocaleDateString('zh-CN'),
      '{ time }': new Date().toLocaleString('zh-CN'),
      '{ 时间 }': new Date().toLocaleString('zh-CN'),
      '{ datetime }': new Date().toLocaleString('zh-CN'),
      '{ 日期时间 }': new Date().toLocaleString('zh-CN')
    };
    
    // 替换所有变量
    for (const [variable, value] of Object.entries(variables)) {
      // 使用全局替换
      content = content.replace(new RegExp(this.escapeRegExp(variable), 'g'), value);
    }
    
    // 处理条件显示（如果字段有值才显示某行）
    content = this.processConditionalContent(content, formData);
    
    return content;
  }
  
  /**
   * 处理YAML frontmatter中的字段，移除空字段的整行
   * @param content 模板内容
   * @param formData 表单数据
   * @returns 处理后的内容
   */
  private processYamlFields(content: string, formData: any): string {
    // 字段映射，将表单字段映射到YAML字段
    const fieldMapping: { [key: string]: string } = {
      'name': formData.name || '',
      'branch': formData.organization || '',
      'organization': formData.organization || '',
      'department': formData.department || '',
      'title': formData.position || '',
      'position': formData.position || '',
      'phone': formData.phone || '',
      'mobile': formData.mobile || '',
      'email': formData.email || '',
      'address': formData.address || '',
      'room': formData.address || '',
      'tags': formData.tags || '',
      'tag': formData.tags || '',
      'avatar': '',
      'car': ''
    };

    // 处理YAML frontmatter部分
    const yamlRegex = /^---\n([\s\S]*?)\n---/;
    const yamlMatch = content.match(yamlRegex);
    
    if (yamlMatch) {
      let yamlContent = yamlMatch[1];
      const lines = yamlContent.split('\n');
      const processedLines: string[] = [];
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        // 检查是否是字段行（包含双花括号变量）
        const fieldMatch = line.match(/^(\s*)([^:]+):\s*["']?\{\{\s*([^}]+)\s*\}\}["']?\s*$/);
        
        if (fieldMatch) {
          const indent = fieldMatch[1];
          const fieldName = fieldMatch[2].trim();
          const variableName = fieldMatch[3].trim();
          
          // 获取对应的值
          const fieldValue = fieldMapping[variableName] || '';
          
          if (fieldValue && fieldValue.trim()) {
            // 如果有值，替换为实际值（适当添加引号）
            const quotedValue = /\s/.test(fieldValue) || fieldValue.includes(',') ? `"${fieldValue}"` : fieldValue;
            processedLines.push(`${indent}${fieldName}: ${quotedValue}`);
          } else {
            // 如果没有值，保留字段但设为空值
            processedLines.push(`${indent}${fieldName}:`);
          }
        } else {
          // 不是字段行，直接保留
          processedLines.push(line);
        }
      }
      
      // 重新组装YAML frontmatter
      const newYamlContent = processedLines.join('\n');
      content = content.replace(yamlMatch[0], `---\n${newYamlContent}\n---`);
    }
    
    return content;
  }

  /**
   * 处理条件内容显示
   * 格式：{{if:field}}内容{{/if:field}}
   * @param content 内容
   * @param formData 表单数据
   * @returns 处理后的内容
   */
  private processConditionalContent(content: string, formData: any): string {
    const conditionalRegex = /\{\{if:(\w+)\}\}([\s\S]*?)\{\{\/if:\1\}\}/g;
    
    return content.replace(conditionalRegex, (match, fieldName, conditionalContent) => {
      const fieldValue = formData[fieldName];
      // 如果字段有值且不为空字符串，则显示条件内容
      if (fieldValue && fieldValue.trim && fieldValue.trim()) {
        return conditionalContent;
      }
      return ''; // 否则返回空字符串
    });
  }
  
  /**
   * 转义正则表达式特殊字符
   * @param string 要转义的字符串
   * @returns 转义后的字符串
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
  
  /**
   * 生成默认联系人内容（当没有模板时使用）
   * @param formData 表单数据
   * @returns 默认格式的内容
   */
  private generateDefaultContactContent(formData: any): string {
    const lines = [];
    
    // YAML前置信息
    if (formData.tags) {
      const tags = formData.tags.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag);
      if (tags.length > 0) {
        lines.push('---');
        lines.push('tags:');
        tags.forEach((tag: string) => {
          lines.push(`  - ${tag}`);
        });
        lines.push('---');
        lines.push('');
      }
    }
    
    lines.push(`# ${formData.name}`);
    lines.push('');
    
    if (formData.organization) {
      lines.push(`**机构**: ${formData.organization}`);
    }
    
    if (formData.department) {
      lines.push(`**部门**: ${formData.department}`);
    }
    
    if (formData.position) {
      lines.push(`**职位**: ${formData.position}`);
    }
    
    lines.push('');
    lines.push('## 联系方式');
    lines.push('');
    
    if (formData.mobile) {
      lines.push(`**手机**: ${formData.mobile}`);
    }
    
    if (formData.phone) {
      lines.push(`**电话**: ${formData.phone}`);
    }
    
    if (formData.email) {
      lines.push(`**邮箱**: ${formData.email}`);
    }
    
    if (formData.address) {
      lines.push(`**地址**: ${formData.address}`);
    }
    
    lines.push('');
    lines.push('## 备注');
    lines.push('');
    lines.push('---');
    lines.push(`*创建时间: ${new Date().toLocaleString('zh-CN')}*`);
    
    return lines.join('\n');
  }
  
  /**
   * 旧版本的生成内容函数（保持向后兼容）
   * @deprecated 请使用 generateContactContentFromTemplate 方法
   */
  private generateContactContent(formData: any): string {
    return this.generateDefaultContactContent(formData);
  }
  
  private correctUsername(username: string, domain: string): string {
    let correctedUsername = username;
    
    // 1. 常见单词修正
    const wordCorrections: { [key: string]: string } = {
      'adm[i1]n': 'admin',
      'off[i1]ce': 'office',
      'he[l1i]{2}o': 'hello',
      '[i1]nfo': 'info',
      'ma[i1][l1i]': 'mail',
      'serv[i1]ce': 'service',
      'sa[l1i]es': 'sales',
      'market[i1]ng': 'marketing'
    };
    
    for (const [pattern, replacement] of Object.entries(wordCorrections)) {
      const regex = new RegExp(`^${pattern}`, 'i');
      if (regex.test(correctedUsername)) {
        correctedUsername = correctedUsername.replace(regex, replacement);
        break;
      }
    }
    
    // 2. 字符混淆修正 - 根据域名类型区分策略
    const isLetterDomain = /gmail|hotmail|outlook|yahoo|foxmail|126|163|sina|qq/.test(domain);
    const isBankDomain = /klb|icbc|ccb|boc|abc|cib|citic|cmbc|spdb|psbc/.test(domain);
    
    if (isLetterDomain) {
      // 个人邮箱倾向于使用字母
      correctedUsername = correctedUsername
        .replace(/(?<![a-z0-9])1(?![a-z0-9])/g, 'l')  // 孤立的1→l
        .replace(/([a-z])1([a-z])/g, '$1l$2')  // 单词中的1→l
        .replace(/([a-z])0([a-z])/g, '$1o$2')  // 单词中的0→o
        .replace(/\b0([a-z]+)/g, 'o$1');  // 开头的0→o
    }
    
    if (isBankDomain) {
      // 银行邮箱常见姓名格式修正
      correctedUsername = correctedUsername
        .replace(/zh[e0]ng/g, 'zhang')
        .replace(/l[i1]u/g, 'liu')
        .replace(/wa?ng/g, 'wang')
        .replace(/zh[a0][o0]/g, 'zhao')
        .replace(/ch[e0]n/g, 'chen')
        .replace(/ya?ng/g, 'yang')
        .replace(/f[e0]ng/g, 'feng')
        .replace(/[\,，\s_\-]+/g, '.')  // 分隔符规范化
        .replace(/\.{2,}/g, '.');  // 多点号处理
    }
    
    // 3. 通用清理
    correctedUsername = correctedUsername
      .replace(/[^\w.-]/g, '')  // 移除非法字符
      .replace(/\.{2,}/g, '.')  // 处理多个点号
      .replace(/^\.+|\.+$/g, '');  // 移除首尾点号
    
    return correctedUsername;
  }
  
  onClose() {
    // 清理事件监听器和样式
    const { contentEl } = this;
    contentEl.empty();
  }
  
  /**
   * 修正常见TLD错误的专用函数
   * 处理OCR识别中常见的顶级域名错误
   */
  private correctCommonTLDErrors(domain: string): string {
    let correctedDomain = domain;
    
    // 双重TLD修正规则（.com.cn, .net.cn, .org.cn, .edu.cn, .gov.cn）
    const doubleTLDPatterns = [
      // .com.cn 相关错误
      { from: /\.con\.cn$/i, to: '.com.cn' },
      { from: /\.c[o0]n\.cn$/i, to: '.com.cn' },
      { from: /\.corn\.cn$/i, to: '.com.cn' },
      { from: /\.c[o0]rn\.cn$/i, to: '.com.cn' },
      { from: /\.com\.c[mn]$/i, to: '.com.cn' },
      { from: /\.c[o0]m\.c[mn]$/i, to: '.com.cn' },
      { from: /\.com\.c[no]$/i, to: '.com.cn' },
      { from: /\.c[o0]m\.c[no]$/i, to: '.com.cn' },
      
      // .net.cn 相关错误
      { from: /\.net\.c[mn]$/i, to: '.net.cn' },
      { from: /\.ne[rt]\.cn$/i, to: '.net.cn' },
      { from: /\.net\.c[no]$/i, to: '.net.cn' },
      { from: /\.ne[rt]\.c[mn]$/i, to: '.net.cn' },
      
      // .org.cn 相关错误
      { from: /\.[o0]rg\.cn$/i, to: '.org.cn' },
      { from: /\.org\.c[mn]$/i, to: '.org.cn' },
      { from: /\.[o0]rg\.c[mn]$/i, to: '.org.cn' },
      { from: /\.org\.c[no]$/i, to: '.org.cn' },
      
      // .edu.cn 相关错误
      { from: /\.edu\.c[mn]$/i, to: '.edu.cn' },
      { from: /\.edu\.c[no]$/i, to: '.edu.cn' },
      { from: /\.ed[uo]\.cn$/i, to: '.edu.cn' },
      
      // .gov.cn 相关错误
      { from: /\.gov\.c[mn]$/i, to: '.gov.cn' },
      { from: /\.g[o0]v\.cn$/i, to: '.gov.cn' },
      { from: /\.gov\.c[no]$/i, to: '.gov.cn' },
      { from: /\.g[o0]v\.c[mn]$/i, to: '.gov.cn' }
    ];
    
    // 单一TLD修正规则
    const singleTLDPatterns = [
      // .com 相关错误
      { from: /\.con$/i, to: '.com' },
      { from: /\.c[o0]n$/i, to: '.com' },
      { from: /\.corn$/i, to: '.com' },
      { from: /\.c[o0]rn$/i, to: '.com' },
      { from: /\.c[o0][mn]$/i, to: '.com' },
      { from: /\.co$/i, to: '.com' },
      { from: /\.c[o0]$/i, to: '.com' },
      { from: /\.cm$/i, to: '.com' },
      { from: /\.c[mn]$/i, to: '.com' },
      
      // .cn 相关错误
      { from: /\.c[no]$/i, to: '.cn' },
      { from: /\.c[0o][mn]$/i, to: '.cn' },
      
      // .net 相关错误
      { from: /\.ne[rt]$/i, to: '.net' },
      { from: /\.n[e3][rt]$/i, to: '.net' },
      { from: /\.n[e3]t$/i, to: '.net' },
      
      // .org 相关错误
      { from: /\.[o0]rg$/i, to: '.org' },
      { from: /\.0rg$/i, to: '.org' },
      { from: /\.[o0]r[gq]$/i, to: '.org' },
      
      // .edu 相关错误
      { from: /\.ed[uo]$/i, to: '.edu' },
      { from: /\.[e3]du$/i, to: '.edu' },
      { from: /\.[e3]d[uo]$/i, to: '.edu' },
      
      // .gov 相关错误
      { from: /\.g[o0]v$/i, to: '.gov' },
      { from: /\.g0v$/i, to: '.gov' },
      { from: /\.g[o0][vw]$/i, to: '.gov' },
      
      // .info 相关错误
      { from: /\.[i1]nf[o0]$/i, to: '.info' },
      { from: /\.inf[o0]$/i, to: '.info' },
      { from: /\.[i1]nfo$/i, to: '.info' },
      
      // .biz 相关错误
      { from: /\.[b6][i1]z$/i, to: '.biz' },
      { from: /\.b[i1]z$/i, to: '.biz' },
      
      // .cc 相关错误
      { from: /\.c[c0]$/i, to: '.cc' },
      { from: /\.[c0][c0]$/i, to: '.cc' }
    ];
    
    // 应用双重TLD修正
    for (const { from, to } of doubleTLDPatterns) {
      if (from.test(correctedDomain)) {
        const original = correctedDomain;
        correctedDomain = correctedDomain.replace(from, to);
        console.log(`双重TLD修正: ${original} -> ${correctedDomain}`);
        return correctedDomain;
      }
    }
    
    // 应用单一TLD修正
    for (const { from, to } of singleTLDPatterns) {
      if (from.test(correctedDomain)) {
        const original = correctedDomain;
        correctedDomain = correctedDomain.replace(from, to);
        console.log(`TLD修正: ${original} -> ${correctedDomain}`);
        return correctedDomain;
      }
    }
    
    // 特殊情况处理：连续的错误字符
    correctedDomain = correctedDomain
      .replace(/\.c0rn/gi, '.corn')  // 先处理 c0rn
      .replace(/\.corn/gi, '.com')   // 再处理 corn
      .replace(/\.c0m/gi, '.com')    // 处理 c0m
      .replace(/\.c0n/gi, '.con')    // 先处理 c0n
      .replace(/\.con/gi, '.com')    // 再处理 con
      .replace(/\.c0/gi, '.co')      // 处理 c0
      .replace(/\.co$/gi, '.com');   // 最后处理 co
    
    return correctedDomain;
  }
  
  /**
   * 检查并显示网络状态提示
   */
  private async checkAndShowNetworkStatus(): Promise<void> {
    // 简单检查网络状态
    const isOnline = navigator.onLine;
    
    if (!isOnline) {
      // 网络离线，显示提示
      this.showNetworkOfflineWarning();
    } else {
      // 网络在线，显示可用提示
      this.showNetworkOnlineInfo();
    }
  }
  
  /**
   * 显示网络离线警告
   */
  private showNetworkOfflineWarning(): void {
    // 更新文本区域显示离线提示
    this.setTextareaValueAndAdjust(
      "⚠️ 网络连接异常，智能识别功能不可用\n\n" +
      "不过您仍可以正常添加联系人：\n\n" +
      "📝 直接输入信息到下方表单，或\n" +
      "📋 粘贴名片文本到此处，系统会自动解析\n\n" +
      "———————————————————————\n" +
      "示例格式：\n" +
      "张三  招商银行信用卡中心\n" +
      "手机：13800138000\n" +
      "邮箱：<EMAIL>\n" +
      "———————————————————————\n\n" +
      "连接网络后可使用图片识别功能"
    );
    
    // 显示简洁的通知
    new Notice("⚠️ 网络离线，智能识别不可用，但可以手动添加", 4000);
  }
  
  /**
   * 显示网络在线信息
   */
  private showNetworkOnlineInfo(): void {
    // 保持智能识别框为空白
    this.setTextareaValueAndAdjust("");
    
    // 显示简洁的欢迎通知
    new Notice("✅ 联系人插件已就绪，所有功能可用", 2000);
  }
  
  /**
   * 设置草稿数据监听器
   */
  private setupDraftDataListener(): void {
    const handleDraftData = (event: CustomEvent) => {
      const draftData = event.detail;
      if (draftData) {
        this.fillFormWithDraftData(draftData);
      }
    };
    
    // 添加事件监听器
    document.addEventListener('fillDraftData', handleDraftData as EventListener);
    
    // 确保在模态框关闭时移除监听器
    const originalClose = this.close.bind(this);
    this.close = () => {
      document.removeEventListener('fillDraftData', handleDraftData as EventListener);
      originalClose();
    };
  }
  
  /**
   * 用草稿数据填充表单
   */
  private fillFormWithDraftData(eventDetail: any): void {
    const draftData = eventDetail.draftData || eventDetail; // 兼容旧的传递方式
    const draftId = eventDetail.draftId;
    
    // 设置当前编辑的草稿ID
    this.currentDraftId = draftId;
    
    try {
      // 填充基本信息
      if (this.nameInput && draftData.name) {
        this.nameInput.value = draftData.name;
      }
      
      if (this.organizationInput && draftData.organization) {
        this.organizationInput.value = draftData.organization;
      }
      
      if (this.departmentInput && draftData.department) {
        this.departmentInput.value = draftData.department;
      }
      
      if (this.positionInput && draftData.position) {
        this.positionInput.value = draftData.position;
      }
      
      // 填充联系方式
      if (this.mobileInput && draftData.mobile) {
        this.mobileInput.value = draftData.mobile;
      }
      
      if (this.phoneInput && draftData.phone) {
        this.phoneInput.value = draftData.phone;
      }
      
      if (this.emailInput && draftData.email) {
        this.emailInput.value = draftData.email;
      }
      
      if (this.addressInput && draftData.address) {
        this.addressInput.value = draftData.address;
      }
      
      // 填充标签
      if (draftData.tags) {
        const tags = draftData.tags.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag);
        tags.forEach((tag: string) => {
          this.selectedTags.add(tag);
          // 如果标签不在可用标签列表中，添加它
          if (!this.availableTags.includes(tag)) {
            this.availableTags.push(tag);
            this.availableTags.sort();
          }
        });
        // 更新标签池显示
        this.updateTagsPoolDisplay();
      }
      
      // 检查必填字段状态
      this.checkRequiredFields();
      
      // 显示成功提示
      new Notice('已加载草稿数据');
      
    } catch (error) {
      console.error('填充草稿数据失败:', error);
      new Notice('加载草稿数据失败');
    }
  }
} 