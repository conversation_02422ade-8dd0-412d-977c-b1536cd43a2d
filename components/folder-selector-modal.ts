import { App, Modal, TFolder } from 'obsidian';

export class FolderSelectorModal extends Modal {
  folders: string[] = [];
  onSelect: (folder: string) => void;

  constructor(app: App, onSelect: (folder: string) => void) {
    super(app);
    this.onSelect = onSelect;
  }

  onOpen() {
    const { contentEl } = this;
    contentEl.createEl("h2", { text: "选择联系人文件夹" });

    // 获取所有文件夹
    this.loadFolders();

    // 创建文件夹列表
    const folderList = contentEl.createEl("div", { cls: "folder-list" });

    // 添加根目录选项
    const rootOption = folderList.createEl("div", { cls: "folder-option" });
    rootOption.createEl("span", { text: "/ (根目录)" });
    rootOption.addEventListener("click", () => {
      this.onSelect("/");
      this.close();
    });

    // 添加所有文件夹选项
    for (const folder of this.folders) {
      const folderOption = folderList.createEl("div", { cls: "folder-option" });
      folderOption.createEl("span", { text: folder });
      folderOption.addEventListener("click", () => {
        this.onSelect(folder);
        this.close();
      });
    }
  }

  loadFolders() {
    this.folders = [];

    const processFolder = (folder: any, path = "") => {
      const folderPath = path ? `${path}/${folder.name}` : folder.name;
      this.folders.push(folderPath);

      folder.children.forEach(child => {
        if (child instanceof TFolder) {
          processFolder(child, folderPath);
        }
      });
    };

    //
    // 处理根目录下的所有文件夹
    this.app.vault.root.children.forEach(child => {
      if (child instanceof TFolder) {
        processFolder(child);
      }
    });

    // 按字母顺序排序
    this.folders.sort();
  }

  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
} 