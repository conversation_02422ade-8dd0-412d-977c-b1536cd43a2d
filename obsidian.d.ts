// 简单的Obsidian API类型声明，只包含插件所需的基本类型
declare module 'obsidian' {
  // 基本类型
  export class Plugin {
    app: App;
    manifest: any;
    loadData(): Promise<any>;
    saveData(data: any): Promise<void>;
    addRibbonIcon(icon: string, title: string, callback: (evt: MouseEvent) => any): HTMLElement;
    addCommand(command: Command): void;
    addSettingTab(tab: PluginSettingTab): void;
    registerEvent(evt: any): void;
    register(cb: () => any): void;
  }

  export class App {
    vault: Vault;
    workspace: Workspace;
    setting: any;
    commands: {
      commands: Record<string, any>;
    };
  }

  export class Modal {
    app: App;
    contentEl: HTMLElement;
    modalEl: HTMLElement;
    constructor(app: App);
    open(): void;
    close(): void;
    onOpen(): void;
    onClose(): void;
  }

  export class PluginSettingTab {
    app: App;
    containerEl: HTMLElement;
    constructor(app: App, plugin: Plugin);
    display(): void;
    hide(): void;
  }

  export class Setting {
    constructor(containerEl: HTMLElement);
    setName(name: string): this;
    setDesc(desc: string): this;
    addText(cb: (text: any) => any): this;
    addButton(cb: (button: any) => any): this;
    addHotkey(cb: (hotkey: any) => any): this;
    onChange(cb: (value: any) => any): this;
  }

  export class Notice {
    constructor(message: string, timeout?: number);
    hide(): void;
  }

  export interface Vault {
    adapter: {
      getResourcePath(path: string): string;
    };
    getAbstractFileByPath(path: string): TAbstractFile | null;
    read(file: TFile): Promise<string>;
    delete(file: TAbstractFile): Promise<void>;
    create(path: string, data: string): Promise<TFile>;
    createFolder(path: string): Promise<void>;
    createBinary(path: string, data: ArrayBuffer | Uint8Array): Promise<TFile>;
    on(name: string, callback: (file: TAbstractFile) => any, ctx?: any): EventRef;
    root: TFolder;
  }

  export interface Workspace {
    on(name: string, callback: (...args: any[]) => any, ctx?: any): EventRef;
    getLeaf(): WorkspaceLeaf;
    getLeavesOfType(type: string): WorkspaceLeaf[];
  }

  export interface WorkspaceLeaf {
    openFile(file: TFile): Promise<void>;
    view: any;
  }

  export interface TAbstractFile {
    path: string;
    name: string;
    vault: Vault;
    basename: string;
    extension: string;
  }

  export interface TFile extends TAbstractFile {
    content: string;
    stat: any;
  }

  export interface TFolder extends TAbstractFile {
    children: TAbstractFile[];
  }

  export const TFile: any;
  export const TFolder: any;

  export interface EventRef {}

  export interface Command {
    id: string;
    name: string;
    callback: () => any;
    hotkeys?: HotkeySetting[];
  }

  export interface HotkeySetting {
    modifiers: string[];
    key: string;
  }

  export function setIcon(el: HTMLElement, icon: string): void;

  // DOM扩展
  export {};
}

// NodeJS 类型
declare namespace NodeJS {
  interface Timeout {}
}

// DOM扩展接口
interface HTMLElement {
  createEl<K extends keyof HTMLElementTagNameMap>(tag: K, attrs?: any): HTMLElementTagNameMap[K];
  empty(): void;
  addClass(className: string): void;
  removeClass(className: string): void;
  style?: CSSStyleDeclaration;
}

// DOM原生扩展
interface HTMLStyleElement {
  sheet: CSSStyleSheet;
} 