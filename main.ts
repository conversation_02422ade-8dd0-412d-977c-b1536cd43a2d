import { Plugin, Notice, TFile, TFolder } from 'obsidian';
import { ContactSettingTab } from './settings';
import { ContactModal } from './modal';
import { AddContactModal } from './components/add-contact-modal';
import { DEFAULT_SETTINGS, ContactPluginSettings } from './types';

export default class ContactPlugin extends Plugin {
  settings: ContactPluginSettings;
  contactCache: {
    files: TFile[];
    infos: any[];
    lastUpdated: number;
  } = {
    files: [],
    infos: [],
    lastUpdated: 0
  };
  
  originalBodyStyle: string = '';
  debounceTimeout: NodeJS.Timeout | null = null;
  modalOpenState: boolean = false;

  async onload() {
    try {
      await this.loadSettings();
      
      // 保存body原始样式
      this.originalBodyStyle = document.body.getAttribute('style') || '';
      
      // 预加载联系人数据
      await this.refreshContactCache().catch(err => {
        console.error("预加载联系人数据失败:", err);
        // 即使预加载失败，也允许插件继续加载
      });
      
      // 添加插件激活标识
      document.body.classList.add('plugin-contact-active');
      
      // 重置可能被插件影响的样式
      this.resetNoteStyles();
      
      this.addRibbonIcon("address-book", "ContactNotes", () => {
        this.openContactManager();
      });
      
      this.addCommand({
        id: "open-contact-manager",
        name: "打开 ContactNotes",
        callback: () => this.openContactManager(),
        hotkeys: this.settings.openContactsHotkey ? [this.settings.openContactsHotkey] : []
      });
      
      this.addCommand({
        id: "add-new-contact",
        name: "添加新联系人",
        callback: () => this.openAddContactModal()
      });
      
      this.addSettingTab(new ContactSettingTab(this.app, this));
      
      // 监听文件变化，更新缓存
      this.registerEvent(
        this.app.vault.on("modify", (file) => {
          if (file instanceof TFile && file.extension === "md") {
            // 检查文件是否在联系人文件夹中
            if (file.path.startsWith(this.settings.contactFolderPath)) {
              // 延迟更新缓存，避免频繁刷新
              this.debouncedRefreshCache();
            }
          }
        })
      );
      
      this.registerEvent(
        this.app.vault.on("create", (file) => {
          if (file instanceof TFile && file.extension === "md") {
            if (file.path.startsWith(this.settings.contactFolderPath)) {
              this.debouncedRefreshCache();
            }
          }
        })
      );
      
      this.registerEvent(
        this.app.vault.on("delete", (file) => {
          if (file instanceof TFile && file.extension === "md") {
            if (file.path.startsWith(this.settings.contactFolderPath)) {
              // 立即通知主窗口文件被删除
              this.notifyContactDeleted(file.path);
              // 刷新缓存
              this.debouncedRefreshCache();
            }
          }
        })
      );
      
      // 添加布局变更监听器，确保样式一致性
      this.registerEvent(
        this.app.workspace.on("layout-change", () => {
          // 当布局变更时清除所有插件添加的样式
          this.cleanupAllStyles();
          // 恢复原始body样式
          this.restoreBodyStyle();
        })
      );
      
      // 添加活动叶片变更监听器，确保新打开的笔记样式正确
      this.registerEvent(
        this.app.workspace.on("active-leaf-change", (leaf) => {
          // 当切换到新的视图时，清除可能影响滚动的样式
          this.cleanupAllStyles();
          // 恢复原始body样式
          this.restoreBodyStyle();
        })
      );
      
      // 为所有已有头像的联系人添加CSS规则
      this.initializeContactAvatarCss();
      
      // 添加全局拖放拦截，防止在联系人模态框开启时图片被插入到笔记中
      this.setupGlobalDropInterception();
      
      // 🔥 关键修复：设置全局联系人事件监听器（插件级别，确保整个生命周期内有效）
      this.setupGlobalContactEventListeners();
      
      console.log("ContactNotes插件加载成功");
    } catch (error) {
      console.error("ContactNotes加载失败:", error);
      new Notice("ContactNotes插件加载失败: " + (error as Error).message, 10000);
    }
  }

  // 重置可能被插件影响的笔记样式
  resetNoteStyles() {
    // 添加一个清理样式的CSS，仅应用于模态框内
    const styleElement = document.createElement('style');
    styleElement.id = 'contact-plugin-reset-styles';
    styleElement.innerHTML = `
      /* 只应用于打开联系人模态框时 */
      .modal .markdown-source-view,
      .modal .markdown-preview-view {
        margin: 0 auto !important;
        padding: var(--file-margins) !important;
        transform: none !important;
        left: auto !important;
        right: auto !important;
        width: 100% !important;
        max-width: var(--file-line-width) !important;
        position: relative !important;
      }
      
      /* 插件特定的样式修复，不影响全局 */
      .Contact_Page {
        overflow-y: auto !important; 
      }
      
      /* 防止添加插件时全局内容位移 */
      .workspace-tabs {
        position: relative !important;
      }
    `;
    
    // 检查是否已存在，如果存在则先移除
    const existingStyle = document.getElementById('contact-plugin-reset-styles');
    if (existingStyle) {
      existingStyle.remove();
    }
    
    // 添加到文档头
    document.head.appendChild(styleElement);
  }
  
  // 刷新所有Markdown视图的方法
  refreshAllMarkdownViews() {
    const allMarkdownViews = this.app.workspace.getLeavesOfType('markdown');
    if (allMarkdownViews.length > 0) {
      allMarkdownViews.forEach(leaf => {
        try {
          // 只重新渲染预览模式，不干扰编辑模式
          if (leaf.view && leaf.view.previewMode) {
            leaf.view.previewMode.rerender(true);
          }
        } catch (e) {
          console.log('重新渲染笔记视图时出错:', e);
        }
      });
    }
  }
  
  // 修复样式问题的方法
  fixStyleIssues() {
    // 添加临时样式修复，仅应用于模态框相关元素
    const tempFixStyle = document.createElement('style');
    tempFixStyle.id = 'contact-plugin-temp-fix';
    tempFixStyle.innerHTML = `
      /* 只针对模态框内容的修复 */
      .modal .markdown-source-view,
      .modal .markdown-preview-view {
        margin: 0 auto !important;
        padding: var(--file-margins) !important;
        transform: none !important;
        position: relative !important;
        top: 0 !important;
      }
      
      /* 限制在模态框内的样式修复 */
      .modal .view-content {
        margin-top: 0 !important;
        position: static !important;
      }
    `;
    document.head.appendChild(tempFixStyle);
    
    // 5秒后移除临时样式修复
    setTimeout(() => {
      const styleEl = document.getElementById('contact-plugin-temp-fix');
      if (styleEl) {
        styleEl.remove();
      }
    }, 5000);
  }
  
  // 添加防抖函数
  debouncedRefreshCache() {
    if (this.debounceTimeout) {
      clearTimeout(this.debounceTimeout);
    }
    this.debounceTimeout = setTimeout(() => {
      this.refreshContactCache();
    }, 2000); // 2秒后更新缓存
  }
  
  // 创建或更新联系人缓存
  async ensureContactsLoaded(forceRefresh = false) {
    try {
      const now = Date.now();
      
      // 确保缓存对象有效
      if (!this.contactCache || !this.contactCache.files) {
        this.contactCache = {
          files: [],
          infos: [],
          lastUpdated: 0
        };
      }
      
      // 如果缓存为空或者超过5分钟未更新或者强制刷新，则刷新缓存
      if (forceRefresh || 
          this.contactCache.files.length === 0 || 
          now - this.contactCache.lastUpdated > 300000) {
        await this.refreshContactCache();
      }
      
      return this.contactCache;
    } catch (error) {
      console.error("确保联系人数据已加载失败:", error);
      
      // 返回空缓存而不是抛出错误
      return {
        files: [],
        infos: [],
        lastUpdated: Date.now()
      };
    }
  }
  
  // 刷新联系人缓存
  async refreshContactCache() {
    try {
      const files = await this.getContactFiles();
      const infos = [];
      
      for (const file of files) {
        try {
          const contactInfo = await this.getContactInfo(file);
          infos.push(contactInfo);
        } catch (error) {
          console.error(`处理文件 ${file.path} 时出错:`, error);
          // 继续处理下一个文件
        }
      }
      
      this.contactCache = {
        files,
        infos,
        lastUpdated: Date.now()
      };
      
      return this.contactCache;
    } catch (error) {
      console.error("刷新联系人缓存失败:", error);
      
      // 确保缓存至少是一个有效的空对象
      if (!this.contactCache.files) {
        this.contactCache = {
          files: [],
          infos: [],
          lastUpdated: Date.now()
        };
      }
      
      return this.contactCache;
    }
  }
  
  onunload() {
    // 移除插件激活标识
    document.body.classList.remove('plugin-contact-active');
    
    // 恢复body原始样式
    this.restoreBodyStyle();
    
    // 修复任何可能存在的样式问题
    this.fixStyleIssues();
  }
  
  async loadSettings() {
    this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
  }
  
  async saveSettings() {
    await this.saveData(this.settings);
  }
  
  async getContactFiles() {
    try {
      const { vault } = this.app;
      // 确保文件夹路径不为空
      if (!this.settings.contactFolderPath) {
        console.warn("联系人文件夹路径未设置，使用默认路径: Contacts");
        this.settings.contactFolderPath = "Contacts";
        await this.saveSettings();
      }
      
      const contactFolder = vault.getAbstractFileByPath(this.settings.contactFolderPath);
      if (!contactFolder || !(contactFolder instanceof TFolder)) {
        console.warn(`联系人文件夹 "${this.settings.contactFolderPath}" 不存在，将在设置中提示用户创建`);
        return []; // 返回空数组，而不是抛出错误
      }
      
      const files: TFile[] = [];
      const getFilesInFolder = (folder: any, currentPath: string = '') => {
        folder.children.forEach((child: any) => {
          if (child instanceof TFile && child.extension === "md") {
            files.push(child as TFile);
          } else if (child instanceof TFolder) {
            // 构造当前文件夹的相对路径
            const childPath = currentPath ? `${currentPath}/${child.name}` : child.name;
            
            // 检查是否需要排除此文件夹
            const isExcluded = this.settings.excludeFolders.some(excludePath => {
              // 支持完整路径匹配和简单名称匹配
              return childPath === excludePath || 
                     child.name === excludePath ||
                     childPath.endsWith('/' + excludePath);
            });
            
            if (!isExcluded) {
              getFilesInFolder(child as any, childPath);
            }
          }
        });
      };
      
      getFilesInFolder(contactFolder as any, this.settings.contactFolderPath);
      return files;
    } catch (error) {
      console.error("获取联系人文件失败:", error);
      return []; // 出错时返回空数组
    }
  }
  
  async getContactInfo(file: TFile) {
    try {
      const content = await this.app.vault.read(file);
      const contactInfo: any = {
        name: file.basename,
        path: file.path,
        content: content
      };
      
      // 检查是否包含 YAML/Frontmatter 格式（---开头和结尾的块）
      const frontmatterMatch = content.match(/^---\s*\n([\s\S]*?)\n---\s*\n/);
      
      if (frontmatterMatch) {
        // 解析 Frontmatter
        const frontmatterContent = frontmatterMatch[1];
        const frontmatterLines = frontmatterContent.split('\n');
        
        let currentKey = '';
        let arrayValues: string[] = [];
        let isParsingArray = false;
        
        for (const line of frontmatterLines) {
          const trimmedLine = line.trim();
          
          // 检查是否是数组项 (以 - 开头)
          if (trimmedLine.startsWith('- ')) {
            if (isParsingArray) {
              arrayValues.push(trimmedLine.substring(2).trim());
            }
            continue;
          }
          
          // 如果之前在解析数组，现在结束了，保存数组
          if (isParsingArray && currentKey) {
            contactInfo[currentKey.toLowerCase().trim()] = arrayValues;
            isParsingArray = false;
            arrayValues = [];
            currentKey = '';
          }
          
          // 检查是否是键值对
          const match = line.match(/^([^:]+):\s*(.*)$/);
          if (match) {
            const [_, key, value] = match;
            const cleanKey = key.toLowerCase().trim();
            const cleanValue = value.trim();
            
            // 如果值为空，可能是数组的开始
            if (!cleanValue) {
              currentKey = cleanKey;
              isParsingArray = true;
              arrayValues = [];
            } else {
              // 普通键值对
              contactInfo[cleanKey] = cleanValue;
            }
          }
        }
        
        // 处理最后一个数组（如果存在）
        if (isParsingArray && currentKey) {
          contactInfo[currentKey.toLowerCase().trim()] = arrayValues;
        }
      } else {
        // 原有的普通文本解析逻辑
        const lines = content.split("\n");
        for (const line of lines) {
          const match = line.match(/^([^:]+):\s*(.+)$/);
          if (match) {
            const [_, key, value] = match;
            contactInfo[key.toLowerCase().trim()] = value.trim();
          }
        }
      }
      
      // 添加头像信息
      if (this.settings.contactAvatars && this.settings.contactAvatars[file.path]) {
        contactInfo.avatar = this.settings.contactAvatars[file.path];
      }
      
      return contactInfo;
    } catch (error) {
      console.error(`读取联系人信息失败 (${file.path}):`, error);
      // 返回基本信息，而不是完全失败
      return {
        name: file.basename,
        path: file.path,
        content: "",
        error: "读取文件内容失败"
      };
    }
  }
  
  // 添加独立的打开 ContactNotes 方法
  async openContactManager() {
    let loadingNotice: Notice | null = null;
    let loadingTimer = setTimeout(() => {
      loadingNotice = new Notice("正在加载联系人数据...", 30000);
    }, 800); // 仅当加载时间超过800毫秒才显示提示
    
    try {
      // 检查并确保联系人文件夹路径设置存在
      if (!this.settings.contactFolderPath) {
        this.settings.contactFolderPath = "Contacts";
        await this.saveSettings();
        console.log("已设置默认联系人文件夹路径为:", this.settings.contactFolderPath);
      }
      
      // 确保数据已加载
      await this.ensureContactsLoaded();
    } catch (error) {
      console.error("ContactNotes加载失败:", error);
    } finally {
      // 无论成功还是失败，清除定时器并关闭通知
      clearTimeout(loadingTimer);
      if (loadingNotice) {
        loadingNotice.hide();
      }
      
      try {
        // 检查联系人文件夹是否存在
        const folder = this.app.vault.getAbstractFileByPath(this.settings.contactFolderPath);
        if (!folder || !(folder instanceof TFolder)) {
          // 如果文件夹不存在，提示用户但仍然打开界面
          new Notice(`联系人文件夹不存在: ${this.settings.contactFolderPath}\n请在设置中配置正确的路径。`, 8000);
          console.warn(`联系人文件夹不存在: ${this.settings.contactFolderPath}, 将尝试打开空联系人模态框`);
        }
        
        // 即使没有联系人数据，也打开模态框
        const modal = new ContactModal(this.app, this);
        modal.open();
      } catch (modalError) {
        console.error("无法打开联系人模态框:", modalError);
        
        // 显示更详细的错误信息给用户
        new Notice(`打开 ContactNotes 失败: ${(modalError as Error).message}`, 5000);
        
        // 如果模态框无法打开，自动打开设置
        setTimeout(() => {
          this.app.setting.open();
          // 使用manifest.id或直接使用硬编码的插件ID
          const pluginId = this.manifest ? this.manifest.id : "obsidian-contact-plugin";
          this.app.setting.openTabById(pluginId);
        }, 1000);
      }
    }
  }
  
  async openAddContactModal() {
    try {
      // 创建并打开添加联系人模态框
      const modal = new AddContactModal(this.app, this);
      modal.open();
      
    } catch (error) {
      console.error("打开添加联系人模态框失败:", error);
      new Notice("打开添加联系人模态框失败: " + (error as Error).message);
    }
  }

  async openAddContactModalWithFile(file: File) {
    try {
      console.log('打开添加联系人模态框并处理文件:', file.name);
      
      // 创建并打开添加联系人模态框
      const modal = new AddContactModal(this.app, this);
      
      // 重写onOpen方法来处理文件
      const originalOnOpen = modal.onOpen.bind(modal);
      modal.onOpen = function() {
        // 调用原始的onOpen方法
        originalOnOpen();
        
        // 延迟处理文件，确保DOM已经渲染完成
        setTimeout(() => {
          try {
            console.log('模态框已打开，开始处理文件');
            
            // 创建一个FileList对象
            const fileList = [file] as any;
            fileList.length = 1;
            
            // 直接调用processDroppedFiles方法（如果存在）
            if (typeof (modal as any).processDroppedFiles === 'function') {
              (modal as any).processDroppedFiles(fileList);
              new Notice('图片已自动加载到智能识别区域，请完善联系人信息', 3000);
            } else {
              console.warn('processDroppedFiles方法不存在');
              new Notice('请手动上传图片到智能识别区域', 3000);
            }
          } catch (error) {
            console.error('处理文件失败:', error);
            new Notice('处理文件失败: ' + (error as Error).message, 3000);
          }
        }, 500);
      };
      
      modal.open();
      
    } catch (error) {
      console.error("打开添加联系人模态框失败:", error);
      new Notice("打开添加联系人模态框失败: " + (error as Error).message);
    }
  }
  
  // 添加最近搜索记录
  addRecentSearch(contact: any) {
    if (!contact || !contact.name) {
      console.warn('🔥 [MainPlugin] addRecentSearch: 无效的联系人对象', contact);
      return;
    }
    
    console.log('🔥 [MainPlugin] addRecentSearch 接收到的联系人对象:', contact);
    console.log('🔥 [MainPlugin] 调用堆栈:', new Error().stack);
    
    // 创建搜索记录对象，保存完整信息
    const searchItem: any = {
      id: contact.path || contact.draftId,
      name: contact.name,
      phone: contact.phone || contact.mobile || '',
      timestamp: Date.now()
    };
    
    // 如果是草稿，保存完整的草稿数据
    if (contact.isDraft) {
      searchItem.isDraft = true;
      searchItem.draftData = {
        name: contact.name,
        organization: contact.organization || '',
        department: contact.department || '',
        position: contact.position || '',
        mobile: contact.mobile || '',
        phone: contact.phone || '',
        email: contact.email || '',
        address: contact.address || '',
        tags: contact.tags || '',
        draftDate: contact.draftDate
      };
    } else {
      // 对于正常联系人，保存所有可能的字段信息
      // 这是关键修复：确保所有字段都被正确保存，以便在界面中显示
      searchItem.isDraft = false;
      searchItem.mobile = contact.mobile || '';
      searchItem.email = contact.email || '';
      searchItem.organization = contact.organization || contact.branch || '';
      searchItem.branch = contact.branch || contact.organization || '';
      searchItem.department = contact.department || '';
      searchItem.position = contact.position || contact.title || '';
      searchItem.title = contact.title || contact.position || '';
      searchItem.address = contact.address || contact.room || '';
      searchItem.room = contact.room || contact.address || '';
      searchItem.tags = contact.tags || '';
      
      // 保存创建时间戳（如果有的话）
      if (contact.createdTime) {
        searchItem.createdTime = contact.createdTime;
      }
      
      // 确保路径信息正确
      if (!searchItem.id && contact.path) {
        searchItem.id = contact.path;
      }
    }
    
    console.log('🔥 [MainPlugin] 创建的搜索记录对象:', searchItem);
    
    // 检查是否已存在相同ID的记录
    const existingIndex = this.settings.recentSearches.findIndex(item => item.id === searchItem.id);
    if (existingIndex !== -1) {
      console.log('🔥 [MainPlugin] 发现已存在的记录，索引:', existingIndex);
      // 如果存在，则更新时间戳并移到最前面
      this.settings.recentSearches.splice(existingIndex, 1);
    }
    
    // 添加到最前面
    this.settings.recentSearches.unshift(searchItem);
    console.log('🔥 [MainPlugin] 已添加到最近搜索列表顶部');
    
    // 保持最多10条记录
    if (this.settings.recentSearches.length > 10) {
      this.settings.recentSearches = this.settings.recentSearches.slice(0, 10);
      console.log('🔥 [MainPlugin] 已截取到最多10条记录');
    }
    
    console.log('🔥 [MainPlugin] 最终的最近搜索列表长度:', this.settings.recentSearches.length);
    console.log('🔥 [MainPlugin] 最终的最近搜索列表:', this.settings.recentSearches.map(item => ({ id: item.id, name: item.name })));
    
    // 保存设置
    this.saveSettings();
    console.log('🔥 [MainPlugin] 设置已保存');
  }
  
  // 获取最近搜索记录
  getRecentSearches() {
    return this.settings.recentSearches || [];
  }

  /**
   * 通知主窗口联系人文件被删除
   * @param filePath 被删除的文件路径
   */
  notifyContactDeleted(filePath: string) {
    console.log('联系人文件被删除:', filePath);
    
    // 发送自定义事件通知ContactModal刷新
    const event = new CustomEvent('contactDeleted', {
      detail: { filePath }
    });
    document.dispatchEvent(event);
    
    // 同时从最近搜索中移除该联系人
    this.removeFromRecentSearches(filePath);
  }

  /**
   * 从最近搜索中移除指定的联系人
   * @param filePath 文件路径
   */
  removeFromRecentSearches(filePath: string) {
    const recentSearches = this.getRecentSearches();
    const filteredSearches = recentSearches.filter(item => 
      item.id !== filePath
    );
    
    if (filteredSearches.length !== recentSearches.length) {
      this.settings.recentSearches = filteredSearches;
      this.saveSettings();
      console.log('已从最近搜索中移除被删除的联系人:', filePath);
    }
  }
  
  // 清理所有插件添加的样式
  cleanupAllStyles() {
    const stylesToRemove = [
      'contact-plugin-reset-styles',
      'contact-plugin-temp-fix',
      'contact-modal-fix'
    ];
    
    stylesToRemove.forEach(id => {
      const styleEl = document.getElementById(id);
      if (styleEl) {
        styleEl.remove();
      }
    });
    
    // 添加基本的样式重置
    this.resetNoteStyles();
  }
  
  // 恢复body原始样式
  restoreBodyStyle() {
    // 不再简单地移除整个style属性，而是保护用户的自定义主题颜色
    // 只移除插件可能添加的特定样式属性，保留其他样式
    const currentStyle = document.body.getAttribute('style') || '';
    
    // 定义插件可能添加的样式属性（需要移除的）
    const pluginStyleProps = [
      'position',
      'transform',
      'top',
      'left',
      'margin',
      'padding',
      'zoom',
      'transform-origin'
    ];
    
    // 解析当前样式
    const styleDeclarations = currentStyle.split(';').filter(decl => decl.trim());
    const filteredStyles: string[] = [];
    
    styleDeclarations.forEach(decl => {
      const prop = decl.split(':')[0]?.trim().toLowerCase();
      // 只保留非插件相关的样式
      if (prop && !pluginStyleProps.includes(prop)) {
        filteredStyles.push(decl.trim());
      }
    });
    
    // 重新设置过滤后的样式
    if (filteredStyles.length > 0) {
      document.body.setAttribute('style', filteredStyles.join('; ') + ';');
    } else {
      document.body.removeAttribute('style');
    }
  }
  
  // 设置全局拖放拦截
  setupGlobalDropInterception() {
    this.modalOpenState = false;
    
    // 监听联系人模态框开启状态
    const originalOpen = ContactModal.prototype.open;
    ContactModal.prototype.open = function(...args) {
      if (this.plugin) {
        this.plugin.modalOpenState = true;
        console.log('联系人模态框已打开，全局拖拽拦截已启用');
      }
      return originalOpen.apply(this, args);
    };
    
    const originalClose = ContactModal.prototype.close;
    ContactModal.prototype.close = function(...args) {
      if (this.plugin) {
        this.plugin.modalOpenState = false;
        console.log('联系人模态框已关闭，全局拖拽拦截已禁用');
      }
      return originalClose.apply(this, args);
    };
    
    // 添加全局拖放拦截
    const handleGlobalDrop = (e: DragEvent) => {
      if (this.modalOpenState && e.dataTransfer && e.dataTransfer.files.length > 0) {
        console.log('全局拖拽事件触发，正在检查是否需要拦截');
        
        // 检查是否是拖拽到联系人卡片上
        // 向上遍历DOM树，查找是否是联系人卡片元素
        let element = e.target as HTMLElement | null;
        let isContactItem = false;
        
        while (element) {
          if (element.classList && (
              element.classList.contains('contact-item') ||
              element.classList.contains('contact-preview-avatar') ||
              element.classList.contains('contact-preview')
          )) {
            isContactItem = true;
            console.log('拖拽目标是联系人卡片，不拦截');
            break;
          }
          element = element.parentElement;
        }
        
        // 如果不是拖拽到联系人卡片上，则拦截
        if (!isContactItem) {
          // 检查是否是图片文件
          const file = e.dataTransfer.files[0];
          if (file && file.type.startsWith('image/')) {
            console.log('拦截拖拽图片事件到编辑器', file.name);
            // 阻止事件冒泡和默认行为
            e.stopPropagation();
            e.preventDefault();
            return false;
          }
        }
      }
    };
    
    // 捕获阶段注册事件，确保在其他处理程序之前处理
    document.addEventListener('drop', handleGlobalDrop as EventListener, { capture: true, passive: false });
    document.addEventListener('dragover', ((e: DragEvent) => {
      if (this.modalOpenState && e.dataTransfer && e.dataTransfer.types.includes('Files')) {
        // 检查是否是拖拽到联系人卡片上
        let element = e.target as HTMLElement | null;
        let isContactItem = false;
        
        while (element) {
          if (element.classList && (
              element.classList.contains('contact-item') ||
              element.classList.contains('contact-preview-avatar') ||
              element.classList.contains('contact-preview')
          )) {
            isContactItem = true;
            break;
          }
          element = element.parentElement;
        }
        
        // 如果不是拖拽到联系人卡片上，则拦截
        if (!isContactItem) {
          // 阻止默认行为，避免编辑器显示拖放提示
          e.preventDefault();
          e.stopPropagation();
        }
      }
    }) as EventListener, { capture: true, passive: false });
    
    // 注册清理函数，在插件卸载时移除事件监听器
    this.register(() => {
      document.removeEventListener('drop', handleGlobalDrop as EventListener, { capture: true });
    });
  }
  
  // 为所有已有头像的联系人添加CSS规则
  /**
   * 设置全局联系人事件监听器
   * 这些监听器在插件整个生命周期内都有效，不依赖于主窗口的开关状态
   */
  setupGlobalContactEventListeners() {
    console.log('🔥 [MainPlugin] 设置全局联系人事件监听器');
    
    // 联系人创建事件处理器
    const handleContactCreated = async (event: CustomEvent) => {
      console.log('🔥 [MainPlugin] 收到全局contactCreated事件:', event.detail);
      
      const contactPath = event.detail?.contactPath;
      if (!contactPath) {
        console.warn('🔥 [MainPlugin] contactCreated事件缺少contactPath');
        return;
      }
      
      // 刷新联系人缓存
      console.log('🔥 [MainPlugin] 开始刷新联系人缓存...');
      await this.refreshContactCache();
      
      // 通知所有打开的ContactModal实例更新界面
      this.notifyContactModalToRefresh(contactPath, 'created');
    };
    
    // 草稿转换事件处理器
    const handleDraftConverted = async (event: CustomEvent) => {
      console.log('🔥 [MainPlugin] 收到全局draftConverted事件:', event.detail);
      
      // 刷新联系人缓存
      await this.refreshContactCache();
      
      // 通知所有打开的ContactModal实例更新界面
      this.notifyContactModalToRefresh(event.detail?.contactPath, 'converted');
    };
    
    // 草稿保存事件处理器
    const handleDraftSaved = (event: CustomEvent) => {
      console.log('🔥 [MainPlugin] 收到全局draftSaved事件:', event.detail);
      
      // 通知所有打开的ContactModal实例更新界面
      this.notifyContactModalToRefresh(null, 'draftSaved');
    };
    
    // 注册全局事件监听器
    document.addEventListener('contactCreated', handleContactCreated as EventListener);
    document.addEventListener('draftConverted', handleDraftConverted as EventListener);
    document.addEventListener('draftSaved', handleDraftSaved as EventListener);
    
    // 使用Obsidian的register方法确保在插件卸载时清理
    this.register(() => {
      console.log('🔥 [MainPlugin] 清理全局联系人事件监听器');
      document.removeEventListener('contactCreated', handleContactCreated as EventListener);
      document.removeEventListener('draftConverted', handleDraftConverted as EventListener);
      document.removeEventListener('draftSaved', handleDraftSaved as EventListener);
    });
  }
  
  /**
   * 通知ContactModal刷新界面
   * @param contactPath 联系人路径
   * @param eventType 事件类型
   */
  notifyContactModalToRefresh(contactPath: string | null, eventType: string) {
    console.log('🔥 [MainPlugin] 通知ContactModal刷新界面:', eventType, contactPath);
    
    // 发送自定义事件通知ContactModal刷新
    const refreshEvent = new CustomEvent('contactModalRefresh', {
      detail: { 
        contactPath, 
        eventType,
        timestamp: Date.now()
      }
    });
    document.dispatchEvent(refreshEvent);
  }

  initializeContactAvatarCss() {
    try {
      // 创建样式表元素
      let styleElement = document.getElementById('contact-avatars-css');
      if (!styleElement) {
        styleElement = document.createElement('style');
        styleElement.id = 'contact-avatars-css';
        document.head.appendChild(styleElement);
      } else {
        // 清空已有内容
        styleElement.textContent = '';
      }
      
      // 检查是否有联系人头像设置
      if (!this.settings.contactAvatars || Object.keys(this.settings.contactAvatars).length === 0) {
        return;
      }
      
      // 从缓存中获取联系人名称与头像路径的映射
      const nameToAvatarMap: Record<string, string> = {};
      
      // 遍历联系人缓存和设置中的头像信息
      if (this.contactCache && this.contactCache.infos) {
        this.contactCache.infos.forEach(contact => {
          // 检查联系人是否有头像路径
          if (contact.path && this.settings.contactAvatars[contact.path]) {
            nameToAvatarMap[contact.name] = this.settings.contactAvatars[contact.path];
          }
        });
      }
      
      // 为每个有头像的联系人添加CSS规则
      let cssRules = '';
      
      for (const contactName in nameToAvatarMap) {
        const imagePath = nameToAvatarMap[contactName];
        const safeContactName = CSS.escape(contactName);
        const resourcePath = this.app.vault.adapter.getResourcePath(imagePath);
        
        // 创建CSS规则
        cssRules += `a[data-href="${safeContactName}"]::before {
          background-image: url(${resourcePath});
          background-size: cover;
          background-position: center center;
          display: inline-block;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          content: "";
          margin-right: 5px;
          vertical-align: middle;
        }\n`;
      }
      
      // 添加所有规则
      styleElement.textContent = cssRules;
      
      console.log(`已初始化 ${Object.keys(nameToAvatarMap).length} 个联系人头像CSS规则`);
    } catch (error) {
      console.error('初始化联系人头像CSS规则失败:', error);
    }
  }
} 