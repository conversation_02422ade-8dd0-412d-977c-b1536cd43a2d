import { App, Modal, Notice, setIcon, TFile, TFolder } from 'obsidian';
import ContactPlugin from './main';
import { ContactInfo, SearchTag, SearchState, YamlFieldConfig } from './types';
import { contactWorkerManager } from './contact-worker-manager';
import { OptimizedContactRenderer } from './optimized-renderer';

// 扩展HTMLElement的接口
declare global {
  interface HTMLElement {
    createEl<K extends keyof HTMLElementTagNameMap>(tag: K, attrs?: any): HTMLElementTagNameMap[K];
    empty(): void;
    addClass(className: string): void;
    removeClass(className: string): void;
  }
}

export class ContactModal extends Modal {
  plugin: ContactPlugin;
  contactFiles: TFile[] = [];
  contactInfos: ContactInfo[] = [];
  filteredContacts: ContactInfo[] = [];
  organizations: Record<string, string[]> = {};
  selectedOrganization: string = '';
  selectedDepartment: string = '';
  selectedRole: string = '';
  
  // 定义优先显示的部门顺序
  priorityDepts: string[] = [
    '零售业务部', 
    '零售管理部/消保护管理部', 
    '渠道运营部/安全保卫部', 
    '资产负债部/计划财务部',
    '法律合规部', 
    '风险管理部', 
    '资产负债部', 
    '资产管理部'
  ];
  
  // 搜索相关元素
  searchInput: HTMLInputElement;
  searchResultsDropdown: HTMLElement;
  resultContainer: HTMLElement;
  
  // 多级搜索相关元素
  multiLevelSearchContainer: HTMLElement;
  tagsContainer: HTMLElement;
  searchResults: HTMLElement;
  resultsList: HTMLElement;
  levelTitle: HTMLElement;
  levelIndicator: HTMLElement;
  inputContainer: HTMLElement;
  resultsWrapper: HTMLElement;
  resultsContainer: HTMLElement;
  contentEl: HTMLElement;
  
  // 搜索状态
  isSearchFocused: boolean = false;
  searchDebounceTimer: NodeJS.Timeout | null = null;
  selectedSearchAction: ContactInfo | null = null;
  saveStateDebounceTimer: NodeJS.Timeout | null = null;
  
  // 多级搜索状态
  selectedActions: any[] = [];
  isMouseDownOnResults: boolean = false;
  hideTimeout: NodeJS.Timeout | null = null;
  isRemoving: boolean = false;
  debouncedQuery: string = '';
  keyboardSelectedIndex: number = -1;
  currentEditingTag: any = null; // 跟踪当前正在编辑的标签
  
  // 添加防抖相关的状态变量
  showMenuDebounceTimer: NodeJS.Timeout | null = null;
  lastMenuShowTime: number = 0;
  isInitializing: boolean = true; // 标识是否在初始化阶段
  
  // 滚动动画相关变量
  private scrollAnimationThreshold: number = 5; // 滚动阈值设为5px，实现精细控制
  private innerContainer: HTMLElement | null = null; // 内部容器引用
  private searchContainer: HTMLElement | null = null; // 搜索容器引用
  private lastScrollTop: number = 0; // 记录上次滚动位置
  private scrollDirection: 'up' | 'down' | 'none' = 'none'; // 滚动方向
  private menuHiddenByScroll: boolean = false; // 菜单是否因滚动被隐藏
  private isResultsHiddenByScroll: boolean = false; // 跟踪搜索结果是否因为滚动被隐藏
  
  // 保存模态框打开前的body样式
  modalBodyStyle: string = '';

  // 性能优化相关属性
  private renderBatchSize: number = 20; // 每批渲染的联系人数量
  private renderQueue: ContactInfo[] = []; // 待渲染队列
  private isRendering: boolean = false; // 是否正在渲染
  private renderCancelToken: number | null = null; // 渲染取消标记
  private visibleContactsCache: Map<string, HTMLElement> = new Map(); // DOM缓存
  private intersectionObserver: IntersectionObserver | null = null; // 视口观察器
  
  // Worker和优化渲染器
  private optimizedRenderer: OptimizedContactRenderer | null = null;
  private useWorkerOptimization: boolean = true; // 是否使用Worker优化
  private workerInitialized: boolean = false;

  constructor(app: App, plugin: ContactPlugin) {
    super(app);
    this.plugin = plugin;
    
    // 初始化缓存数据
    this.initCacheData(plugin);
    
    // 初始化Worker
    this.initializeWorker();
  }

  // 初始化缓存数据
  initCacheData(plugin: ContactPlugin) {
    // 防止缓存为空导致错误
    if (!plugin.contactCache || !plugin.contactCache.files) {
      plugin.contactCache = {
        files: [],
        infos: [],
        lastUpdated: Date.now()
      };
    }
    
    this.contactFiles = plugin.contactCache.files || [];
    this.contactInfos = plugin.contactCache.infos || [];
    this.filteredContacts = [...this.contactInfos];
    
    // 预热DOM缓存（首批联系人）
    this.preWarmDOMCache();
  }

  // 初始化Worker
  private async initializeWorker() {
    try {
      if (this.useWorkerOptimization && contactWorkerManager.isAvailable()) {
        console.log('初始化Contact Worker...');
        
        // 将联系人数据发送到Worker
        if (this.contactInfos.length > 0) {
          await contactWorkerManager.setContacts(this.contactInfos);
          this.workerInitialized = true;
          console.log('Worker初始化成功，已加载', this.contactInfos.length, '个联系人');
          
          // 提取组织结构
          this.organizations = await this.extractOrganizationsAndDepartments();
        }
      } else {
        console.log('Worker不可用，使用传统渲染方式');
        this.useWorkerOptimization = false;
        // 降级处理，直接提取组织结构
        this.organizations = await this.extractOrganizationsAndDepartments();
      }
    } catch (error) {
      console.error('Worker初始化失败:', error);
      this.useWorkerOptimization = false;
      this.workerInitialized = false;
      // 降级处理
      this.organizations = await this.extractOrganizationsAndDepartments();
    }
  }

  // 预热DOM缓存，提前准备常用联系人的DOM元素
  private preWarmDOMCache() {
    if (this.contactInfos.length === 0) return;
    
    // 预热前10个联系人的缓存键
    const recentSearches = this.plugin.getRecentSearches();
    const preWarmList = recentSearches.slice(0, 10).map(item => item.id);
    
    // 如果最近搜索不足10个，用前面的联系人补充
    if (preWarmList.length < 10) {
      const additionalContacts = this.contactInfos
        .slice(0, 10 - preWarmList.length)
        .map(contact => contact.path)
        .filter(path => !preWarmList.includes(path));
      preWarmList.push(...additionalContacts);
    }
    
    // 预生成缓存键（不实际创建DOM，只准备数据结构）
    preWarmList.forEach(contactPath => {
      if (contactPath && !this.visibleContactsCache.has(contactPath)) {
        // 预设缓存槽位，实际DOM将在需要时创建
        this.visibleContactsCache.set(contactPath, null as any);
      }
    });
  }

  // 设置模态框样式
  setModalStyles() {
    this.modalEl.style.width = '1322px';
    this.modalEl.style.height = '80vh';
    this.modalEl.style.maxWidth = '1800px';
    this.modalEl.style.padding = '0';
    this.modalEl.style.background = '#fff';
    
    // 移除自定义定位，恢复默认位置
    this.modalEl.style.left = '';
    this.modalEl.style.transform = '';
  }
  
  async onOpen() {
    console.log('=== Modal onOpen 开始 ===');
    
    // 阶段1：立即设置基础UI，避免白屏
    const { contentEl } = this;
    this.contentEl = contentEl; // 保存contentEl引用
    contentEl.addClass('Contact_Page');
    this.modalBodyStyle = document.body.getAttribute('style') || '';
    this.setModalStyles();
    
    // 阶段2：设置标题（轻量操作）
    this.setupModalTitle();
    
    // 阶段3：创建基础UI结构（不包含数据渲染）
    this.createSearchBar(contentEl);
    this.resultContainer = contentEl.createEl('div', {cls: 'contact-results'});
    
    // 立即显示加载状态，避免空白容器
    this.showLoadingState();
    
    this.createAddContactButton(contentEl);
    
    // 阶段3.5：延迟触发搜索容器动画，确保初始状态渲染完成
    if (this.inputContainer) {
      // 给初始状态足够的渲染时间，然后平滑过渡到ready状态
      setTimeout(() => {
        console.log('触发搜索输入容器就绪动画');
        this.inputContainer.removeClass('contact-obcm-search--initializing');
        this.inputContainer.addClass('contact-obcm-search--ready');
      }, 50); // 50ms延迟确保初始状态完全渲染
    }
    
    // 阶段4：优化的数据加载和渲染流程
    try {
      console.log('=== 开始数据加载 ===');
      
      // 检查是否需要刷新数据
      const needsRefresh = !this.plugin.contactCache || 
                          !this.plugin.contactCache.infos || 
                          this.plugin.contactCache.infos.length === 0 ||
                          (Date.now() - this.plugin.contactCache.lastUpdated) > 60000;
      
      console.log('needsRefresh:', needsRefresh);
      console.log('contactCache存在:', !!this.plugin.contactCache);
      console.log('联系人数量:', this.plugin.contactCache?.infos?.length || 0);
      
      // 加载数据（同步进行，减少异步层级）
      await this.plugin.ensureContactsLoaded(needsRefresh);
      
      this.initCacheData(this.plugin);
      
      // 初始化Worker和组织结构
      await this.initializeWorker();
      
      // 初始化状态
      this.selectedOrganization = '';
      this.selectedDepartment = '';
      this.selectedRole = '';
      this.filteredContacts = [...this.contactInfos];
      
      console.log('=== 数据加载完成，开始渲染 ===');
      
      // 移除加载状态并立即渲染内容
      this.hideLoadingState();
      
      // 设置事件监听器
      this.setupDraftSavedListener();
      // Airbnb风格滚动监听器 - 使用requestAnimationFrame优化性能
      let ticking = false;
      
      const handleScroll = () => {
        const scrollTop = this.contentEl.scrollTop;

        // 计算滚动方向
        if (scrollTop > this.lastScrollTop) {
          this.scrollDirection = 'down';
        } else if (scrollTop < this.lastScrollTop) {
          this.scrollDirection = 'up';
        }

        console.log('🔄 滚动事件触发 - scrollTop:', scrollTop, 'direction:', this.scrollDirection, 'threshold:', this.scrollAnimationThreshold);

        // 搜索结果容器滚动隐藏逻辑
        if (scrollTop > this.scrollAnimationThreshold) {
          // 滚动超过阈值（5px），隐藏搜索结果容器
          if (!this.isResultsHiddenByScroll && this.resultsContainer) {
            console.log('🔄 滚动超过阈值 - 隐藏搜索结果容器');
            this.isResultsHiddenByScroll = true;
            
            // 使用动画隐藏
            this.resultsContainer.removeClass('contact-obcm-search--scroll-showing');
            this.resultsContainer.addClass('contact-obcm-search--scroll-hiding');
            
            // 动画完成后设置 display: none
            setTimeout(() => {
              if (this.resultsContainer && this.isResultsHiddenByScroll) {
                this.resultsContainer.style.display = 'none';
              }
            }, 300);
          }
        }
        
        // 注意：一旦滚动隐藏，即使回到顶部也保持隐藏状态（符合用户需求）

        // 更新上次滚动位置
        this.lastScrollTop = scrollTop;

        this.debouncedSaveSearchState();
        this.handleScrollAnimation();
      };

      const onScroll = () => {
        if (!ticking) {
          requestAnimationFrame(() => {
            handleScroll();
            ticking = false;
          });
          ticking = true;
        }
      };

      this.contentEl.addEventListener('scroll', onScroll, { passive: true });
      
      // 恢复搜索状态
      this.restoreSearchState();
      
      // 立即渲染联系人（减少延迟）
      this.renderContacts();
      
      // 异步设置最终交互，避免阻塞渲染
      requestAnimationFrame(() => {
        this.setupFinalInteractions();
      });
      
    } catch (error) {
      console.error('数据加载失败:', error);
      this.hideLoadingState();
      // 降级处理：显示错误状态或使用现有数据
      this.showErrorState(error);
      this.setupFinalInteractions();
    }
  }
  
  // 设置模态框标题
  private setupModalTitle() {
    const modalHeader = this.modalEl.querySelector('.modal-header');
    if (modalHeader) {
      const titleEl = modalHeader.querySelector('.modal-title');
      if (titleEl) {
        titleEl.textContent = 'ContactNotes';
      } else {
        const newTitleEl = document.createElement('div');
        newTitleEl.className = 'modal-title';
        newTitleEl.textContent = 'ContactNotes';
        modalHeader.appendChild(newTitleEl);
      }
    }
  }
  
  // 显示加载状态
  private showLoadingState() {
    if (!this.resultContainer) return;
    
    // 创建加载容器
    const loadingContainer = this.resultContainer.createEl('div', {
      cls: 'contact-loading-state'
    });
    
    // 加载指示器
    const loadingIndicator = loadingContainer.createEl('div', {
      cls: 'contact-loading-indicator'
    });
    
    // 加载动画
    const spinner = loadingIndicator.createEl('div', {
      cls: 'contact-loading-spinner'
    });
    
    // 加载文本
    const loadingText = loadingIndicator.createEl('div', {
      cls: 'contact-loading-text',
      text: '正在加载联系人数据...'
    });
    
    console.log('加载状态已显示');
  }
  
  // 隐藏加载状态
  private hideLoadingState() {
    if (!this.resultContainer) return;
    
    const loadingState = this.resultContainer.querySelector('.contact-loading-state');
    if (loadingState) {
      // 添加淡出动画
      loadingState.addClass('fading-out');
      
      // 动画完成后移除元素
      setTimeout(() => {
        loadingState.remove();
      }, 200);
      
      console.log('加载状态已隐藏');
    }
  }
  
  // 显示错误状态
  private showErrorState(error: any) {
    if (!this.resultContainer) return;
    
    // 清空容器
    this.resultContainer.empty();
    
    // 创建错误容器
    const errorContainer = this.resultContainer.createEl('div', {
      cls: 'contact-error-state'
    });
    
    // 错误图标
    const errorIcon = errorContainer.createEl('div', {
      cls: 'contact-error-icon'
    });
    errorIcon.innerHTML = `
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="10"/>
        <line x1="15" y1="9" x2="9" y2="15"/>
        <line x1="9" y1="9" x2="15" y2="15"/>
      </svg>
    `;
    
    // 错误信息
    const errorMessage = errorContainer.createEl('div', {
      cls: 'contact-error-message'
    });
    
    const errorTitle = errorMessage.createEl('h3', {
      text: '数据加载失败'
    });
    
    const errorDesc = errorMessage.createEl('p', {
      text: '无法加载联系人数据，请检查网络连接或稍后重试。'
    });
    
    // 重试按钮
    const retryButton = errorMessage.createEl('button', {
      cls: 'contact-error-retry',
      text: '重试'
    });
    
    retryButton.addEventListener('click', () => {
      // 重新加载模态框
      this.close();
      setTimeout(() => {
        this.open();
      }, 100);
    });
    
    console.error('显示错误状态:', error);
  }

  // 设置最终交互（焦点和下拉菜单）
  private setupFinalInteractions() {
    console.log('=== 设置最终交互 ===');
    
    if (this.searchInput && this.inputContainer) {
      // 搜索容器动画已经在onOpen中提前触发了，这里只处理焦点设置
      // 等待容器动画完成后设置焦点 (动画时间: 250ms + 50ms缓冲)
      setTimeout(() => {
        console.log('搜索输入容器动画完成，开始聚焦搜索框');
        this.searchInput.focus();
        this.isSearchFocused = true;
        
        // 使用防抖版本的showIntelligentNextLevel，避免重复调用
        requestAnimationFrame(() => {
          console.log('焦点设置完成，显示智能下拉菜单');
          this.isInitializing = false; // 标记初始化完成
          this.debouncedShowIntelligentNextLevel('setup');
          console.log('=== Modal 初始化完成 ===');
        });
        
      }, 550); // 等待搜索输入容器动画完成（CSS transition是0.5s）
    }
  }
  
  // 创建搜索栏 - 集成高级搜索功能
  createSearchBar(container: HTMLElement) {
    this.searchContainer = container.createEl('div', { cls: 'search-container' });
    
    // 创建高级搜索输入框
    this.createAdvancedSearchInput(this.searchContainer);
  }
  
  // 创建添加联系人按钮
  createAddContactButton(container: HTMLElement) {
    // 将按钮添加到模态框容器（modalEl）而不是内容容器（contentEl）
    // 这样按钮就成为 modal-content 的兄弟元素
    const addButton = this.modalEl.createEl('button', {
      cls: 'contact-add-button-floating',
      attr: {
        'aria-label': '添加新联系人',
        'title': '添加新联系人'
      }
    });

    addButton.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="12" y1="5" x2="12" y2="19"/>
        <line x1="5" y1="12" x2="19" y2="12"/>
      </svg>
    `;

    addButton.addEventListener('click', () => {
      this.plugin.openAddContactModal();
    });
  }
  
  // 创建多级搜索输入框
  createAdvancedSearchInput(container: HTMLElement) {
    // 创建多级搜索容器
    this.multiLevelSearchContainer = container.createEl('div', {cls: 'contact-obcm-search--container'});

    // 创建搜索包装器
    const searchWrapper = this.multiLevelSearchContainer.createEl('div', {cls: 'contact-obcm-search--search-wrapper'});

    // 创建内部容器，保持样式控制但不作为父级
    const innerContainer = searchWrapper.createEl('div', {cls: 'contact-obcm-search--inner-container'});
    this.innerContainer = innerContainer; // 保存引用用于滚动动画

    // 创建输入容器，直接作为searchWrapper的子元素
    this.inputContainer = searchWrapper.createEl('div', {cls: 'contact-obcm-search--input-container contact-obcm-search--initializing'});

    // 创建标签和输入包装器
    const tagsInputWrapper = this.inputContainer.createEl('div', {cls: 'contact-obcm-search--tags-input-wrapper'});

    // 创建标签容器
    this.tagsContainer = tagsInputWrapper.createEl('div', {cls: 'contact-obcm-search--tags-container'});

    // 创建搜索输入框
    this.searchInput = tagsInputWrapper.createEl('input', {
      type: 'text',
      placeholder: '搜索联系人...',
      cls: 'contact-obcm-search--search-input',
      attr: {
        'id': 'contact-search',
        'autocomplete': 'off'
      }
    });

    // 创建右侧搜索图标（紫色圆形背景，白色图标）
    const rightSearchIcon = this.inputContainer.createEl('div', {cls: 'contact-obcm-search--right-icon'});
    rightSearchIcon.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; fill: none; stroke: currentcolor; stroke-width: 4; overflow: visible;"><path fill="none" d="M13 24a11 11 0 1 0 0-22 11 11 0 0 0 0 22zm8-3 9 9"></path></svg>
    `;

    // 添加点击回到顶部的交互功能 (Airbnb风格)
    this.inputContainer.addEventListener('click', (e) => {
      e.preventDefault();
      // 检查是否在滚动状态（紧凑模式）
      if (this.inputContainer.classList.contains('contact-obcm-search--scrolled')) {
        // 1. 记住当前位置
        const currentPosition = this.inputContainer.getBoundingClientRect();
        
        // 2. 恢复搜索框状态
        this.inputContainer.classList.remove('contact-obcm-search--scrolled');
        
        // 3. 设置过渡起点（小搜索框位置）
        this.inputContainer.style.position = 'fixed';
        this.inputContainer.style.top = `${currentPosition.top}px`;
        this.inputContainer.style.left = `${currentPosition.left}px`;
        this.inputContainer.style.transform = 'scale(0.35)';
        
        // 4. 强制重绘
        this.inputContainer.offsetHeight;
        
        // 5. 设置过渡终点（大搜索框位置）
        this.inputContainer.style.position = 'relative';
        this.inputContainer.style.top = '';
        this.inputContainer.style.left = '';
        this.inputContainer.style.transform = '';
        
        // 6. 显示搜索结果
        if (this.resultsContainer) {
          this.resultsContainer.style.display = '';
          this.resultsContainer.classList.remove('contact-obcm-search--scroll-hiding');
          this.resultsContainer.classList.add('contact-obcm-search--scroll-showing');
          this.isResultsHiddenByScroll = false;
        }
        
        // 7. 滚动到顶部
        this.contentEl.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
        
        // 8. 聚焦搜索框
        if (this.searchInput) {
          this.searchInput.focus();
        }
      }
    });

    // Airbnb风格悬停效果 - 添加微妙的交互反馈
    this.inputContainer.addEventListener('mouseenter', () => {
      if (this.inputContainer.classList.contains('contact-obcm-search--scrolled')) {
        this.inputContainer.style.cursor = 'pointer';
        // 添加微妙的悬停反馈
        this.inputContainer.style.transition = 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
      }
    });

    this.inputContainer.addEventListener('mouseleave', () => {
      this.inputContainer.style.cursor = '';
      // 恢复默认状态
      this.inputContainer.style.transition = 'all 0.6s cubic-bezier(0.16, 1, 0.3, 1)';
    });



    // 创建结果容器，直接作为searchWrapper的子元素
    this.resultsContainer = searchWrapper.createEl('div', {cls: 'contact-obcm-search--results-container'});
    this.resultsWrapper = this.resultsContainer.createEl('div', {cls: 'contact-obcm-search--results-wrapper'});

    // 创建搜索结果
    this.searchResults = this.resultsWrapper.createEl('div', {
      cls: 'contact-obcm-search--search-results contact-obcm-search--hidden'
    });

    // 创建级别头部
    const levelHeader = this.searchResults.createEl('div', {cls: 'contact-obcm-search--level-header'});
    this.levelTitle = levelHeader.createEl('span', {
      cls: 'contact-obcm-search--level-title',
      text: 'Level 1 - 搜索联系人'
    });
    this.levelIndicator = levelHeader.createEl('span', {cls: 'contact-obcm-search--level-indicator'});

    // 创建结果列表
    this.resultsList = this.searchResults.createEl('ul', {cls: 'contact-obcm-search--results-list'});

    // 创建传统搜索下拉菜单（用于简单搜索）
    this.searchResultsDropdown = this.resultsContainer.createEl('div', {
      cls: 'search-results-dropdown hidden'
    });

    // 设置多级搜索功能
    this.setupMultiLevelSearch();

    // 不在这里设置默认标签，而是在 restoreSearchState 中处理
  }
  

  
  // 从联系人信息中提取机构和部门信息
  async extractOrganizationsAndDepartments(): Promise<Record<string, string[]>> {
    try {
      if (this.useWorkerOptimization && this.workerInitialized) {
        // 使用Worker获取组织结构
        return await contactWorkerManager.getOrganizations();
      }
    } catch (error) {
      console.error('Worker获取组织结构失败:', error);
    }
    
    // 降级到传统方式
    const orgDeptMap: Record<string, Set<string>> = {};
    
    this.contactInfos.forEach(contact => {
      const org = contact.branch || '未知机构';
      const dept = contact.department || '未知部门';
      
      if (!orgDeptMap[org]) {
        orgDeptMap[org] = new Set();
      }
      orgDeptMap[org].add(dept);
    });
    
    // 转换为普通对象
    const result: Record<string, string[]> = {};
    Object.keys(orgDeptMap).forEach(org => {
      result[org] = Array.from(orgDeptMap[org]);
    });
    
    return result;
  }

  // 处理滚动动画效果 (Airbnb风格)
  private handleScrollAnimation() {
    if (!this.innerContainer || !this.resultContainer || !this.searchContainer || !this.inputContainer) return;
    
    // 获取滚动位置
    const scrollTop = this.contentEl.scrollTop;
    
    // 设置270px的滚动阈值
    const scrollThreshold = 270;
    
    // 根据滚动位置决定是否显示紧凑模式
    if (scrollTop > scrollThreshold) {
      // 添加滚动状态类
      if (!this.inputContainer.classList.contains('contact-obcm-search--scrolled')) {
        this.inputContainer.classList.add('contact-obcm-search--scrolled');
        
        // 隐藏搜索结果
        if (this.resultsContainer && !this.isResultsHiddenByScroll) {
          this.isResultsHiddenByScroll = true;
          this.resultsContainer.classList.remove('contact-obcm-search--scroll-showing');
          this.resultsContainer.classList.add('contact-obcm-search--scroll-hiding');
        }
      }
    } else {
      // 恢复正常状态
      if (this.inputContainer.classList.contains('contact-obcm-search--scrolled')) {
        this.inputContainer.classList.remove('contact-obcm-search--scrolled');
        
        // 显示搜索结果
        if (this.resultsContainer && this.isResultsHiddenByScroll) {
          this.isResultsHiddenByScroll = false;
          this.resultsContainer.classList.remove('contact-obcm-search--scroll-hiding');
          this.resultsContainer.classList.add('contact-obcm-search--scroll-showing');
        }
      }
    }
  }


  // 渲染联系人 - 基本框架
  renderContacts() {
    if (!this.resultContainer) {
      console.error('resultContainer 未初始化');
      return;
    }
    
    // 取消之前的渲染
    this.cancelCurrentRender();
    
    this.resultContainer.empty();
    
    // 安全地获取搜索关键词
    const query = this.searchInput?.value?.trim() || '';
    
    console.log('renderContacts 开始渲染，query:', query);
    console.log('搜索输入框值:', this.searchInput?.value);
    console.log('debouncedQuery值:', this.debouncedQuery);
    console.log('selectedActions:', this.selectedActions);
    
    // 根据选中的标签筛选联系人
    this.applyTagFilters();
    
    // 创建总容器
    const contactsContainer = this.resultContainer.createEl("div", { cls: "contacts-container" });
    
    // 只有在未输入搜索内容时才显示最近搜索
    console.log('准备调用 renderRecentSearches，query:', query);
    this.renderRecentSearches(contactsContainer, query);
    
    // 搜索结果为空时的提示
    if (this.filteredContacts.length === 0) {
      // 修改为显示提示信息和新增联系人按钮
      // 将提示移动到contactsContainer内作为第一个子元素
      const notFoundMessage = contactsContainer.createEl("p", { 
        text: "未找到匹配的联系人",
        cls: "no-contacts-message" 
      });
      
      // 确保这个元素是contactsContainer的第一个子元素
      if (contactsContainer.firstChild !== notFoundMessage) {
        contactsContainer.insertBefore(notFoundMessage, contactsContainer.firstChild);
      }
      
      // 创建联系人列表容器
      const contactList = contactsContainer.createEl("div", { cls: "contact-list all-contacts" });
      
      // 添加新建联系人按钮
      this.createNewContactCard(contactList);
      
      return;
    }
    
    // 如果输入了搜索内容，不按部门分组，直接显示搜索结果
    if (query) {
      // 检查是否只有"全部"标签且结果较多，提示用户选择具体机构
      const selectedOrgs = this.selectedActions.filter(action => action.type === 'organization');
      const specificOrgs = selectedOrgs.filter(org => org.id !== 'all');
      const hasOnlyAllOrg = selectedOrgs.length > 0 && specificOrgs.length === 0;
      
      // 创建搜索结果标题
      const titleElement = contactsContainer.createEl("h3", { 
        text: `搜索结果 (${this.filteredContacts.length})`, 
        cls: "search-results-title" 
      });
      
      // 如果只有"全部"标签且结果较多（>10个），添加提示
      if (hasOnlyAllOrg && this.filteredContacts.length > 10) {
        const hintElement = contactsContainer.createEl("div", { 
          cls: "search-hint-container"
        });
        hintElement.innerHTML = `
          <div class="search-hint">
            💡 找到 ${this.filteredContacts.length} 个结果。<span class="hint-link">选择具体机构</span>以获得更精确的搜索结果
          </div>
        `;
        
        // 添加点击事件，显示机构选择
        const hintLink = hintElement.querySelector('.hint-link') as HTMLElement;
        if (hintLink) {
          hintLink.addEventListener('click', () => {
            // 聚焦搜索框并显示机构选择
            this.searchInput.focus();
            this.isSearchFocused = true;
            const orgOptions = this.getOrganizationOptions('');
            this.renderMultiLevelResults(orgOptions);
            this.updateResultsContainerWidth();
            this.showMultiLevelResults();
          });
        }
      }
      
      // 创建联系人列表容器
      const contactList = contactsContainer.createEl("div", { cls: "contact-list all-contacts" });
      
      // 添加新建联系人按钮
      this.createNewContactCard(contactList);
      
      // 使用虚拟滚动优化大量联系人卡片的渲染
      this.renderContactsWithVirtualScrolling(contactList, this.filteredContacts, query);
      
      return;
    }
    
    // 按部门分组
    this.renderContactsByDepartment(contactsContainer);
  }
  
  // 取消当前渲染
  private cancelCurrentRender() {
    if (this.renderCancelToken) {
      cancelAnimationFrame(this.renderCancelToken);
      this.renderCancelToken = null;
    }
    this.isRendering = false;
    this.renderQueue = [];
  }

  // 使用虚拟滚动渲染联系人
  private renderContactsWithVirtualScrolling(container: HTMLElement, contacts: ContactInfo[], query: string = '') {
    if (contacts.length === 0) return;
    
    // 如果联系人数量较少（<50），使用传统渲染
    if (contacts.length < 50) {
      this.renderContactsBatch(container, contacts);
      return;
    }
    
    // 初始化优化渲染器
    this.initOptimizedRenderer(container);
    
    // 准备数据格式
    const groupedContacts = {
      'search-results': {
        contacts: contacts,
        count: contacts.length
      }
    };
    
    // 使用虚拟滚动渲染
    if (this.optimizedRenderer) {
      this.optimizedRenderer.renderContacts(groupedContacts, query);
    } else {
      // 降级到传统渲染
      this.renderContactsBatch(container, contacts);
    }
  }

  // 分批渲染联系人卡片（传统方式）
  private renderContactsBatch(container: HTMLElement, contacts: ContactInfo[]) {
    if (contacts.length === 0) return;
    
    // 如果联系人数量较少，直接渲染
    if (contacts.length <= this.renderBatchSize) {
      contacts.forEach(contact => {
        this.createContactCard(container, contact);
      });
      return;
    }
    
    // 大量联系人使用分批渲染
    this.renderQueue = [...contacts];
    this.isRendering = true;
    
    // 先渲染首批联系人（立即可见）
    const firstBatch = this.renderQueue.splice(0, this.renderBatchSize);
    firstBatch.forEach(contact => {
      this.createContactCard(container, contact);
    });
    
    // 继续分批渲染剩余联系人
    this.scheduleNextBatch(container);
  }

  // 调度下一批渲染
  private scheduleNextBatch(container: HTMLElement) {
    if (!this.isRendering || this.renderQueue.length === 0) {
      this.isRendering = false;
      return;
    }
    
    this.renderCancelToken = requestAnimationFrame(() => {
      const batch = this.renderQueue.splice(0, this.renderBatchSize);
      
      // 渲染这一批联系人
      batch.forEach(contact => {
        this.createContactCard(container, contact);
      });
      
      // 继续下一批
      this.scheduleNextBatch(container);
    });
  }

  // 提取常用的联系人筛选逻辑
  filterContacts() {
    // 直接调用渲染方法，它会自动应用标签筛选
    this.renderContacts();
  }

  // 统一的联系人搜索匹配逻辑（用于主窗口和下拉菜单）
  searchMatchesContact(contact: ContactInfo, searchQuery: string): boolean {
    if (!searchQuery) return true;
    
    const isPhoneQuery = /^6\d*$/.test(searchQuery);
    
    if (isPhoneQuery) {
      return contact.phone && contact.phone.includes(searchQuery);
    }
    
    // 对所有字段进行搜索匹配
    return Object.entries(contact).some(([key, value]) => {
      if (key === 'content' || key === 'path') return false;
      return value && String(value).toLowerCase().includes(searchQuery);
    });
  }

  // 初始化优化渲染器
  private initOptimizedRenderer(container: HTMLElement) {
    if (!this.optimizedRenderer) {
      try {
        const { OptimizedContactRenderer } = require('./optimized-renderer');
        this.optimizedRenderer = new OptimizedContactRenderer(container);
      } catch (error) {
        console.warn('OptimizedContactRenderer 初始化失败，使用传统渲染:', error);
        this.optimizedRenderer = null;
      }
    }
  }
  
  // 修复内容下移问题
  fixContentShift() {
    // 不再直接在这里添加样式，样式已移至styles.css文件
    // 如果需要，可以在这里添加临时的样式修复
    const styleEl = document.createElement('style');
    styleEl.id = 'contact-modal-fix';
    styleEl.innerHTML = `
      /* 空的样式表，仅用于临时修复 */
      /* 所有样式已移至styles.css */
    `;
    document.head.appendChild(styleEl);
  }


  
  // 设置草稿保存事件监听器
  setupDraftSavedListener() {
    // 统一的缓存刷新和重新渲染逻辑
    const refreshAndRender = async (reason: string) => {
      try {
        console.log(`开始${reason}缓存刷新...`);
        
        // 强制刷新联系人缓存
        await this.plugin.refreshContactCache();
        console.log(`${reason}缓存刷新完成，文件数量:`, this.plugin.contactCache.files.length);
        
        // 重新初始化缓存数据
        this.initCacheData(this.plugin);
        console.log(`${reason}初始化缓存数据完成，联系人数量:`, this.contactInfos.length);
        
        // 重新初始化Worker和组织结构
        await this.initializeWorker();
        
        // 重新渲染界面
        this.renderContacts();
        console.log(`${reason}界面渲染完成`);
      } catch (error) {
        console.error(`${reason}后刷新缓存失败:`, error);
        this.renderContacts(); // 即使刷新失败也要重新渲染
      }
    };
    
    const handleDraftSaved = (event: CustomEvent) => {
      // 草稿保存只需重新渲染，不需要刷新缓存
      this.renderContacts();
    };
    
    const handleDraftConverted = async (event: CustomEvent) => {
      console.log('🔥 [ContactModal] 处理草稿转换事件:', event.detail);
      
      const { draftId, contactPath } = event.detail;
      
      if (draftId && contactPath) {
        console.log('🔥 [ContactModal] 开始就地转换草稿卡片');
        console.log('🔥 [ContactModal] 草稿ID:', draftId);
        console.log('🔥 [ContactModal] 联系人路径:', contactPath);
        
        // 🔥 关键：执行就地转换而不是完全刷新
        await this.convertDraftCardInPlace(draftId, contactPath);
      } else {
        console.log('🔥 [ContactModal] 缺少转换信息，执行完全刷新');
        await refreshAndRender('草稿转换');
      }
    };

    const handleContactCreated = async (event: CustomEvent) => {
      console.log('🆕 收到contactCreated事件:', event.detail);
      
      // 完全清空所有搜索状态，确保显示最近联系
      if (this.searchInput) {
        this.searchInput.value = '';
      }
      this.debouncedQuery = '';
      
      // 清空多级搜索的选中标签
      this.selectedActions = [];
      
      // 隐藏搜索下拉菜单和多级搜索结果
      this.hideSearchDropdown();
      this.hideMultiLevelResults();
      
      // 清空标签容器（如果有的话）
      if (this.tagsContainer) {
        this.tagsContainer.empty();
      }
      
      console.log('✅ 已完全清空所有搜索状态，准备显示最近联系');
      
      // 延迟处理，确保addRecentSearch已经完成
      setTimeout(async () => {
        console.log('🔄 开始刷新联系人缓存和界面');
        
        // 验证最近搜索状态
        const recentSearches = this.plugin.getRecentSearches();
        console.log('📋 当前最近搜索列表:', recentSearches);
        
        const contactPath = event.detail?.contactPath;
        if (contactPath) {
          console.log('🔍 开始验证新创建的联系人:', contactPath);
          
          // 检查最近搜索中是否有对应记录
          const recentSearchItem = recentSearches.find(item => item.id === contactPath);
          if (recentSearchItem) {
            console.log('✅ 新创建的联系人已在最近搜索中找到:', recentSearchItem.name);
            console.log('📝 最近搜索记录详情:', recentSearchItem);
          } else {
            console.error('❌ 新创建的联系人未在最近搜索中找到:', contactPath);
            console.error('🔍 期望路径:', contactPath);
            console.error('📋 实际最近搜索列表:', recentSearches.map(item => ({ id: item.id, name: item.name })));
            
            // 尝试手动重新添加到最近搜索
            console.log('🔧 尝试手动重新添加到最近搜索...');
            // 这里可以添加手动重新添加的逻辑
          }
        }
        
        // 先刷新缓存，再重新渲染界面
        await refreshAndRender('新建联系人');
        
        // 强制重新渲染，确保显示最新的联系人
        console.log('🎨 强制重新渲染界面...');
        this.renderContacts();
        
        console.log('✅ 联系人创建处理完成');
      }, 150); // 稍微增加延迟，确保所有操作都完成
    };

    const handleContactDeleted = async (event: CustomEvent) => {
      console.log('收到contactDeleted事件:', event.detail);
      const deletedFilePath = event.detail?.filePath;
      
      if (deletedFilePath) {
        console.log('联系人文件被删除:', deletedFilePath);
        
        // 立即从界面中移除对应的卡片
        this.removeContactCardFromUI(deletedFilePath);
        
        // 刷新缓存和重新渲染
        await refreshAndRender('联系人删除');
        
        console.log('联系人删除处理完成');
      }
    };
    
    // 🔥 关键修复：监听来自主插件的统一刷新事件，而不是原始事件
    const handleContactModalRefresh = async (event: CustomEvent) => {
      console.log('🔥 [ContactModal] 收到contactModalRefresh事件:', event.detail);
      
      const { contactPath, eventType } = event.detail;
      
      switch (eventType) {
        case 'created':
          console.log('🔥 [ContactModal] 处理联系人创建刷新');
          await handleContactCreated({ detail: { contactPath } } as CustomEvent);
          break;
          
        case 'converted':
          console.log('🔥 [ContactModal] 处理草稿转换刷新');
          await handleDraftConverted({ detail: { contactPath } } as CustomEvent);
          break;
          
        case 'draftSaved':
          console.log('🔥 [ContactModal] 处理草稿保存刷新');
          handleDraftSaved({ detail: {} } as CustomEvent);
          break;
          
        default:
          console.log('🔥 [ContactModal] 处理通用刷新');
          await refreshAndRender('通用刷新');
          break;
      }
    };

    // 🔥 关键修复：只监听统一的刷新事件和删除事件
    document.addEventListener('contactModalRefresh', handleContactModalRefresh as EventListener);
    document.addEventListener('contactDeleted', handleContactDeleted as EventListener);
    
    // 确保在模态框关闭时移除监听器
    const originalClose = this.close.bind(this);
    this.close = () => {
      document.removeEventListener('contactModalRefresh', handleContactModalRefresh as EventListener);
      document.removeEventListener('contactDeleted', handleContactDeleted as EventListener);
      originalClose();
    };
  }
  
  onClose() {
    // 保存当前搜索状态
    this.saveSearchState();
    
    // 取消渲染并清理性能相关资源
    this.cancelCurrentRender();
    this.visibleContactsCache.clear();
    
    // 清理优化渲染器
    if (this.optimizedRenderer) {
      this.optimizedRenderer.destroy();
      this.optimizedRenderer = null;
    }
    
    // 清理Worker资源
    if (this.useWorkerOptimization) {
      // 不销毁Worker，因为它是单例，其他地方可能还在使用
      this.workerInitialized = false;
    }
    
    // 清理进度指示器和通知已删除
    
    // 清理视口观察器
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
      this.intersectionObserver = null;
    }
    
    // 清理所有定时器
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
      this.searchDebounceTimer = null;
    }
    
    if (this.saveStateDebounceTimer) {
      clearTimeout(this.saveStateDebounceTimer);
      this.saveStateDebounceTimer = null;
    }
    
    if (this.showMenuDebounceTimer) {
      clearTimeout(this.showMenuDebounceTimer);
      this.showMenuDebounceTimer = null;
    }
    
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }
    
    const { contentEl } = this;
    contentEl.empty();
    
    // 安全地恢复body样式，保护用户的自定义主题颜色
    this.safeRestoreBodyStyle();
    
    // 修复任何样式问题
    this.plugin.fixStyleIssues();
  }
  
  // 安全地恢复body样式，保护用户自定义主题颜色
  private safeRestoreBodyStyle() {
    // 如果有保存的样式，采用智能恢复
    if (this.modalBodyStyle) {
      // 获取当前样式
      const currentStyle = document.body.getAttribute('style') || '';
      
      // 定义插件可能添加的样式属性（需要移除的）
      const pluginStyleProps = [
        'position',
        'transform',
        'top',
        'left',
        'margin',
        'padding',
        'zoom',
        'transform-origin'
      ];
      
      // 解析保存的原始样式
      const originalDeclarations = this.modalBodyStyle.split(';').filter(decl => decl.trim());
      const currentDeclarations = currentStyle.split(';').filter(decl => decl.trim());
      
      // 从当前样式中提取用户可能添加的新样式（如自定义主题颜色）
      const userAddedStyles: string[] = [];
      const originalStyleMap = new Map<string, string>();
      
      // 建立原始样式映射
      originalDeclarations.forEach(decl => {
        const [prop, value] = decl.split(':').map(s => s.trim());
        if (prop && value) {
          originalStyleMap.set(prop.toLowerCase(), decl.trim());
        }
      });
      
      // 找出用户可能新增的样式
      currentDeclarations.forEach(decl => {
        const prop = decl.split(':')[0]?.trim().toLowerCase();
        if (prop) {
          // 如果是插件相关属性，跳过
          if (pluginStyleProps.includes(prop)) {
            return;
          }
          // 如果原始样式中没有这个属性，说明是用户新增的
          if (!originalStyleMap.has(prop)) {
            userAddedStyles.push(decl.trim());
          }
        }
      });
      
      // 合并原始样式和用户新增样式
      const finalStyles = [...originalDeclarations, ...userAddedStyles];
      
      if (finalStyles.length > 0) {
        document.body.setAttribute('style', finalStyles.join('; ') + ';');
    } else {
      document.body.removeAttribute('style');
    }
    } else {
      // 如果没有保存的样式，使用安全的清理方法
      const currentStyle = document.body.getAttribute('style') || '';
      
      // 定义插件可能添加的样式属性（需要移除的）
      const pluginStyleProps = [
        'position',
        'transform',
        'top',
        'left',
        'margin',
        'padding',
        'zoom',
        'transform-origin'
      ];
      
      // 解析当前样式
      const styleDeclarations = currentStyle.split(';').filter(decl => decl.trim());
      const filteredStyles: string[] = [];
      
      styleDeclarations.forEach(decl => {
        const prop = decl.split(':')[0]?.trim().toLowerCase();
        // 只保留非插件相关的样式
        if (prop && !pluginStyleProps.includes(prop)) {
          filteredStyles.push(decl.trim());
        }
      });
      
      // 重新设置过滤后的样式
      if (filteredStyles.length > 0) {
        document.body.setAttribute('style', filteredStyles.join('; ') + ';');
      } else {
        document.body.removeAttribute('style');
      }
    }
  }
  
  // 快速辅助方法

  // 检查是否匹配机构筛选
  matchesOrgFilter(contact: ContactInfo) {
    const selectedOrgs = this.selectedActions.filter(action => action.type === 'organization');
    if (selectedOrgs.length === 0) return true;
    
    // 如果选择了"全部"，所有联系人都匹配
    const hasAllOption = selectedOrgs.some(org => org.id === 'all');
    if (hasAllOption) return true;
    
    // 否则检查是否匹配具体的机构
    return selectedOrgs.some(org => contact.branch === org.id);
  }
  
  // 检查是否匹配部门筛选
  matchesDeptFilter(contact: ContactInfo) {
    const selectedDepts = this.selectedActions.filter(action => action.type === 'department');
    if (selectedDepts.length === 0) return true;
    
    return selectedDepts.some(dept => {
      if (dept.id === '未分类') {
        return !contact.department || !contact.department.trim();
      } else {
        return contact.department === dept.id;
      }
    });
  }
  
  // 检查是否匹配角色筛选
  matchesRoleFilter(contact: ContactInfo) {
    const selectedRoles = this.selectedActions.filter(action => action.type === 'role');
    if (selectedRoles.length === 0) return true;
    
    return selectedRoles.some(role => contact.title === role.id);
  }
  
  // 检查是否匹配搜索关键词
  matchesSearchQuery(contact: ContactInfo, query: string, isPhoneQuery: boolean) {
    if (!query) return true;
    
    // 如果是6开头的数字，只搜索电话号码字段
    if (isPhoneQuery) {
      return contact.phone && contact.phone.includes(query);
    }
    
    // 普通搜索，排除隐藏字段
    return Object.entries(contact).some(([key, value]) => {
      // 跳过content和path字段
      if (key === 'content' || key === 'path') return false;
      return value && String(value).toLowerCase().includes(query);
    });
  }

  // 以下是界面渲染相关的基本方法框架
  
  // 按部门分组显示联系人
  renderContactsByDepartment(container: HTMLElement) {
    // 从selectedActions获取筛选信息
    const selectedOrgs = this.selectedActions.filter(action => action.type === 'organization');
    const selectedDepts = this.selectedActions.filter(action => action.type === 'department');
    
    // 如果选择了特定部门，则简单列出联系人
    if (selectedDepts.length > 0) {
      const deptName = selectedDepts[0].label;
      const deptTitle = container.createEl("h3", { 
        text: `${deptName} (${this.filteredContacts.length})`, 
        cls: "department-title" 
      });
      
      const contactList = container.createEl("div", { cls: "contact-list" });
      
      // 添加新建联系人按钮
      this.createNewContactCard(contactList);
      
      // 使用虚拟滚动优化大量联系人卡片的渲染
      this.renderContactsWithVirtualScrolling(contactList, this.filteredContacts);
      
      return;
    }
    
    // 否则，按机构和部门分组显示
    // 创建分组映射：机构 -> 部门 -> 联系人
    const groupedContacts: Record<string, Record<string, ContactInfo[]>> = {};
    
    // 首先，确保所有联系人都有机构和部门
    this.filteredContacts.forEach(contact => {
      // 没有设置分行的联系人归为"未分类"
      const branch = contact.branch || "未分类";
      // 没有设置部门的联系人归为"未分类部门"
      const department = contact.department || "未分类部门";
      
      // 确保机构和部门的数据结构存在
      if (!groupedContacts[branch]) {
        groupedContacts[branch] = {};
      }
      
      if (!groupedContacts[branch][department]) {
        groupedContacts[branch][department] = [];
      }
      
      // 添加联系人到对应部门
      groupedContacts[branch][department].push(contact);
    });
    
    // 创建一个有序的机构名称列表
    const branches = Object.keys(groupedContacts).sort();
    
    // 检查是否选择了特定机构（但不是"全部"）
    const selectedSpecificOrg = selectedOrgs.find(org => org.id !== 'all');
    
    // 如果筛选了特定机构（非"全部"），只显示该机构
    if (selectedSpecificOrg) {
      const orgName = selectedSpecificOrg.id;
      // 确保筛选的机构存在，如果不存在，显示"未找到"消息
      if (!groupedContacts[orgName]) {
        container.createEl("p", { 
          text: `未找到"${orgName}"机构的联系人` 
        });
        return;
      }
      
      // 获取该机构的所有部门
      const departments = Object.keys(groupedContacts[orgName]);
      
      // 根据优先列表排序部门
      departments.sort((a, b) => {
        const priorityA = this.priorityDepts.indexOf(a);
        const priorityB = this.priorityDepts.indexOf(b);
        
        // 如果两个部门都在优先列表中，按优先级排序
        if (priorityA >= 0 && priorityB >= 0) {
          return priorityA - priorityB;
        }
        
        // 如果只有一个部门在优先列表中，它优先
        if (priorityA >= 0) return -1;
        if (priorityB >= 0) return 1;
        
        // 否则按字母顺序排序
        return a.localeCompare(b);
      });
      
              // 创建机构容器
        const branchContainer = container.createEl("div", { cls: "branch-container" });
        
        // 计算统计数据
        const totalDepartments = departments.length;
        const totalContacts = departments.reduce((sum, dept) => 
          sum + groupedContacts[orgName][dept].length, 0);
        
        // 创建机构标题容器
        const branchTitleContainer = branchContainer.createEl("div", { cls: "branch-title-container" });
        
        // 创建可折叠的机构标题
        const branchTitle = branchTitleContainer.createEl("h3", { 
          cls: "branch-title collapsible-title",
          attr: { 'data-collapsed': 'false' }
        });
        
        // 添加折叠箭头
        const collapseArrow = branchTitle.createEl("span", { cls: "collapse-arrow" });
        collapseArrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
          <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
        </svg>`;
        
        // 添加机构名称，如果为空则显示"未设置所属机构"
        const orgDisplayName = orgName && orgName.trim() ? orgName : "未设置所属机构";
        const branchNameEl = branchTitle.createEl("span", { 
          text: orgDisplayName, 
          cls: `branch-name ${!orgName || !orgName.trim() ? 'branch-name-empty' : ''}` 
        });
        
        // 创建统计信息容器
        const statsContainer = branchTitleContainer.createEl("div", { cls: "branch-stats" });
        
        // 添加部门数量图标
        const deptStats = statsContainer.createEl("span", { cls: "stat-item" });
        deptStats.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          <span class="stat-number">${totalDepartments}</span>
        `;
        
        // 添加人员数量图标
        const contactStats = statsContainer.createEl("span", { cls: "stat-item" });
        contactStats.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          <span class="stat-number">${totalContacts}</span>
        `;
        
        // 创建可折叠的内容容器
        const branchContent = branchContainer.createEl("div", { cls: "branch-content" });
      
      // 为每个部门创建一个联系人列表
      departments.forEach(department => {
        const contacts = groupedContacts[orgName][department];
        if (contacts.length === 0) return;
        
          const deptContainer = branchContent.createEl("div", { cls: "department-container" });
        
        // 创建部门标题容器
        const deptTitleContainer = deptContainer.createEl("div", { cls: "department-title-container" });
        
        // 创建可折叠的部门标题
        const deptTitle = deptTitleContainer.createEl("h4", { 
          cls: "department-title collapsible-title",
          attr: { 'data-collapsed': 'false' }
        });
        
        // 添加折叠箭头
        const deptCollapseArrow = deptTitle.createEl("span", { cls: "collapse-arrow" });
        deptCollapseArrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
        </svg>`;
        
        // 添加部门名称，如果为空则显示"未设置所属部门"
        const departmentDisplayName = department && department.trim() ? department : "未设置所属部门";
        const departmentNameEl = deptTitle.createEl("span", { 
          text: departmentDisplayName, 
          cls: `department-name ${!department || !department.trim() ? 'department-name-empty' : ''}` 
        });
        
        // 创建统计信息容器
        const deptStatsContainer = deptTitleContainer.createEl("div", { cls: "department-stats" });
        
        // 添加人员数量图标
        const contactStats = deptStatsContainer.createEl("span", { cls: "stat-item" });
        contactStats.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
          <span class="stat-number">${contacts.length}</span>
        `;
        
        // 添加复制邮箱按钮
        const copyEmailBtn = deptTitleContainer.createEl("button", { 
          cls: "department-copy-email-btn",
          attr: { 
            'aria-label': '复制全部门邮箱地址',
            'title': '复制全部门邮箱地址',
            'data-department': department
          }
        });
        copyEmailBtn.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
            <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
          </svg>
        `;
        
        // 添加复制邮箱功能
        copyEmailBtn.addEventListener('click', (e) => {
          e.stopPropagation(); // 阻止触发折叠功能
          this.copyDepartmentEmails(contacts, copyEmailBtn);
        });
        
        // 创建部门联系人列表
        const contactList = deptContainer.createEl("div", { cls: "contact-list" });
        // 使用虚拟滚动优化大量联系人卡片的渲染
        this.renderContactsWithVirtualScrolling(contactList, contacts);
        
        // 添加部门折叠功能
        this.setupDepartmentCollapse(deptTitle, contactList);
        
        // 设置阴影检测功能
        this.setupStickyDetection(deptTitleContainer);
      });
        
        // 添加折叠功能
        this.setupBranchCollapse(branchTitle, branchContent);
    } else {
      // 如果没有筛选机构，按机构和部门分组显示所有联系人
      branches.forEach(branch => {
        const branchContainer = container.createEl("div", { cls: "branch-container" });
        
          // 计算该机构的统计数据
          const departments = Object.keys(groupedContacts[branch]);
          const totalDepartments = departments.length;
          const totalContacts = departments.reduce((sum, dept) => 
            sum + groupedContacts[branch][dept].length, 0);
          
          // 创建机构标题容器
          const branchTitleContainer = branchContainer.createEl("div", { cls: "branch-title-container" });
          
          // 创建可折叠的机构标题
          const branchTitleEl = branchTitleContainer.createEl("h3", { 
            cls: "branch-title collapsible-title",
            attr: { 'data-collapsed': 'false' }
          });
          
          // 添加折叠箭头
          const collapseArrow = branchTitleEl.createEl("span", { cls: "collapse-arrow" });
          collapseArrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
          </svg>`;
          
          // 添加机构名称，如果为空则显示"未设置所属机构"
          const branchDisplayName = branch && branch.trim() ? branch : "未设置所属机构";
          const branchNameEl = branchTitleEl.createEl("span", { 
            text: branchDisplayName, 
            cls: `branch-name ${!branch || !branch.trim() ? 'branch-name-empty' : ''}` 
          });
          
          // 创建统计信息容器
          const statsContainer = branchTitleContainer.createEl("div", { cls: "branch-stats" });
        
          // 添加部门数量图标
          const deptStats = statsContainer.createEl("span", { cls: "stat-item" });
          deptStats.innerHTML = `
            部门 · 
            <span class="stat-number">${totalDepartments}</span>
          `;
          
          // 添加人员数量图标
          const contactStats = statsContainer.createEl("span", { cls: "stat-item" });
          contactStats.innerHTML = `
            人数 · 
            <span class="stat-number">${totalContacts}</span>
          `;
        
                  // 创建可折叠的内容容器
          const branchContent = branchContainer.createEl("div", { cls: "branch-content" });
        
        // 根据优先列表排序部门
        departments.sort((a, b) => {
          const priorityA = this.priorityDepts.indexOf(a);
          const priorityB = this.priorityDepts.indexOf(b);
          
          // 如果两个部门都在优先列表中，按优先级排序
          if (priorityA >= 0 && priorityB >= 0) {
            return priorityA - priorityB;
          }
          
          // 如果只有一个部门在优先列表中，它优先
          if (priorityA >= 0) return -1;
          if (priorityB >= 0) return 1;
          
          // 否则按字母顺序排序
          return a.localeCompare(b);
        });
        
        // 为每个部门创建一个联系人列表
        departments.forEach(department => {
          const contacts = groupedContacts[branch][department];
          if (contacts.length === 0) return;
          
            const deptContainer = branchContent.createEl("div", { cls: "department-container" });
          
          // 创建部门标题容器
          const deptTitleContainer = deptContainer.createEl("div", { cls: "department-title-container" });
          
          // 创建可折叠的部门标题
          const deptTitle = deptTitleContainer.createEl("h4", { 
            cls: "department-title collapsible-title",
            attr: { 'data-collapsed': 'false' }
          });
          
          // 添加折叠箭头
          const deptCollapseArrow = deptTitle.createEl("span", { cls: "collapse-arrow" });
          deptCollapseArrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
          </svg>`;
          
          // 添加部门名称，如果为空则显示"未设置所属部门"
          const departmentDisplayName = department && department.trim() ? department : "未设置所属部门";
          const departmentNameEl = deptTitle.createEl("span", { 
            text: departmentDisplayName, 
            cls: `department-name ${!department || !department.trim() ? 'department-name-empty' : ''}` 
          });
          
          // 创建统计信息容器
          const deptStatsContainer = deptTitleContainer.createEl("div", { cls: "department-stats" });
          
          // 添加人员数量图标
          const contactStats = deptStatsContainer.createEl("span", { cls: "stat-item" });
          contactStats.innerHTML = `
             人数 · 
            <span class="stat-number">${contacts.length}</span>
          `;
          
          // 添加复制邮箱按钮
          const copyEmailBtn = deptTitleContainer.createEl("button", { 
            cls: "department-copy-email-btn",
            attr: { 
              'aria-label': '复制全部门邮箱地址',
              'title': '复制全部门邮箱地址',
              'data-department': department
            }
          });
          copyEmailBtn.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
              <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
            </svg>
          `;
          
          // 添加复制邮箱功能
          copyEmailBtn.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止触发折叠功能
            this.copyDepartmentEmails(contacts, copyEmailBtn);
          });
          
          // 创建部门联系人列表
          const contactList = deptContainer.createEl("div", { cls: "contact-list" });
          // 使用虚拟滚动优化大量联系人卡片的渲染
          this.renderContactsWithVirtualScrolling(contactList, contacts);
          
          // 添加部门折叠功能
          this.setupDepartmentCollapse(deptTitle, contactList);
          
          // 设置阴影检测功能
          this.setupStickyDetection(deptTitleContainer);
        });
          
          // 添加折叠功能
          this.setupBranchCollapse(branchTitleEl, branchContent);
      });
    }
  }
  
  // 渲染最近搜索记录
  renderRecentSearches(container: HTMLElement, query: string) {
    console.log('renderRecentSearches 被调用, query:', query);
    console.log('当前搜索输入框值:', this.searchInput?.value);
    console.log('当前debouncedQuery值:', this.debouncedQuery);
    
    // 如果有搜索关键词，不显示最近搜索
    if (query) {
      console.log('由于存在搜索关键词，跳过最近搜索渲染');
      return;
    }
    
    // 获取最近搜索记录
    const recentSearches = this.plugin.getRecentSearches();
    console.log('获取到的最近搜索记录数量:', recentSearches.length);
    console.log('最近搜索记录:', recentSearches);
    
    if (recentSearches.length === 0) {
      console.log('最近搜索记录为空，跳过渲染');
      return;
    }
    
    // 创建最近搜索容器
    const recentContainer = container.createEl("div", { cls: "recent-searches-container" });
    
    // 创建最近搜索标题容器
    const recentTitleContainer = recentContainer.createEl("div", { cls: "recent-searches-title-container" });
    
    // 创建可折叠的最近搜索标题
    const recentTitle = recentTitleContainer.createEl("h3", { 
      cls: "recent-searches-title collapsible-title",
      attr: { 'data-collapsed': 'false' }
    });
    
    // 添加折叠箭头
    const collapseArrow = recentTitle.createEl("span", { cls: "collapse-arrow" });
    collapseArrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
      <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
    </svg>`;
    
    // 添加标题名称
    recentTitle.createEl("span", { text: "最近联系", cls: "recent-searches-name" });
    
    // 创建统计信息容器
    const statsContainer = recentTitleContainer.createEl("div", { cls: "recent-searches-stats" });
    
    // 添加联系人数量统计
    const contactStats = statsContainer.createEl("span", { cls: "stat-item" });
    contactStats.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
        <circle cx="12" cy="7" r="4"></circle>
      </svg>
      <span class="stat-number">${recentSearches.length}</span>
    `;
    
    // 创建最近搜索联系人列表
    const recentList = recentContainer.createEl("div", { cls: "contact-list recent-searches" });
    
    // 查找这些联系人的详细信息，并添加到列表中
    recentSearches.forEach(recent => {
      try {
        if (recent.isDraft && recent.draftData) {
          // 处理草稿项目
          const draftContact = this.createDraftContactInfo(recent);
          this.createContactCard(recentList, draftContact, true);
        } else {
          // 处理普通联系人
          let contact = this.contactInfos.find(c => c.path === recent.id);
          if (!contact) {
            // 如果在缓存中找不到，创建一个基于最近搜索记录的临时联系人对象
            contact = this.createContactFromRecentSearch(recent);
            console.log('为最近搜索创建临时联系人对象:', contact.name, contact.path);
          }
          if (contact) {
            // 创建联系人卡片，标记为最近搜索
            this.createContactCard(recentList, contact, true);
          } else {
            console.warn('无法为最近搜索项创建联系人对象:', recent);
          }
        }
      } catch (error) {
        console.error('处理最近搜索项时出错:', recent, error);
      }
    });
    
    // 添加折叠功能
    this.setupRecentSearchesCollapse(recentTitle, recentList);
    
    // 设置阴影检测功能
    this.setupStickyDetection(recentTitleContainer);
  }
  
  // 从草稿数据创建ContactInfo对象
  createDraftContactInfo(recentItem: any): ContactInfo {
    const draftData = recentItem.draftData;
    return {
      name: draftData.name || '未命名草稿',
      path: recentItem.id,
      content: '',
      phone: draftData.phone || '',
      mobile: draftData.mobile || '',
      email: draftData.email || '',
      branch: draftData.organization || '',
      department: draftData.department || '',
      title: draftData.position || '',
      isDraft: true,
      draftData: draftData,
      draftDate: draftData.draftDate
    };
  }

  // 从最近搜索记录创建ContactInfo对象（用于新创建但缓存未更新的联系人）
  createContactFromRecentSearch(recentItem: any): ContactInfo {
    // 尝试从已刷新的缓存中找到完整的联系人信息
    const fullContact = this.contactInfos.find(c => c.path === recentItem.id);
    if (fullContact) {
      // 如果找到完整信息，直接返回
      console.log('在缓存中找到完整联系人信息:', fullContact.name);
      return fullContact;
    }
    
    // 如果缓存中没有，创建基于最近搜索记录的完整联系人对象
    // 使用recentItem中保存的所有信息来填充联系人对象
    console.log('为最近搜索创建临时联系人对象:', recentItem.name, recentItem.id);
    console.log('最近搜索记录详情:', recentItem);
    
    return {
      name: recentItem.name || '新联系人',
      path: recentItem.id, // 使用id作为主要路径标识符
      content: '',
      phone: recentItem.phone || '',
      mobile: recentItem.mobile || recentItem.phone || '',
      email: recentItem.email || '',
      branch: recentItem.branch || recentItem.organization || '',
      department: recentItem.department || '',
      title: recentItem.title || recentItem.position || '',
      room: recentItem.room || recentItem.address || '',
      address: recentItem.address || recentItem.room || '',
      tags: recentItem.tags || '',
      isDraft: false,
      // 标记这是一个基于最近搜索创建的临时联系人对象
      isTemporary: true,
      // 保存原始的最近搜索数据，便于调试
      originalRecentItem: recentItem
    };
  }
  
  // 添加草稿联系人信息（带占位符样式）
  addDraftContactInfo(container: HTMLElement, contact: ContactInfo) {
    const draftData = contact.draftData;
    
    // 添加草稿头像（带占位符）
    this.addDraftAvatar(container, contact);
    
    // 添加草稿姓名（带占位符）
    this.addDraftName(container, contact);
    
    // 添加草稿电话信息（带占位符）
    this.addDraftPhoneInfo(container, contact);
    
    // 添加草稿详细信息（带占位符）
    this.addDraftDetails(container, contact);
  }
  
  // 添加草稿头像（带占位符）
  addDraftAvatar(container: HTMLElement, contact: ContactInfo) {
    const avatarEl = container.createEl("div", { 
      cls: "contact-preview-avatar contact-draft-avatar",
      attr: {
        'title': '草稿头像',
        'data-contact-name': contact.name || ''
      }
    });
    
    // 如果有姓名，显示首字母，否则显示占位符
    if (contact.name && contact.name.trim()) {
      const firstChar = this.getFirstName(contact.name)[0] || "?";
      avatarEl.createEl("span", { text: firstChar });
    } else {
      // 创建占位符头像
      const placeholder = avatarEl.createEl("div", { cls: "contact-draft-avatar-placeholder" });
      placeholder.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      `;
    }
  }
  
  // 添加草稿姓名（带占位符）
  addDraftName(container: HTMLElement, contact: ContactInfo) {
    const nameEl = container.createEl("div", { cls: "contact-preview-title contact-draft-name" });
    
    if (contact.name && contact.name.trim()) {
      nameEl.textContent = contact.name;
    } else {
      // 创建占位符
      const placeholder = nameEl.createEl("div", { 
        cls: "contact-draft-placeholder contact-draft-name-block"
      });
    }
  }
  
  // 添加草稿电话信息（带占位符）
  addDraftPhoneInfo(container: HTMLElement, contact: ContactInfo) {
    const draftData = contact.draftData;
    
    // 电话号码
    if (draftData?.phone && draftData.phone.trim()) {
      const isLightTheme = document.body.classList.contains('theme-light');
      const phoneIcon = isLightTheme ? '📞' : '☎️';
      container.createEl("div", { 
        text: `${phoneIcon} "${draftData.phone}"`, 
        cls: "contact-preview-phone" 
      });
    } else {
      // 电话占位符
      const phonePlaceholder = container.createEl("div", { 
        cls: "contact-preview-phone contact-draft-phone-placeholder" 
      });
      phonePlaceholder.innerHTML = `
        <div class="contact-draft-placeholder contact-draft-phone-block">
        </div>
      `;
    }
    
    // 手机号码
    if (draftData?.mobile && draftData.mobile.trim()) {
      container.createEl("div", { 
        text: draftData.mobile, 
        cls: "contact-preview-mobile" 
      });
    } else {
      // 手机占位符
      const mobilePlaceholder = container.createEl("div", { 
        cls: "contact-preview-mobile contact-draft-mobile-placeholder" 
      });
      mobilePlaceholder.innerHTML = `
        <div class="contact-draft-placeholder contact-draft-mobile-block">
        </div>
      `;
    }
    
    // 邮箱
    if (draftData?.email && draftData.email.trim()) {
      const emailText = container.createEl("div", { 
        text: draftData.email, 
        cls: "contact-preview-email" 
      });
      emailText.setAttribute('data-original-email', draftData.email);
    } else {
      // 邮箱占位符
      const emailPlaceholder = container.createEl("div", { 
        cls: "contact-preview-email contact-draft-email-placeholder" 
      });
      emailPlaceholder.innerHTML = `
        <div class="contact-draft-placeholder contact-draft-email-block">
        </div>
      `;
    }
  }
  
  // 添加草稿详细信息（带占位符）
  addDraftDetails(container: HTMLElement, contact: ContactInfo) {
    const draftData = contact.draftData;
    
    // 组织信息
    if ((draftData?.organization && draftData.organization.trim()) || 
        (draftData?.department && draftData.department.trim())) {
      let orgText = "";
      if (draftData.organization) orgText += draftData.organization;
      if (draftData.organization && draftData.department) orgText += ", ";
      if (draftData.department) orgText += draftData.department;
      
      container.createEl("div", { 
        text: orgText, 
        cls: "contact-preview-org" 
      });
    } else {
      // 组织信息占位符
      const orgPlaceholder = container.createEl("div", { 
        cls: "contact-preview-org contact-draft-org-placeholder" 
      });
      orgPlaceholder.innerHTML = `
        <div class="contact-draft-placeholder contact-draft-org-block">
        </div>
      `;
    }
    
    // 职位信息
    if (draftData?.position && draftData.position.trim()) {
      container.createEl("div", { 
        text: draftData.position, 
        cls: "contact-preview-subtitle" 
      });
    } else {
      // 职位占位符
      const titlePlaceholder = container.createEl("div", { 
        cls: "contact-preview-subtitle contact-draft-title-placeholder" 
      });
      titlePlaceholder.innerHTML = `
        <div class="contact-draft-placeholder contact-draft-title-block">
        </div>
      `;
    }
    
    // 草稿保存时间
    if (draftData?.draftDate) {
      const dateText = new Date(draftData.draftDate).toLocaleString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      
      // 创建位置容器
      const locationContainer = container.createEl("div", { 
        cls: "contact-preview-location" 
      });
      
      // 在位置容器中添加占位符
      locationContainer.createEl("div", {
        cls: "contact-draft-placeholder contact-draft-location-block"
      });
      
      // 创建草稿日期容器
      const dateContainer = container.createEl("div", { 
        text: `草稿保存于 ${dateText}`, 
        cls: "contact-draft-date" 
      });
    }
  }
  
  // 打开草稿进行编辑
  async openDraftForEditing(contact: ContactInfo) {
    try {
      // 不关闭当前模态框，直接在主窗口基础上打开草稿编辑窗口
      await this.plugin.openAddContactModal();
      
      // 等待一小段时间让新模态框完全加载
      setTimeout(() => {
        // 通过事件通知新模态框填充草稿数据，传递完整的contact对象
        const event = new CustomEvent('fillDraftData', {
          detail: {
            draftData: contact.draftData,
            draftId: contact.draftId || contact.path.replace('draft://', '')
          }
        });
        document.dispatchEvent(event);
      }, 100);
      
    } catch (error) {
      console.error('打开草稿编辑失败:', error);
      new Notice('无法打开草稿编辑，请重试');
    }
  }
  
  // 创建单个联系人卡片
  createContactCard(container: HTMLElement, contact: ContactInfo, isRecentSearch = false) {
    // 确保contact对象存在
    if (!contact) {
      console.warn('createContactCard: contact is null or undefined');
      return null;
    }
    
    const contactEl = container.createEl("div", { cls: "contact-item" });
    contactEl.setAttribute('data-contact-name', contact?.name || '');
    contactEl.setAttribute('data-contact-path', contact?.path || '');
    contactEl.setAttribute('draggable', 'false'); // 防止卡片被拖动
    
    // 如果是草稿，添加特殊样式
    if (contact.isDraft) {
      contactEl.addClass('contact-draft-item');
    }
    
    // 创建联系人预览容器
    const previewEl = contactEl.createEl("div", { cls: "contact-preview" });
    
    // 如果是最近搜索项，添加删除按钮
    if (isRecentSearch) {
      this.addDeleteButton(previewEl, contact);
    }
    
    // 添加联系人基本信息
    if (contact.isDraft) {
      // 为草稿使用特殊的占位样式
      this.addDraftContactInfo(previewEl, contact);
    } else {
      // 为普通联系人使用标准样式
      this.addContactAvatar(previewEl, contact);
      this.addContactName(previewEl, contact);
      this.addContactPhoneInfo(previewEl, contact);
      this.addContactDetails(previewEl, contact);
    }
    
    // 点击联系人卡片的处理
    contactEl.addEventListener("click", (e) => {
      // 检查点击的是否是姓名元素，如果是则不执行复制邮箱操作
      const target = e.target as HTMLElement;
      if (target.classList.contains('contact-preview-title') || target.closest('.contact-preview-title')) {
        return; // 让姓名的点击事件处理
      }
      
      // 检查点击的是否是头像元素，如果是则不执行复制邮箱操作
      if (target.classList.contains('contact-preview-avatar') || target.closest('.contact-preview-avatar')) {
        return; // 让头像的点击事件处理
      }
      
      // 多重检查删除按钮区域 - 更严格的检测
      if (target.classList.contains('contact-delete-btn') || 
          target.closest('.contact-delete-btn') ||
          target.classList.contains('trash-icon') ||
          target.closest('.trash-icon')) {
        e.stopPropagation();
        e.preventDefault();
        return; // 让删除按钮的点击事件处理
      }
      
      // 基于坐标的删除按钮区域检测（右上角区域）
      const rect = contactEl.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const clickY = e.clientY - rect.top;
      
      // 删除按钮在右上角，大约 28x28 像素区域（加上 10px 边距）
      if (clickX > rect.width - 48 && clickY < 48) {
        e.stopPropagation();
        e.preventDefault();
        return; // 可能点击了删除按钮区域
      }
      
      // 添加点击效果
      contactEl.classList.add('contact-item-clicked');
      setTimeout(() => {
        contactEl.classList.remove('contact-item-clicked');
      }, 200);
      
      // 如果是草稿，打开草稿进行编辑
      if (contact.isDraft && contact.draftData) {
        this.openDraftForEditing(contact);
        return;
      }
      
      // 复制邮箱到剪贴板
      if (contact.email && contact.email.trim() !== '') {
        // 触发邮箱复制动画
        this.triggerEmailCopyAnimation(contactEl, contact.email);
        
        navigator.clipboard.writeText(contact.email).then(() => {
          // 不再显示Notice，因为我们有了视觉反馈
        }).catch(() => {
          // 如果现代API失败，尝试使用传统方法
          const textArea = document.createElement('textarea');
          textArea.value = contact.email;
          document.body.appendChild(textArea);
          textArea.select();
          try {
            document.execCommand('copy');
            // 成功复制，动画已经在triggerEmailCopyAnimation中处理
          } catch (err) {
            new Notice('复制邮箱失败', 2000);
          }
          document.body.removeChild(textArea);
        });
      } else {
        new Notice('该联系人没有邮箱信息', 2000);
      }
    });
    
    // 添加拖拽事件处理 - 使用联系人卡片而不是预览容器
    this.setupAvatarDragAndDrop(contactEl, contact);
    
    return contactEl;
  }
  
  // 添加删除按钮到最近搜索联系人卡片
  addDeleteButton(container: HTMLElement, contact: ContactInfo) {
    const deleteButton = container.createEl("div", {
      cls: "contact-delete-btn",
      attr: {
        'title': '从最近联系中移除'
      }
    });
    
    // 添加垃圾桶SVG图标
    deleteButton.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="trash-icon">
        <polyline points="3 6 5 6 21 6"></polyline>
        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
        <line x1="10" y1="11" x2="10" y2="17"></line>
        <line x1="14" y1="11" x2="14" y2="17"></line>
      </svg>
    `;
    
    // 在捕获阶段阻止事件，确保卡片不会收到任何点击事件
    deleteButton.addEventListener("click", (e) => {
      e.stopPropagation();
      e.preventDefault();
      e.stopImmediatePropagation(); // 立即停止事件传播，确保不触发任何其他事件
      
      // 添加删除按钮自己的点击效果
      deleteButton.classList.add('contact-delete-btn-clicked');
      setTimeout(() => {
        deleteButton.classList.remove('contact-delete-btn-clicked');
      }, 150);
      
      // 从最近搜索中移除该联系人
      this.removeFromRecentSearches(contact.path);
      
      // 更新界面
      const parent = container.parentElement;
      if (parent) {
        // 删除整个联系人卡片
        parent.remove();
        
        // 获取更新后的最近搜索列表
        const recentSearches = this.plugin.getRecentSearches();
        
        if (recentSearches.length === 0) {
          // 如果最近搜索为空，隐藏整个最近搜索容器
          const recentContainer = this.resultContainer.querySelector('.recent-searches-container');
          if (recentContainer) recentContainer.remove();
        } else {
          // 更新统计数据
          const statNumber = this.resultContainer.querySelector('.recent-searches-stats .stat-number');
          if (statNumber) {
            statNumber.textContent = recentSearches.length.toString();
          }
        }
      }
    });
    
    // 在捕获阶段添加额外的事件阻止机制
    deleteButton.addEventListener("mousedown", (e) => {
      e.stopPropagation();
      e.stopImmediatePropagation();
    }, true); // 使用捕获阶段
    
    deleteButton.addEventListener("mouseup", (e) => {
      e.stopPropagation();
      e.stopImmediatePropagation();
    }, true); // 使用捕获阶段
    
    // 阻止触摸事件（移动设备）- 使用 passive 选项提高性能
    deleteButton.addEventListener("touchstart", (e) => {
      e.stopPropagation();
      e.stopImmediatePropagation();
    }, { capture: true, passive: true });
    
    deleteButton.addEventListener("touchend", (e) => {
      e.stopPropagation();
      e.stopImmediatePropagation();
    }, { capture: true, passive: true });
  }
  
  // 从最近搜索中删除联系人
  removeFromRecentSearches(contactPath: string) {
    // 获取当前最近搜索
    const recentSearches = this.plugin.getRecentSearches();
    
    // 过滤掉要删除的联系人
    const filtered = recentSearches.filter(item => item.id !== contactPath);
    
    // 更新设置
    this.plugin.settings.recentSearches = filtered;
    this.plugin.saveSettings();
  }

  /**
   * 从UI中立即移除指定路径的联系人卡片
   * @param filePath 被删除的文件路径
   */
  removeContactCardFromUI(filePath: string) {
    try {
      // 查找并移除对应的联系人卡片元素
      const contactCards = this.resultContainer.querySelectorAll('.contact-item');
      
      contactCards.forEach(card => {
        const cardElement = card as HTMLElement;
        // 检查卡片是否对应被删除的文件
        if (cardElement.dataset.contactPath === filePath) {
          console.log('从UI中移除联系人卡片:', filePath);
          // 添加淡出动画
          cardElement.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
          cardElement.style.opacity = '0';
          cardElement.style.transform = 'scale(0.95)';
          
          // 动画结束后移除元素
          setTimeout(() => {
            if (cardElement.parentNode) {
              cardElement.parentNode.removeChild(cardElement);
            }
          }, 300);
        }
      });

      // 同时从缓存中移除
      this.visibleContactsCache.delete(filePath);
      
      // 从内存中的联系人信息数组中移除
      this.contactInfos = this.contactInfos.filter(contact => contact.path !== filePath);
      this.filteredContacts = this.filteredContacts.filter(contact => contact.path !== filePath);
      
      console.log('联系人卡片已从UI中移除:', filePath);
    } catch (error) {
      console.error('移除联系人卡片时出错:', error);
    }
  }
  
  // 添加联系人头像
  addContactAvatar(container: HTMLElement, contact: ContactInfo) {
    // 确保contact对象存在
    if (!contact) {
      console.warn('addContactAvatar: contact is null or undefined');
      return;
    }
    
    const avatarEl = container.createEl("div", { 
      cls: "contact-preview-avatar contact-avatar-droppable",
      attr: {
        'title': '拖拽图片到此处设置头像，或点击上传',
        'data-contact-name': contact?.name || ''
      }
    });
    
    // 如果有自定义头像，则尝试显示图片
    if (contact.avatar && typeof contact.avatar === 'string' && contact.avatar.trim() !== '') {
      try {
        // 确保头像路径是有效的
        const avatarPath = contact.avatar.trim();
        const img = avatarEl.createEl("img", {
          attr: {
            src: this.plugin.app.vault.adapter.getResourcePath(avatarPath),
            alt: contact?.name || "Avatar"
          }
        });
        
        // 图片加载失败时显示首字母
        img.onerror = () => {
          this.replaceWithFirstChar(img, avatarEl, contact);
          
          // 清除失效的头像路径
          if (this.plugin.settings.contactAvatars[contact.path]) {
            delete this.plugin.settings.contactAvatars[contact.path];
            this.plugin.saveSettings();
            
            // 更新联系人对象
            contact.avatar = null;
            
            // 找到并更新缓存中的联系人信息
            const cacheContactIndex = this.contactInfos.findIndex(c => c.path === contact.path);
            if (cacheContactIndex !== -1) {
              this.contactInfos[cacheContactIndex].avatar = null;
            }
          }
        };
      } catch (e) {
        console.error("加载头像失败:", e);
        // 失败时显示首字母
        this.showFirstChar(avatarEl, contact);
      }
    } else {
      // 如果没有自定义头像，则显示首字母
      this.showFirstChar(avatarEl, contact);
    }
    
    // 添加拖拽提示
    const dragTipEl = avatarEl.createEl("div", { cls: "contact-avatar-drag-tip" });
    dragTipEl.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>`;
    
    // 添加点击事件，点击头像时打开文件选择窗口
    avatarEl.addEventListener('click', (e) => {
      e.stopPropagation(); // 阻止冒泡，避免触发联系人打开事件
      
      // 创建隐藏的文件输入元素
      const fileInput = document.createElement('input');
      fileInput.type = 'file';
      fileInput.accept = 'image/*'; // 只接受图片文件
      fileInput.style.display = 'none';
      document.body.appendChild(fileInput);
      
      // 监听文件选择变化
      fileInput.addEventListener('change', async () => {
        if (fileInput.files && fileInput.files.length > 0) {
          // 使用现有的处理逻辑处理选择的文件
          await this.handleAvatarDrop(fileInput.files[0], contact);
        }
        // 移除临时创建的输入元素
        document.body.removeChild(fileInput);
      });
      
      // 触发文件选择对话框
      fileInput.click();
    });
  }
  
  // 移除图片并显示首字母
  replaceWithFirstChar(imgElement: HTMLImageElement, avatarEl: HTMLElement, contact: ContactInfo) {
    if (imgElement && imgElement.parentNode) {
      imgElement.remove();
    }
    this.showFirstChar(avatarEl, contact);
  }
  
  // 显示联系人姓氏首字母
  showFirstChar(avatarEl: HTMLElement, contact: ContactInfo) {
    avatarEl.empty();
    const safeName = contact?.name || "";
    const firstName = this.getFirstName(safeName);
    const firstChar = firstName ? firstName.charAt(0) : "?";
    avatarEl.createEl("span", { text: firstChar });
  }
  
  // 获取联系人的姓氏
  getFirstName(fullName: string | any): string {
    // 确保 fullName 是字符串，并且不为空
    if (!fullName || typeof fullName !== 'string') return "";
    
    const nameStr = fullName.trim();
    if (!nameStr) return "";
    
    // 处理中文姓名：通常姓在前，取第一个字
    if (/[\u4e00-\u9fa5]/.test(nameStr)) {
      return nameStr.charAt(0);
    }
    
    // 处理西方姓名：通常名在前姓在后，尝试分割并取姓氏首字母
    const nameParts = nameStr.split(/\s+/);
    if (nameParts.length > 1) {
      // 假设最后一部分是姓
      return nameParts[nameParts.length - 1];
    }
    
    // 如果无法判断，则返回第一个字符
    return nameStr;
  }
  
  // 添加联系人姓名
  addContactName(container: HTMLElement, contact: ContactInfo) {
    const safeName = contact?.name || "未命名联系人";
    const nameEl = container.createEl("div", { 
      text: safeName, 
      cls: "contact-preview-title" 
    });
    
    // 添加点击事件，点击姓名打开笔记
    nameEl.addEventListener("click", (e) => {
      console.log('点击联系人姓名:', contact?.name);
      e.stopPropagation(); // 阻止事件冒泡，避免触发卡片的复制邮箱事件
      this.openContactNote(contact);
    });
    
    // 添加鼠标悬停样式提示
    nameEl.style.cursor = 'pointer';
    nameEl.setAttribute('title', '点击打开联系人笔记');
  }
  
  // 添加联系人电话信息
  addContactPhoneInfo(container: HTMLElement, contact: ContactInfo) {
    // 添加电话
    if (contact.phone && contact.phone !== "-") {
      const isLightTheme = document.body.classList.contains('theme-light');
      const phoneIcon = isLightTheme ? '📞' : '☎️';
      container.createEl("div", { 
        text: `${phoneIcon} "${contact.phone}"`, 
        cls: "contact-preview-phone" 
      });
    }
    
    // 添加手机
    if (contact.mobile) {
      container.createEl("div", { 
        text: contact.mobile, 
        cls: "contact-preview-mobile" 
      });
    }
    
    // 添加邮箱 - 使用特殊结构支持动画效果
    if (contact.email) {
      // 直接创建邮箱文本元素，不需要容器和图标
      const emailText = container.createEl("div", { 
        text: contact.email, 
        cls: "contact-preview-email" 
      });
      
      // 存储原始邮箱文本，用于恢复
      emailText.setAttribute('data-original-email', contact.email);
    }
  }
  
  // 添加联系人详细信息
  addContactDetails(container: HTMLElement, contact: ContactInfo) {
    // 确保contact对象存在
    if (!contact) {
      console.warn('addContactDetails: contact is null or undefined');
      return;
    }
    
    const fieldSettings = this.plugin.settings.templateFieldSettings;
    
    if (fieldSettings && fieldSettings.fields.length > 0) {
      // 使用YAML字段配置控制显示
      const enabledFields = fieldSettings.fields.filter(field => field.enabled);
      
      // 分离必需字段和可编辑字段
      const requiredFields = enabledFields.filter(field => field.isRequired);
      const editableFields = enabledFields.filter(field => !field.isRequired);
      
      // 必需字段按固定顺序排序
      const sortedRequiredFields = requiredFields.sort((a, b) => {
        const requiredOrder = ['name', 'avatar', 'phone', 'mobile', 'email'];
        const aIndex = requiredOrder.indexOf(a.key.toLowerCase());
        const bIndex = requiredOrder.indexOf(b.key.toLowerCase());
        return aIndex - bIndex;
      });
      
      // 可编辑字段按order排序
      const sortedEditableFields = editableFields.sort((a, b) => a.order - b.order);
      
      // 合并字段列表：必需字段在前，可编辑字段在后
      const finalFieldsOrder = [...sortedRequiredFields, ...sortedEditableFields];
      
      // 基础字段（姓名、头像、座机、手机、邮箱）由其他方法处理，这里过滤掉
      const baseFields = ['name', 'avatar', 'phone', 'mobile', 'email'];
      const nonBaseFields = finalFieldsOrder.filter(field => !baseFields.includes(field.key.toLowerCase()));
      
      let inlineBuffer: {field: YamlFieldConfig, value: string}[] = [];
      
      for (const field of nonBaseFields) {
        const fieldValue = this.getFieldValue(contact, field.key);
        if (fieldValue) {
          if (field.isInline) {
            // 内联字段缓存起来
            inlineBuffer.push({field, value: fieldValue});
            
            // 检查是否是最后一个内联字段，或者下一个字段不是内联字段
            const currentIndex = nonBaseFields.findIndex(f => f.key === field.key);
            const nextField = currentIndex + 1 < nonBaseFields.length ? nonBaseFields[currentIndex + 1] : null;
            const isLastInlineField = !nextField || !nextField.isInline;
            
            // 如果是最后一个内联字段，立即显示所有缓存的内联字段
            if (isLastInlineField && inlineBuffer.length > 0) {
              const inlineText = inlineBuffer.map(item => item.value).join(', ');
              container.createEl("div", { 
                text: inlineText, 
                cls: "contact-preview-org" 
              });
              inlineBuffer = []; // 清空缓存
            }
          } else {
            // 非内联字段立即显示
            this.addFieldElement(container, field, fieldValue);
          }
        }
      }
    } else {
      // 如果没有YAML字段配置，使用默认显示方式
      // 添加组织信息
      if (contact.branch || contact.department) {
        let orgText = "";
        if (contact.branch) orgText += contact.branch;
        if (contact.branch && contact.department) orgText += ", ";
        if (contact.department) orgText += contact.department;
        
        container.createEl("div", { 
          text: orgText, 
          cls: "contact-preview-org" 
        });
      }
      
      // 添加职位信息
      if (contact.title) {
        container.createEl("div", { 
          text: contact.title, 
          cls: "contact-preview-subtitle" 
        });
      }
      
      // 添加位置信息
      if (contact.room) {
        container.createEl("div", { 
          text: contact.room, 
          cls: "contact-preview-location" 
        });
      }
    }
  }
  
  /**
   * 获取联系人字段值
   */
  private getFieldValue(contact: ContactInfo, fieldKey: string): string | null {
    const key = fieldKey.toLowerCase();
    const contactData = contact as any;
    
    // 安全检查函数：确保值是字符串且不为空
    const isValidStringValue = (value: any): boolean => {
      return value && typeof value === 'string' && value.trim() !== '';
    };
    
    // 尝试直接获取
    if (contactData[fieldKey] && isValidStringValue(contactData[fieldKey])) {
      return contactData[fieldKey];
    }
    
    // 尝试小写版本
    if (contactData[key] && isValidStringValue(contactData[key])) {
      return contactData[key];
    }
    
    // 特殊字段映射
    const fieldMapping: Record<string, string[]> = {
      'organization': ['branch', 'organization', 'company'],
      'company': ['branch', 'organization', 'company'],
      'branch': ['branch', 'organization', 'company'],
      'department': ['department'],
      'position': ['title', 'position', 'job'],
      'job': ['title', 'position', 'job'],
      'title': ['title', 'position', 'job'],
      'office': ['room', 'office', 'location'],
      'location': ['room', 'office', 'location'],
      'room': ['room', 'office', 'location'],
      'created': ['created', 'date'],
      'date': ['created', 'date'],
      'car': ['car'],
      'address': ['address'],
      'tags': ['tags']
    };
    
    if (fieldMapping[key]) {
      for (const mappedKey of fieldMapping[key]) {
        if (contactData[mappedKey] && isValidStringValue(contactData[mappedKey])) {
          return contactData[mappedKey];
        }
      }
    }
    
    return null;
  }
  
  /**
   * 添加字段显示元素
   */
  private addFieldElement(container: HTMLElement, field: YamlFieldConfig, value: string) {
    const cssClass = this.getFieldCssClass(field.key);
    container.createEl("div", { 
      text: value, 
      cls: cssClass
    });
  }
  
  /**
   * 获取字段对应的CSS类名
   */
  private getFieldCssClass(fieldKey: string): string {
    const classMapping: Record<string, string> = {
      'branch': 'contact-preview-org',
      'department': 'contact-preview-org',
      'organization': 'contact-preview-org',
      'company': 'contact-preview-org',
      'title': 'contact-preview-subtitle',
      'position': 'contact-preview-subtitle',
      'job': 'contact-preview-subtitle',
      'room': 'contact-preview-location',
      'office': 'contact-preview-location',
      'location': 'contact-preview-location',
      'address': 'contact-preview-location',
      'car': 'contact-preview-subtitle',
      'tags': 'contact-preview-tags',
      'created': 'contact-preview-date',
      'date': 'contact-preview-date'
    };
    
    return classMapping[fieldKey.toLowerCase()] || 'contact-preview-subtitle';
  }

  // 触发邮箱复制动画效果
  triggerEmailCopyAnimation(contactEl: HTMLElement, originalEmail: string) {
    const emailElement = contactEl.querySelector('.contact-preview-email') as HTMLElement;
    
    if (!emailElement) {
      return; // 如果找不到必要的元素，直接返回
    }
    
    // 先缩放整个联系人卡片
    contactEl.classList.add('contact-item-clicked');
    setTimeout(() => {
      contactEl.classList.remove('contact-item-clicked');
    }, 200);
    
    // 然后执行邮箱文字动画
    setTimeout(() => {
      this.animateTextChange(emailElement, '已复制电子邮箱', originalEmail);
    }, 100);
  }

  // 复制部门所有邮箱地址
  copyDepartmentEmails(contacts: ContactInfo[], buttonElement: HTMLElement) {
    // 收集所有有效的邮箱地址
    const emails = contacts
      .map(contact => contact.email)
      .filter(email => email && email.trim() !== '' && email !== '-')
      .join('; ');
    
    if (emails.length === 0) {
      new Notice('该部门没有可用的邮箱地址', 2000);
      return;
    }
    
    // 复制到剪贴板
    navigator.clipboard.writeText(emails).then(() => {
      // 触发按钮动画
      this.triggerCopyButtonAnimation(buttonElement, `已复制 ${contacts.filter(c => c.email && c.email.trim() !== '' && c.email !== '-').length} 个邮箱`);
    }).catch(() => {
      // 如果现代API失败，尝试使用传统方法
      const textArea = document.createElement('textarea');
      textArea.value = emails;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        this.triggerCopyButtonAnimation(buttonElement, `已复制 ${contacts.filter(c => c.email && c.email.trim() !== '' && c.email !== '-').length} 个邮箱`);
      } catch (err) {
        new Notice('复制邮箱失败', 2000);
      }
      document.body.removeChild(textArea);
    });
  }
  
  // 触发复制按钮动画
  triggerCopyButtonAnimation(buttonElement: HTMLElement, successText: string) {
    // 保存原始内容
    const originalHTML = buttonElement.innerHTML;
    const originalClasses = buttonElement.className;
    const originalAriaLabel = buttonElement.getAttribute('aria-label') || '复制全部门邮箱地址';
    

    
    // 阶段1：缩放按钮
    buttonElement.classList.add('department-copy-btn-clicked');
    
    setTimeout(() => {
      // 阶段2：显示成功消息（更大的对勾图标）
      buttonElement.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="m9 12 2 2 4-4"></path>
        </svg>
      `;
      buttonElement.classList.remove('department-copy-btn-clicked');
      buttonElement.classList.add('department-copy-btn-success');
      buttonElement.setAttribute('aria-label', successText);
      
      // 阶段3：1.5秒后恢复原状
      setTimeout(() => {
        buttonElement.innerHTML = originalHTML;
        buttonElement.className = originalClasses;
        buttonElement.setAttribute('aria-label', originalAriaLabel);
      }, 1500);
    }, 200);
  }

  // 波浪切换文字动画 - 整体动画模拟波浪效果
  animateTextChange(element: HTMLElement, successText: string, originalText: string) {
    // 保存原始状态
    const originalClasses = element.className;
    const rect = element.getBoundingClientRect();
    
    // 设置固定容器样式，防止布局变化
    element.style.position = 'relative';
    element.style.width = rect.width + 'px';
    element.style.height = rect.height + 'px';
    element.style.display = 'flex';
    element.style.alignItems = 'center';
    element.style.justifyContent = 'center';
    element.style.overflow = 'hidden';
    
    // 阶段1：波浪淡出原文本
    element.classList.add('email-wave-fadeout');
    
    setTimeout(() => {
      // 阶段2：显示成功消息
      element.innerHTML = `
        <span class="email-success-icon">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="10"/>
            <path d="m9 12 2 2 4-4" stroke="white" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </span>
        <span class="email-success-text">${successText}</span>
      `;
      
      element.classList.remove('email-wave-fadeout');
      element.classList.add('email-success-state');
      
      // 阶段3：停顿1.5秒后波浪淡入恢复原文本
      setTimeout(() => {
        element.classList.add('email-wave-fadeout');
        
        setTimeout(() => {
          // 恢复原文本
          element.innerHTML = originalText;
          element.classList.remove('email-wave-fadeout');
          element.classList.remove('email-success-state');
          element.classList.add('email-wave-fadein');
          
          setTimeout(() => {
            // 完全恢复原始状态
            element.className = originalClasses;
            element.style.position = '';
            element.style.width = '';
            element.style.height = '';
            element.style.display = '';
            element.style.alignItems = '';
            element.style.justifyContent = '';
            element.style.overflow = '';
            element.classList.remove('email-wave-fadein');
          }, 400);
        }, 300);
      }, 1500);
    }, 400);
  }
  
  // 打开联系人笔记
  openContactNote(contact: ContactInfo) {
    console.log('打开联系人笔记:', contact?.name, '路径:', contact?.path);
    
    // 如果有路径，就打开相应的笔记
    if (contact?.path) {
      const file = this.plugin.app.vault.getAbstractFileByPath(contact.path);
      if (file && file instanceof TFile) {
        // 关闭当前模态框
        this.close();
        
        // 打开联系人笔记
        this.plugin.app.workspace.getLeaf().openFile(file as TFile);
        console.log('成功打开联系人笔记:', contact.name);
      } else {
        console.error('找不到联系人文件:', contact.path);
        new Notice(`找不到联系人文件: ${contact.path}`, 3000);
      }
    } else {
      console.error('联系人没有路径信息');
      new Notice("此联系人没有关联的笔记文件", 3000);
    }
  }
  
  // 以下是拖放头像功能的方法
  
  // 设置头像拖拽处理
  setupAvatarDragAndDrop(container: HTMLElement, contact: ContactInfo) {
    console.log(`设置拖拽事件处理器: ${contact.name}, 容器类型: ${container.className}, 容器ID: ${container.id}`);
    
    // 获取联系人头像元素 - 确保我们能找到它
    const avatarEl = container.querySelector('.contact-preview-avatar');
    if (!avatarEl) {
      console.error(`未找到头像元素: ${contact.name}`);
      return;
    }
    
    // 添加拖拽相关事件
    container.addEventListener('dragover', (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      console.log(`拖拽到: ${contact.name}, 容器类型: ${container.className}`);
      container.addClass('contact-avatar-dropzone');
      
      // 确保拖拽传递的是文件类型
      if (e.dataTransfer) {
        e.dataTransfer.dropEffect = 'copy';
      }
    });
    
    container.addEventListener('dragleave', (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      console.log(`拖拽移出: ${contact.name}, 容器类型: ${container.className}`);
      container.removeClass('contact-avatar-dropzone');
    });
    
    container.addEventListener('dragenter', (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      console.log(`拖拽进入: ${contact.name}, 容器类型: ${container.className}`);
    });
    
    container.addEventListener('drop', async (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      console.log(`拖拽释放: ${contact.name}, 容器类型: ${container.className}, 有效数据类型: ${e.dataTransfer?.types.join(', ')}`);
      container.removeClass('contact-avatar-dropzone');
      
      // 阻止编辑器接收这个拖放事件
      e.stopImmediatePropagation();
      
      // 处理拖拽的文件
      const files = e.dataTransfer?.files;
      if (files && files.length > 0) {
        console.log(`拖拽文件信息: 类型=${files[0].type}, 大小=${files[0].size}字节, 名称=${files[0].name}`);
        await this.handleAvatarDrop(files[0], contact);
      } else {
        console.log('未获取到有效的拖拽文件');
        console.log('DataTransfer对象详情:', e.dataTransfer ? Object.keys(e.dataTransfer) : 'null');
      }
    });
    
    // 高亮头像元素，提示用户可以拖拽
    avatarEl.addClass('contact-avatar-droppable');
    
    // 添加提示信息
    avatarEl.setAttribute('title', '拖拽图片到此处设置头像');
  }
  
  // 处理头像拖拽上传
  async handleAvatarDrop(file: File, contact: ContactInfo) {
    try {
      console.log(`开始处理拖拽的文件: 类型=${file.type}, 名称=${file.name}, 联系人=${contact.name}`);
      
      // 检查是否是图片文件
      if (!file.type.startsWith('image/')) {
        console.error(`文件类型不是图片: ${file.type}`);
        new Notice('只能上传图片文件作为头像', 3000);
        return;
      }
      
      // 显示头像确认对话框
      console.log('显示确认对话框');
      const confirmResult = await this.showAvatarConfirmDialog(contact.name);
      console.log(`用户确认结果: ${confirmResult}`);
      if (!confirmResult) return;
      
      // 加载图片
      console.log('开始读取图片文件');
      const reader = new FileReader();
      
      // 读取文件为 DataURL
      const loadImagePromise = new Promise<string>((resolve, reject) => {
        reader.onload = (e) => {
          console.log('图片读取完成');
          resolve(e.target?.result as string);
        };
        reader.onerror = (e) => {
          console.error('图片读取失败:', e);
          reject(e);
        };
        reader.readAsDataURL(file);
      });
      
      const dataUrl = await loadImagePromise;
      console.log(`图片数据URL长度: ${dataUrl.length}`);
      
      // 调整图片尺寸
      console.log('开始调整图片尺寸');
      const resizedImageData = await this.resizeImage(dataUrl, 30, 30);
      console.log(`调整后图片数据URL长度: ${resizedImageData.length}`);
      
      // 保存图片到联系人文件夹/images目录
      console.log('开始保存图片');
      const imagePath = await this.saveAvatarImage(resizedImageData, contact);
      console.log(`图片保存路径: ${imagePath}`);
      
      // 更新联系人头像路径
      console.log('更新联系人头像路径在设置中');
      this.plugin.settings.contactAvatars[contact.path] = imagePath;
      await this.plugin.saveSettings();
      
      // 更新当前联系人的头像信息
      contact.avatar = imagePath;
      
      // 更新联系人缓存
      // 在contactInfos和filteredContacts中找到并更新联系人信息
      const cacheContactIndex = this.contactInfos.findIndex(c => c.path === contact.path);
      if (cacheContactIndex !== -1) {
        this.contactInfos[cacheContactIndex].avatar = imagePath;
      }
      
      const filteredContactIndex = this.filteredContacts.findIndex(c => c.path === contact.path);
      if (filteredContactIndex !== -1) {
        this.filteredContacts[filteredContactIndex].avatar = imagePath;
      }
      
      // 添加CSS规则
      console.log('添加CSS规则');
      this.addContactCssRule(contact.name, imagePath);
      
      // 刷新显示
      console.log('重新渲染联系人列表');
      this.renderContacts();
      
      new Notice(`联系人"${contact.name}"头像已更新`, 2000);
    } catch (error) {
      console.error('处理头像上传失败:', error);
      new Notice('头像上传失败: ' + (error as Error).message, 3000);
    }
  }
  
  // 显示头像确认对话框
  async showAvatarConfirmDialog(contactName: string): Promise<boolean> {
    return new Promise((resolve) => {
      // 创建确认对话框
      const modalEl = document.createElement('div');
      modalEl.className = 'avatar-confirm-dialog';
      document.body.appendChild(modalEl);
      
      // 添加标题
      const titleEl = document.createElement('h3');
      titleEl.textContent = `确认将此图片用作"联系人 ${contactName} 的头像"？`;
      modalEl.appendChild(titleEl);
      
      // 添加按钮容器
      const buttonContainer = document.createElement('div');
      buttonContainer.className = 'avatar-confirm-buttons';
      modalEl.appendChild(buttonContainer);
      
      // 添加确认按钮
      const confirmButton = document.createElement('button');
      confirmButton.textContent = '确定';
      confirmButton.className = 'mod-cta';
      buttonContainer.appendChild(confirmButton);
      
      // 添加取消按钮
      const cancelButton = document.createElement('button');
      cancelButton.textContent = '取消';
      buttonContainer.appendChild(cancelButton);
      
      // 添加背景遮罩
      const overlay = document.createElement('div');
      overlay.className = 'avatar-confirm-overlay';
      document.body.appendChild(overlay);
      
      // 确认按钮点击事件
      confirmButton.addEventListener('click', () => {
        document.body.removeChild(modalEl);
        document.body.removeChild(overlay);
        resolve(true);
      });
      
      // 取消按钮点击事件
      cancelButton.addEventListener('click', () => {
        document.body.removeChild(modalEl);
        document.body.removeChild(overlay);
        resolve(false);
      });
      
      // 点击遮罩关闭
      overlay.addEventListener('click', () => {
        document.body.removeChild(modalEl);
        document.body.removeChild(overlay);
        resolve(false);
      });
    });
  }
  
  // 调整图片尺寸
  async resizeImage(dataUrl: string, width: number, height: number): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        const img = document.createElement('img');
        img.onload = () => {
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          
          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('无法创建画布上下文'));
            return;
          }
          
          ctx.drawImage(img, 0, 0, width, height);
          
          // 转换为圆形头像
          ctx.globalCompositeOperation = 'destination-in';
          ctx.beginPath();
          ctx.arc(width / 2, height / 2, width / 2, 0, Math.PI * 2);
          ctx.closePath();
          ctx.fill();
          
          // 恢复默认操作模式
          ctx.globalCompositeOperation = 'source-over';
          
          // 返回调整后的图片数据
          resolve(canvas.toDataURL('image/png'));
        };
        
        img.onerror = () => {
          reject(new Error('图片加载失败'));
        };
        
        img.src = dataUrl;
      } catch (e) {
        reject(e);
      }
    });
  }
  
  // 保存头像图片
  async saveAvatarImage(dataUrl: string, contact: ContactInfo): Promise<string> {
    try {
      console.log(`开始保存头像: ${contact.name}`);
      
      // 获取联系人文件所在目录
      const contactFile = this.plugin.app.vault.getAbstractFileByPath(contact.path);
      if (!contactFile || !(contactFile instanceof TFile)) {
        console.error(`找不到联系人文件: ${contact.path}`);
        throw new Error('找不到联系人文件');
      }
      
      // 获取联系人文件所在的目录路径
      const contactFilePath = contact.path;
      const lastSlashIndex = contactFilePath.lastIndexOf('/');
      const contactDirPath = lastSlashIndex > 0 ? contactFilePath.substring(0, lastSlashIndex) : '';
      
      console.log(`联系人目录路径: ${contactDirPath}`);
      
      // 确保images目录存在
      let imagesDirPath = contactDirPath ? `${contactDirPath}/images` : 'images';
      console.log(`尝试使用图片目录: ${imagesDirPath}`);
      
      try {
        // 先检查目录是否存在
        let imagesDir = this.plugin.app.vault.getAbstractFileByPath(imagesDirPath);
        
        // 如果目录不存在，创建它
        if (!imagesDir) {
          console.log(`创建图片目录: ${imagesDirPath}`);
          try {
            // 尝试创建整个路径
            await this.plugin.app.vault.createFolder(imagesDirPath);
            console.log(`成功创建图片目录: ${imagesDirPath}`);
            // 重新获取创建的目录引用
            imagesDir = this.plugin.app.vault.getAbstractFileByPath(imagesDirPath);
          } catch (err) {
            console.error(`创建目录失败 (${imagesDirPath}): ${err.message}`);
            
            // 如果创建失败，使用根目录下的images文件夹
            imagesDirPath = 'images';
            imagesDir = this.plugin.app.vault.getAbstractFileByPath('images');
            
            // 如果根目录下的images也不存在，尝试创建它
            if (!imagesDir) {
              console.log(`尝试在根目录创建images文件夹: ${imagesDirPath}`);
              await this.plugin.app.vault.createFolder('images');
              console.log(`成功在根目录创建images文件夹`);
            }
          }
        } else {
          console.log(`图片目录已存在: ${imagesDirPath}`);
        }
        
        // 直接使用联系人姓名作为文件名，处理特殊字符
        let safeName = contact.name || "未命名联系人";
        
        // 移除不安全的文件名字符
        safeName = safeName.replace(/[\\/:*?"<>|]/g, "_");
        
        // 直接使用联系人姓名作为文件名，不再添加时间戳
        const fileName = `${safeName}.png`;
        const filePath = `${imagesDirPath}/${fileName}`;
        
        console.log(`准备保存头像文件: ${filePath}`);
        
        // 检查是否已存在同名文件，如果存在则删除
        const existingFile = this.plugin.app.vault.getAbstractFileByPath(filePath);
        if (existingFile && existingFile instanceof TFile) {
          await this.plugin.app.vault.delete(existingFile);
          console.log(`删除了已存在的文件: ${filePath}`);
        }
        
        // 将base64数据转换为二进制
        const base64Data = dataUrl.replace(/^data:image\/\w+;base64,/, '');
        const binaryData = this.base64ToBinary(base64Data);
        
        // 保存文件
        console.log(`开始写入二进制文件: ${filePath}, 数据长度: ${binaryData.length}字节`);
        await this.plugin.app.vault.createBinary(filePath, binaryData);
        console.log(`头像保存成功: ${filePath}`);
        
        // 增加验证，确认文件确实被创建了
        const newFile = this.plugin.app.vault.getAbstractFileByPath(filePath);
        if (!newFile || !(newFile instanceof TFile)) {
          console.error(`头像文件没有被正确创建: ${filePath}`);
          throw new Error('头像文件保存失败');
        }
        
        new Notice(`头像已保存到 ${filePath}`, 3000);
        return filePath;
      } catch (folderErr) {
        console.error('处理图片目录时发生错误:', folderErr);
        
        // 如果目录操作失败，使用临时目录策略
        // 直接使用联系人姓名作为文件名，不再添加时间戳
        const fileName = `${contact.name || "联系人"}.png`;
        const filePath = fileName; // 直接保存在根目录
        
        // 检查是否已存在同名文件，如果存在则删除
        const existingFile = this.plugin.app.vault.getAbstractFileByPath(filePath);
        if (existingFile && existingFile instanceof TFile) {
          await this.plugin.app.vault.delete(existingFile);
          console.log(`删除了已存在的文件: ${filePath}`);
        }
        
        // 将base64数据转换为二进制
        const base64Data = dataUrl.replace(/^data:image\/\w+;base64,/, '');
        const binaryData = this.base64ToBinary(base64Data);
        
        // 保存文件到根目录
        console.log(`尝试保存头像到根目录: ${filePath}`);
        await this.plugin.app.vault.createBinary(filePath, binaryData);
        
        new Notice(`头像已保存到 ${filePath}（根目录）`, 3000);
        return filePath;
      }
    } catch (error) {
      console.error('保存头像失败:', error);
      new Notice(`保存头像失败: ${error.message}`, 3000);
      throw error;
    }
  }
  
  // base64 转二进制数据
  base64ToBinary(base64: string): Uint8Array {
    const raw = window.atob(base64);
    const rawLength = raw.length;
    const array = new Uint8Array(new ArrayBuffer(rawLength));
    
    for(let i = 0; i < rawLength; i++) {
      array[i] = raw.charCodeAt(i);
    }
    return array;
  }
  
  // 添加联系人CSS规则
  addContactCssRule(contactName: string, imagePath: string) {
    try {
      console.log(`添加联系人CSS规则: ${contactName} -> ${imagePath}`);
      
      // 查找或创建样式元素
      let styleElement = document.getElementById('contact-avatars-css');
      
      if (!styleElement) {
        styleElement = document.createElement('style');
        styleElement.id = 'contact-avatars-css';
        document.head.appendChild(styleElement);
        console.log('创建了新的样式元素');
      }
      
      // 创建CSS选择器（使用联系人名称作为选择器）
      // 对名称进行转义，处理特殊字符
      const escapedName = contactName.replace(/"/g, '\\"');
      
      const selector = `.contact-item[data-contact-name="${escapedName}"] .contact-preview-avatar`;
      const linkSelector = `a[data-href="${escapedName}"]::before`;
      
      // 获取图片资源路径
      const resourcePath = this.plugin.app.vault.adapter.getResourcePath(imagePath);
      console.log(`资源路径: ${resourcePath}`);
      
      // 创建CSS规则
      const cssRule = `
        ${selector} {
          background-image: url('${resourcePath}');
          background-size: cover;
          background-position: center;
        }
        ${selector} span {
          display: none;
        }
        ${linkSelector} {
          background-image: url('${resourcePath}');
          background-size: cover;
          background-position: center center;
          display: inline-block;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          content: "";
          margin-right: 5px;
          vertical-align: middle;
        }
      `;
      
      console.log('CSS规则:', cssRule);
      
      // 添加到样式表
      try {
        const htmlStyleElement = styleElement as HTMLStyleElement;
        if (htmlStyleElement.sheet) {
          try {
            const sheet = htmlStyleElement.sheet;
            sheet.insertRule(`${selector} { background-image: url('${resourcePath}'); background-size: cover; background-position: center; }`, sheet.cssRules.length);
            sheet.insertRule(`${selector} span { display: none; }`, sheet.cssRules.length);
            sheet.insertRule(`${linkSelector} { background-image: url('${resourcePath}'); background-size: cover; background-position: center center; display: inline-block; width: 20px; height: 20px; border-radius: 50%; content: ""; margin-right: 5px; vertical-align: middle; }`, sheet.cssRules.length);
            console.log('CSS规则已成功添加到样式表');
          } catch (e) {
            console.error('通过sheet API添加CSS规则失败:', e);
            // 备用方法：直接添加到style元素内容中
            styleElement.textContent += cssRule;
            console.log('通过textContent添加了CSS规则');
          }
        } else {
          // 备用方法：直接添加到style元素内容中
          styleElement.textContent += cssRule;
          console.log('通过textContent添加了CSS规则（无sheet）');
        }
      } catch (e) {
        console.error('添加CSS规则发生异常:', e);
        // 最后的备用方法
        const newStyle = document.createElement('style');
        newStyle.textContent = cssRule;
        document.head.appendChild(newStyle);
        console.log('创建了新样式元素来添加规则');
      }
      
      // 更新全局联系人头像CSS
      this.plugin.initializeContactAvatarCss();
    } catch (error) {
      console.error('设置CSS规则失败:', error);
    }
  }

  // 重置所有过滤器
  resetFilters() {
    // 重置筛选状态
    this.selectedOrganization = '';
    this.selectedDepartment = '';
    this.selectedRole = '';
    
    // 重置搜索框
    if (this.searchInput) {
      this.searchInput.value = '';
      const clearButton = this.searchInput.parentElement?.querySelector('.search-clear-button') as HTMLElement;
      if (clearButton) clearButton.style.display = 'none';
    }
    
    // 重新渲染联系人
    this.renderContacts();
  }
  


  // 创建新建联系人卡片
  createNewContactCard(container: HTMLElement) {
    const contactEl = container.createEl("div", { cls: "contact-item new-contact-item" });
    contactEl.setAttribute('draggable', 'false');
    
    // 创建联系人预览容器
    const previewEl = contactEl.createEl("div", { cls: "contact-preview" });
    
    // 添加拖放区域 - 改为独立元素，不再复用avatar类
    const dropZone = previewEl.createEl("div", { 
      cls: "new-contact-drop-zone",
      attr: {
        'title': '拖拽文件到此处创建新联系人'
      }
    });
    
    // 添加圆形加号图标容器
    const plusCircle = dropZone.createEl("div", { cls: "plus-circle" });
    
    // 添加加号图标
    plusCircle.innerHTML = `
      <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
        <circle cx="8.5" cy="7" r="4"></circle>
        <line x1="20" y1="8" x2="20" y2="14"></line>
        <line x1="23" y1="11" x2="17" y2="11"></line>
      </svg>
    `;
    
    // 添加提示文字
    dropZone.createEl("div", { text: "拖拽图片、名片、邮件签名、通讯录截图,将自动填充", cls: "drop-text" });
    
    // 添加拖拽处理
    this.setupNewContactDragAndDrop(contactEl);
    
    // 点击事件 - 创建新联系人
    contactEl.addEventListener("click", () => {
      this.createNewContact();
    });
    
    return contactEl;
  }
  
  // 设置新建联系人的拖拽功能
  setupNewContactDragAndDrop(element: HTMLElement) {
    // 拖拽进入事件
    element.addEventListener("dragenter", (e) => {
      e.preventDefault();
      e.stopPropagation();
      element.classList.add("dragover");
    });
    
    // 拖拽悬停事件
    element.addEventListener("dragover", (e) => {
      e.preventDefault();
      e.stopPropagation();
      element.classList.add("dragover");
    });
    
    // 拖拽离开事件
    element.addEventListener("dragleave", (e) => {
      e.preventDefault();
      e.stopPropagation();
      element.classList.remove("dragover");
    });
    
    // 拖拽放下事件
    element.addEventListener("drop", async (e) => {
      e.preventDefault();
      e.stopPropagation();
      element.classList.remove("dragover");
      
      // 处理文件拖放
      if (e.dataTransfer && e.dataTransfer.files.length > 0) {
        const file = e.dataTransfer.files[0];
        await this.handleNewContactDrop(file);
      }
    });
  }
  
  // 处理拖拽到新建联系人区域的文件
  async handleNewContactDrop(file: File) {
    // 检查是否是图片文件
    if (file.type.startsWith('image/')) {
      // 创建新联系人并设置头像
      await this.createNewContactWithAvatar(file);
    } else {
      // 如果不是图片文件，提示用户
      new Notice("请拖拽图片文件作为联系人头像");
    }
  }
  
  // 创建新联系人
  async createNewContact() {
    // 使用AddContactModal替代NewContactModal
    this.plugin.openAddContactModal();
  }
  
  // 使用头像创建新联系人
  async createNewContactWithAvatar(file: File) {
    try {
      console.log('开始使用头像创建新联系人:', file.name);
      
      // 检查是否是图片文件
      if (!file.type.startsWith('image/')) {
        new Notice('只能使用图片文件作为头像', 3000);
        return;
      }
      
      // 直接调用插件的方法打开添加联系人模态框，并传入文件
      await this.plugin.openAddContactModalWithFile(file);
      
    } catch (error) {
      console.error('使用头像创建联系人失败:', error);
      new Notice('创建联系人失败: ' + (error as Error).message, 3000);
    }
  }
  
  // 设置机构标题的折叠功能
  setupBranchCollapse(titleElement: HTMLElement, contentElement: HTMLElement) {
    titleElement.addEventListener('click', () => {
      const isCollapsed = titleElement.getAttribute('data-collapsed') === 'true';
      const arrow = titleElement.querySelector('.collapse-arrow') as HTMLElement;
      
      if (isCollapsed) {
        // 展开
        titleElement.setAttribute('data-collapsed', 'false');
        contentElement.style.display = '';
        if (arrow) {
          arrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
          </svg>`;
        }
        titleElement.removeClass('collapsed');
      } else {
        // 折叠
        titleElement.setAttribute('data-collapsed', 'true');
        contentElement.style.display = 'none';
        if (arrow) {
          arrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
          </svg>`;
        }
        titleElement.addClass('collapsed');
      }
      
      // 折叠状态改变时保存状态
      this.debouncedSaveSearchState();
    });
    
    // 添加必要的CSS样式
    this.addCollapsibleStyles();
  }
  
  // 设置部门标题的折叠功能
  setupDepartmentCollapse(titleElement: HTMLElement, contentElement: HTMLElement) {
    titleElement.addEventListener('click', () => {
      const isCollapsed = titleElement.getAttribute('data-collapsed') === 'true';
      const arrow = titleElement.querySelector('.collapse-arrow') as HTMLElement;
      
      if (isCollapsed) {
        // 展开
        titleElement.setAttribute('data-collapsed', 'false');
        contentElement.style.display = '';
        if (arrow) {
          arrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
          </svg>`;
        }
        titleElement.removeClass('collapsed');
      } else {
        // 折叠
        titleElement.setAttribute('data-collapsed', 'true');
        contentElement.style.display = 'none';
        if (arrow) {
          arrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
          </svg>`;
        }
        titleElement.addClass('collapsed');
      }
      
      // 折叠状态改变时保存状态
      this.debouncedSaveSearchState();
    });
  }
  
  // 设置最近搜索标题的折叠功能
  setupRecentSearchesCollapse(titleElement: HTMLElement, contentElement: HTMLElement) {
    titleElement.addEventListener('click', () => {
      const isCollapsed = titleElement.getAttribute('data-collapsed') === 'true';
      const arrow = titleElement.querySelector('.collapse-arrow') as HTMLElement;
      
      if (isCollapsed) {
        // 展开
        titleElement.setAttribute('data-collapsed', 'false');
        contentElement.style.display = '';
        if (arrow) {
          arrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
            <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
          </svg>`;
        }
        titleElement.removeClass('collapsed');
      } else {
        // 折叠
        titleElement.setAttribute('data-collapsed', 'true');
        contentElement.style.display = 'none';
        if (arrow) {
          arrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
          </svg>`;
        }
        titleElement.addClass('collapsed');
      }
      
      // 折叠状态改变时保存状态
      this.debouncedSaveSearchState();
    });
  }
  

  

  
  // 添加折叠功能的CSS样式 (样式已移至 styles.css)
  addCollapsibleStyles() {
    // 样式已移至 styles.css 文件，无需动态插入
  }
  
  // 设置下拉选择器的通用功能
  setupSelectDropdown(trigger: HTMLElement, optionsContainer: HTMLElement) {
    let isMouseOverOptions = false;
    let closeTimeout: NodeJS.Timeout | null = null;
    
    // 点击触发器显示/隐藏选项
    trigger.addEventListener('click', (e) => {
      e.stopPropagation();
      
      // 关闭其他打开的下拉菜单
      
      
      // 切换当前下拉菜单
      if (optionsContainer.hasClass('show')) {
        optionsContainer.removeClass('show');
      } else {
        optionsContainer.addClass('show');
        // 聚焦到第一个选项
        const firstOption = optionsContainer.querySelector('.select-option') as HTMLElement;
        if (firstOption) {
          firstOption.focus();
        }
      }
    });
    
    // 鼠标进入选项容器
    optionsContainer.addEventListener('mouseenter', () => {
      isMouseOverOptions = true;
      if (closeTimeout) {
        clearTimeout(closeTimeout);
        closeTimeout = null;
      }
    });
    
    // 鼠标离开选项容器，延迟关闭
    optionsContainer.addEventListener('mouseleave', () => {
      isMouseOverOptions = false;
      closeTimeout = setTimeout(() => {
        if (!isMouseOverOptions) {
          optionsContainer.removeClass('show');
        }
      }, 200); // 200ms延迟，避免误触
    });
    
    // 鼠标进入触发器取消关闭
    trigger.addEventListener('mouseenter', () => {
      if (closeTimeout) {
        clearTimeout(closeTimeout);
        closeTimeout = null;
      }
    });
    
    // 键盘导航支持
    trigger.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        trigger.click();
      } else if (e.key === 'ArrowDown' && optionsContainer.hasClass('show')) {
        e.preventDefault();
        const firstOption = optionsContainer.querySelector('.select-option') as HTMLElement;
        if (firstOption) {
          firstOption.focus();
        }
      }
    });
    
    // 为选项添加键盘导航
    optionsContainer.addEventListener('keydown', (e) => {
      const options = Array.from(optionsContainer.querySelectorAll('.select-option')) as HTMLElement[];
      const currentIndex = options.findIndex(option => option === document.activeElement);
      
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          const nextIndex = currentIndex < options.length - 1 ? currentIndex + 1 : 0;
          options[nextIndex].focus();
          break;
        case 'ArrowUp':
          e.preventDefault();
          const prevIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
          options[prevIndex].focus();
          break;
        case 'Enter':
          e.preventDefault();
          if (currentIndex >= 0) {
            options[currentIndex].click();
          }
          break;
        case 'Escape':
          e.preventDefault();
          optionsContainer.removeClass('show');
          trigger.focus();
          break;
      }
    });
    
    // 点击文档其他地方关闭下拉菜单
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;
      if (!trigger.contains(target) && !optionsContainer.contains(target)) {
        optionsContainer.removeClass('show');
      }
    });
  }
  

  
  // 防抖搜索函数
  debounceSearch(func: (query: string) => void, delay: number) {
    return (query: string) => {
      if (this.searchDebounceTimer) {
        clearTimeout(this.searchDebounceTimer);
      }
      this.searchDebounceTimer = setTimeout(() => func(query), delay);
    };
  }
  
  // 执行高级搜索
  performAdvancedSearch(query: string): ContactInfo[] {
    if (!query.trim()) return this.contactInfos.slice(0, 8); // 显示前8个联系人
    
    const normalizedQuery = query.toLowerCase().trim();
    const isPhoneQuery = /^6\d*$/.test(normalizedQuery);
    
    return this.contactInfos.filter(contact => {
      // 应用筛选条件
      if (!this.matchesOrgFilter(contact) || 
          !this.matchesDeptFilter(contact) || 
          !this.matchesRoleFilter(contact)) {
        return false;
      }
      
      // 搜索匹配
      if (isPhoneQuery) {
        return contact.phone && contact.phone.includes(normalizedQuery);
      }
      
      return Object.entries(contact).some(([key, value]) => {
        if (key === 'content' || key === 'path') return false;
        return value && String(value).toLowerCase().includes(normalizedQuery);
      });
    }).slice(0, 8); // 限制结果数量
  }
  
  // 渲染搜索下拉列表
  renderSearchDropdown(contacts: ContactInfo[]) {
    this.searchResultsDropdown.empty();
    
    if (contacts.length === 0) {
      const noResults = this.searchResultsDropdown.createEl('div', {
        cls: 'search-dropdown-item no-results',
        text: '未找到匹配的联系人'
      });
      return;
    }
    
    contacts.forEach((contact, index) => {
      const item = this.searchResultsDropdown.createEl('div', {
        cls: 'search-dropdown-item',
        attr: { 'data-index': index.toString() }
      });
      
      // 联系人信息容器
      const infoContainer = item.createEl('div', {cls: 'contact-info-container'});
      
      // 左侧：头像和基本信息
      const leftInfo = infoContainer.createEl('div', {cls: 'contact-left-info'});
      
      // 头像
      const avatar = leftInfo.createEl('div', {cls: 'contact-dropdown-avatar'});
      if (contact.avatar) {
        const img = avatar.createEl('img', {
          attr: { src: this.plugin.app.vault.adapter.getResourcePath(contact.avatar) }
        });
        img.onerror = () => {
          avatar.empty();
          avatar.createEl('span', { text: this.getFirstName(contact.name || "")[0] || "?" });
        };
      } else {
        avatar.createEl('span', { text: this.getFirstName(contact.name || "")[0] || "?" });
      }
      
      // 姓名和部门
      const nameInfo = leftInfo.createEl('div', {cls: 'contact-name-info'});
      nameInfo.createEl('div', {
        cls: 'contact-dropdown-name',
        text: contact.name || '未命名联系人'
      });
      
      if (contact.department || contact.branch) {
        let orgText = '';
        if (contact.branch) orgText += contact.branch;
        if (contact.branch && contact.department) orgText += ' - ';
        if (contact.department) orgText += contact.department;
        nameInfo.createEl('div', {
          cls: 'contact-dropdown-org',
          text: orgText
        });
      }
      
      // 右侧：联系方式
      const rightInfo = infoContainer.createEl('div', {cls: 'contact-right-info'});
      
      if (contact.phone && contact.phone !== '-') {
        rightInfo.createEl('div', {
          cls: 'contact-dropdown-phone',
          text: contact.phone
        });
      }
      
      if (contact.mobile) {
        rightInfo.createEl('div', {
          cls: 'contact-dropdown-mobile',
          text: contact.mobile
        });
      }
      
      // 点击事件
      item.addEventListener('click', () => {
        console.log('点击下拉菜单联系人:', contact?.name);
        this.selectedSearchAction = contact;
        this.searchInput.value = contact.name || '';
        this.hideSearchDropdown();
        this.openContactNote(contact);
      });
      
      // 鼠标悬停
      item.addEventListener('mouseenter', () => {
        this.clearDropdownSelection();
        item.addClass('selected');
      });
    });
  }
  
  // 显示搜索下拉列表
  showSearchDropdown() {
    this.searchResultsDropdown.removeClass('hidden');
    this.searchResultsDropdown.addClass('show');
  }
  
  // 隐藏搜索下拉列表
  hideSearchDropdown() {
    this.searchResultsDropdown.addClass('hide');
    setTimeout(() => {
      this.searchResultsDropdown.removeClass('show');
      this.searchResultsDropdown.removeClass('hide');
      this.searchResultsDropdown.addClass('hidden');
    }, 300);
  }
  

  
  // 清空搜索输入框
  clearSearchInput() {
    this.searchInput.value = '';
    this.debouncedQuery = '';
    
    // 触发输入事件以更新搜索结果
    this.filterContacts();
    
    // 隐藏多级搜索结果
    this.hideMultiLevelResults();
    
    // 保存搜索状态
    this.debouncedSaveSearchState();
    
    // 重新聚焦到搜索框
    this.searchInput.focus();
  }
  
  // 处理搜索下拉列表键盘导航
  handleSearchDropdownNavigation(e: KeyboardEvent) {
    const items = Array.from(this.searchResultsDropdown.querySelectorAll('.search-dropdown-item:not(.no-results)')) as HTMLElement[];
    if (items.length === 0) return;
    
    const currentSelected = this.searchResultsDropdown.querySelector('.search-dropdown-item.selected') as HTMLElement;
    let newIndex = -1;
    
    if (currentSelected) {
      const currentIndex = items.indexOf(currentSelected);
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
          break;
        case 'ArrowUp':
          e.preventDefault();
          newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
          break;
        case 'Enter':
          e.preventDefault();
          currentSelected.click();
          return;
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      newIndex = 0;
    }
    
    if (newIndex >= 0) {
      this.clearDropdownSelection();
      items[newIndex].addClass('selected');
    }
  }
  
  // 清除下拉列表选择状态
  clearDropdownSelection() {
    this.searchResultsDropdown.querySelectorAll('.search-dropdown-item.selected').forEach(item => {
      item.removeClass('selected');
    });
  }

  // 设置多级搜索功能
  setupMultiLevelSearch() {
    // 防抖搜索函数
    const debouncedSearch = this.debounceSearch((query: string) => {
      if (!this.isSearchFocused) return;
      
      this.debouncedQuery = query;
      // 当搜索框聚焦时，总是显示多级搜索结果
      if (this.isSearchFocused) {
        this.updateMultiLevelResults();
        this.showMultiLevelResults(); // 确保显示
      } else {
        this.hideMultiLevelResults();
      }
    }, 150);
    
    // 输入事件
    this.searchInput.addEventListener('input', (e) => {
      const value = (e.target as HTMLInputElement).value;
      const query = value.trim();
      
      // 立即更新 debouncedQuery，避免时序问题
      this.debouncedQuery = value;
      
      if (this.isSearchFocused) {
        // 总是隐藏传统搜索下拉菜单，使用多级搜索
        this.hideSearchDropdown();
        // 不管是否有输入内容，都显示多级搜索（输入内容会过滤结果）
        this.updateMultiLevelResults();
        this.showMultiLevelResults(); // 确保显示
      }
      
      debouncedSearch(value);
      
      // 同时执行原有的筛选功能
      this.filterContacts();
      
      // 保存搜索状态（防抖）
      this.debouncedSaveSearchState();
    });
    
    // 焦点事件
    this.searchInput.addEventListener('focus', () => {
      console.log('=== 搜索框焦点事件触发 ===');
      this.isSearchFocused = true;

      // 重置搜索结果隐藏状态 - 用户主动聚焦搜索框时恢复显示
      if (this.isResultsHiddenByScroll && this.resultsContainer) {
        console.log('🎯 焦点事件 - 恢复搜索结果容器显示');
        this.isResultsHiddenByScroll = false;
        
        // 移除隐藏动画类，恢复显示
        this.resultsContainer.removeClass('contact-obcm-search--scroll-hiding');
        this.resultsContainer.style.display = '';
        
        // 添加显示动画
        this.resultsContainer.addClass('contact-obcm-search--scroll-showing');
        
        // 清理动画类
        setTimeout(() => {
          if (this.resultsContainer) {
            this.resultsContainer.removeClass('contact-obcm-search--scroll-showing');
          }
        }, 300);
      }

      // 移除搜索框变小状态（如果存在）
      if (this.inputContainer && this.inputContainer.hasClass('contact-obcm-search--scrolled')) {
        console.log('🎯 移除搜索框变小状态，恢复大搜索框');
        this.inputContainer.removeClass('contact-obcm-search--scrolled');
      }

      // 如果不是通过点击标签触发的，清除当前编辑标签
      if (!this.currentEditingTag || Date.now() - (this.currentEditingTag.lastClickTime || 0) > 500) {
        this.currentEditingTag = null;
        console.log('焦点事件 - 清除当前编辑标签');
      }

      // 总是隐藏传统搜索下拉菜单，使用多级搜索
      this.hideSearchDropdown();

      // 使用防抖机制显示下拉菜单，避免与焦点事件重复调用
      this.debouncedShowIntelligentNextLevel('focus');
    });
    
    // 点击事件 - 当搜索框已有焦点时，点击显示下拉列表
    this.searchInput.addEventListener('click', () => {
      console.log('=== 搜索框点击事件触发 ===');
      this.isSearchFocused = true;

      // 重置搜索结果隐藏状态 - 用户主动点击搜索框时恢复显示
      if (this.isResultsHiddenByScroll && this.resultsContainer) {
        console.log('🖱️ 点击事件 - 恢复搜索结果容器显示');
        this.isResultsHiddenByScroll = false;
        
        // 移除隐藏动画类，恢复显示
        this.resultsContainer.removeClass('contact-obcm-search--scroll-hiding');
        this.resultsContainer.style.display = '';
        
        // 添加显示动画
        this.resultsContainer.addClass('contact-obcm-search--scroll-showing');
        
        // 清理动画类
        setTimeout(() => {
          if (this.resultsContainer) {
            this.resultsContainer.removeClass('contact-obcm-search--scroll-showing');
          }
        }, 300);
      }

      // 移除搜索框变小状态（如果存在）
      if (this.inputContainer && this.inputContainer.hasClass('contact-obcm-search--scrolled')) {
        console.log('🖱️ 移除搜索框变小状态，恢复大搜索框');
        this.inputContainer.removeClass('contact-obcm-search--scrolled');
      }

      // 如果不是通过点击标签触发的，清除当前编辑标签
      if (!this.currentEditingTag || Date.now() - (this.currentEditingTag.lastClickTime || 0) > 500) {
        this.currentEditingTag = null;
        console.log('点击事件 - 清除当前编辑标签');
      }

      // 总是隐藏传统搜索下拉菜单，使用多级搜索
      this.hideSearchDropdown();

      // 使用防抖机制显示下拉菜单，避免与焦点事件重复调用
      this.debouncedShowIntelligentNextLevel('click');
    });
    
    // 失焦事件
    this.searchInput.addEventListener('blur', () => {
      if (!this.isMouseDownOnResults) {
        setTimeout(() => {
          if (document.activeElement !== this.searchInput) {
            this.isSearchFocused = false;
            this.hideMultiLevelResults();
          }
        }, 150);
      }
    });
    
    // 键盘事件
    this.searchInput.addEventListener('keydown', (e) => this.handleMultiLevelKeydown(e));
    
    // 搜索结果鼠标事件
    this.searchResults.addEventListener('mousedown', () => {
      this.isMouseDownOnResults = true;
    });
    
    this.searchResults.addEventListener('mouseup', () => {
      this.isMouseDownOnResults = false;
    });
    
    // 传统搜索下拉菜单鼠标事件
    this.searchResultsDropdown.addEventListener('mousedown', () => {
      this.isMouseDownOnResults = true;
    });
    
    this.searchResultsDropdown.addEventListener('mouseup', () => {
      this.isMouseDownOnResults = false;
    });
    
    document.addEventListener('mouseup', () => {
      this.isMouseDownOnResults = false;
    });
    
    // 全局键盘快捷键
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.searchInput.blur();
        this.hideMultiLevelResults();
        this.keyboardSelectedIndex = -1;
      }
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        this.searchInput.focus();
      }
    });
  }
  
  // 处理多级搜索键盘事件
  handleMultiLevelKeydown(e: KeyboardEvent) {
    const items = this.resultsList.querySelectorAll('.contact-obcm-search--search-item');
    
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (items.length > 0) {
        this.updateKeyboardSelection(Math.min(this.keyboardSelectedIndex + 1, items.length - 1));
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (items.length > 0) {
        this.updateKeyboardSelection(Math.max(this.keyboardSelectedIndex - 1, 0));
      }
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (this.keyboardSelectedIndex >= 0 && this.keyboardSelectedIndex < items.length) {
        const selectedItem = items[this.keyboardSelectedIndex] as HTMLElement;
        const itemId = selectedItem.dataset.itemId;
        const itemType = selectedItem.dataset.itemType;
        
        if (itemId && itemType) {
          const filteredItems = this.performMultiLevelSearch();
          const item = filteredItems.find(i => i.id === itemId && i.type === itemType);
          if (item) {
            this.addSearchTag(item);
          }
        }
      }
    } else if (e.key === 'Backspace' && this.searchInput.value === '' && 
              this.selectedActions.length > 0 && !this.isRemoving) {
      const lastAction = this.selectedActions[this.selectedActions.length - 1];
      this.removeSearchTag(lastAction.id);
    }
  }
  
  // 更新键盘选择
  updateKeyboardSelection(newIndex: number) {
    const items = this.resultsList.querySelectorAll('.contact-obcm-search--search-item');
    
    items.forEach(item => item.removeClass('contact-obcm-search--keyboard-selected'));
    
    if (newIndex >= 0 && newIndex < items.length) {
      this.keyboardSelectedIndex = newIndex;
      items[newIndex].addClass('contact-obcm-search--keyboard-selected');
    } else {
      this.keyboardSelectedIndex = -1;
    }
  }
  
  // 更新多级搜索结果
  updateMultiLevelResults() {
    console.log('updateMultiLevelResults 开始执行');
    console.log('isSearchFocused:', this.isSearchFocused, 'isRemoving:', this.isRemoving);
    
    if (!this.isSearchFocused || this.isRemoving) {
      console.log('条件不满足，退出 updateMultiLevelResults');
      return;
    }
    
    // 检查是否应该显示结果
    const query = this.searchInput.value.trim();
    const hasSelectedActions = this.selectedActions.length > 0;
    
    console.log('查询内容:', query, '是否有选中的标签:', hasSelectedActions);
    console.log('当前选中的标签:', this.selectedActions.map(a => `${a.type}:${a.label}`));
    
    // 总是显示多级搜索结果，无论是否有查询内容
    // 查询内容会影响筛选结果，但不会隐藏整个菜单
    const filteredContacts = this.performMultiLevelSearch();
    console.log('搜索结果数量:', filteredContacts.length);
    
    if (filteredContacts.length > 0) {
      console.log('搜索结果预览:', filteredContacts.slice(0, 3).map(item => `${item.type}:${item.label}`));
    }
    
    this.renderMultiLevelResults(filteredContacts);
    this.updateResultsContainerWidth();
    this.showMultiLevelResults();
    console.log('updateMultiLevelResults 执行完成');
  }
  
  // 执行多级搜索
  performMultiLevelSearch(): any[] {
    const query = this.debouncedQuery.toLowerCase().trim();
    const currentLevel = this.getCurrentSearchLevel();
    
    console.log('执行多级搜索，当前级别:', currentLevel, '查询内容:', query);
    
    if (currentLevel === 1) {
      // 第一级：显示机构列表
      console.log('显示机构列表');
      return this.getOrganizationOptions(query);
    } else if (currentLevel === 2) {
      // 第二级：显示选中机构的部门列表
      console.log('显示部门列表');
      return this.getDepartmentOptions(query);
    } else if (currentLevel === 3) {
      // 第三级：显示岗位列表
      console.log('显示岗位列表');
      return this.getRoleOptions(query);
    } else {
      // 第四级：显示联系人列表
      console.log('显示联系人列表');
      return this.getContactOptions(query);
    }
  }
  
  // 获取当前搜索级别
  getCurrentSearchLevel(): number {
    // 检查是否有搜索查询内容
    const searchQuery = this.debouncedQuery.toLowerCase().trim();
    
    // 如果有搜索内容，直接进入联系人搜索级别
    if (searchQuery) {
      console.log('有搜索内容，直接进入联系人级别:', searchQuery);
      return 4; // 联系人级别
    }
    
    if (this.selectedActions.length === 0) return 1; // 机构级别
    
    const hasOrg = this.selectedActions.some(action => action.type === 'organization');
    const hasDept = this.selectedActions.some(action => action.type === 'department');
    const hasRole = this.selectedActions.some(action => action.type === 'role');
    
    console.log('getCurrentSearchLevel - hasOrg:', hasOrg, 'hasDept:', hasDept, 'hasRole:', hasRole);
    console.log('当前选中的actions:', this.selectedActions.map(a => `${a.type}:${a.label}`));
    
    // 即使有机构标签，也需要判断是否可以继续选择部门
    // 如果只有机构标签（无论是"全部"还是具体机构），显示部门级别
    if (hasOrg && !hasDept) {
      console.log('有机构标签但无部门标签，返回部门级别(2)');
      return 2; // 部门级别
    }
    
    // 如果有机构和部门标签，但没有岗位标签，显示岗位级别  
    if (hasOrg && hasDept && !hasRole) {
      console.log('有机构和部门标签但无岗位标签，返回岗位级别(3)');
      return 3; // 岗位级别
    }
    
    // 如果有机构、部门和岗位标签，显示联系人级别
    if (hasOrg && hasDept && hasRole) {
      console.log('有机构、部门和岗位标签，返回联系人级别(4)');
      return 4; // 联系人级别
    }
    
    // 如果没有机构标签，显示机构级别
    if (!hasOrg) {
      console.log('无机构标签，返回机构级别(1)');
      return 1; // 机构级别
    }
    
    // 默认返回部门级别
    console.log('默认返回部门级别(2)');
    return 2;
  }
  
  // 获取机构选项
  getOrganizationOptions(query: string): any[] {
    const organizations = Object.keys(this.organizations);
    let filteredOrgs = organizations;
    
    // 使用 debouncedQuery 而不是传入的 query 参数，以确保与用户输入同步
    const searchQuery = this.debouncedQuery.toLowerCase().trim();
    
    if (searchQuery) {
      filteredOrgs = organizations.filter(org => 
        org.toLowerCase().includes(searchQuery)
      );
    }
    
    const options = [];
    
    // 检查当前编辑的标签，如果是总行标签，则隐藏"全部"选项
    const isEditingZongHang = this.currentEditingTag && 
                             this.currentEditingTag.type === 'organization' && 
                             this.currentEditingTag.id === '总行';
    
    // 只有在非总行标签编辑时才显示"全部"选项
    if (!isEditingZongHang) {
      options.push({
        id: 'all',
        label: '全部',
        type: 'organization',
        level: 1,
        description: '查看联系人文件夹下的所有联系人'
      });
    }
    
    // 添加具体机构选项
    const orgOptions = filteredOrgs.map(org => ({
      id: org,
      label: org,
      type: 'organization',
      level: 1,
      description: `${this.organizations[org].length} 个部门`
    }));
    
    return options.concat(orgOptions);
  }
  
  // 获取部门选项
  getDepartmentOptions(query: string): any[] {
    const selectedOrgs = this.selectedActions.filter(action => action.type === 'organization');
    console.log('获取部门选项，当前选中的机构数量:', selectedOrgs.length);
    console.log('获取部门选项，当前选中的机构:', selectedOrgs.map(org => org.label));
    
    if (selectedOrgs.length === 0) {
      console.log('没有选中的机构，返回空数组');
      return [];
    }
    
    let departments: string[] = [];
    
    // 获取具体的机构标签（排除"全部"）
    const specificOrgs = selectedOrgs.filter(org => org.id !== 'all');
    const hasAllOption = selectedOrgs.some(org => org.id === 'all');
    
    console.log('具体机构标签:', specificOrgs.map(org => org.label));
    console.log('是否有"全部"标签:', hasAllOption);
    
    const allDepartments = new Set<string>();
    
    if (specificOrgs.length > 0) {
      // 如果有具体机构标签，优先基于具体机构获取部门（即使同时有"全部"标签）
      console.log('基于具体机构获取部门:', specificOrgs.map(org => org.label));
      
      this.contactInfos.forEach(contact => {
        const orgMatches = specificOrgs.some(org => contact.branch === org.id);
        if (orgMatches) {
          if (contact.department && typeof contact.department === 'string' && contact.department.trim()) {
            allDepartments.add(contact.department.trim());
          } else {
            allDepartments.add('未分类');
          }
        }
      });
      
      console.log('匹配的联系人数量:', this.contactInfos.filter(contact => 
        specificOrgs.some(org => contact.branch === org.id)
      ).length);
      
    } else if (hasAllOption) {
      // 只有"全部"标签时，获取所有部门
      console.log('只有"全部"标签，获取所有部门');
      
      this.contactInfos.forEach(contact => {
        if (contact.department && typeof contact.department === 'string' && contact.department.trim()) {
          allDepartments.add(contact.department.trim());
        }
      });
      
      // 如果没有部门信息的联系人，添加一个"未分类"部门
      if (this.contactInfos.some(contact => !contact.department || typeof contact.department !== 'string' || !contact.department.trim())) {
        allDepartments.add('未分类');
      }
    }
    
    departments = Array.from(allDepartments);
    console.log('最终获取到的部门:', departments);
    
    let filteredDepts = departments;
    
    // 使用 debouncedQuery 进行筛选
    const searchQuery = this.debouncedQuery.toLowerCase().trim();
    
    if (searchQuery) {
      filteredDepts = departments.filter(dept => 
        dept.toLowerCase().includes(searchQuery)
      );
    }
    
    return filteredDepts.map(dept => {
      let contactCount = 0;
      
      if (specificOrgs.length > 0) {
        // 如果有具体机构标签，统计该机构下该部门的联系人数量
        if (dept === '未分类') {
          // 统计具体机构中没有部门信息的联系人
          contactCount = this.contactInfos.filter(contact => {
            const orgMatches = specificOrgs.some(org => contact.branch === org.id);
            return orgMatches && (!contact.department || typeof contact.department !== 'string' || !contact.department.trim());
          }).length;
        } else {
          contactCount = this.contactInfos.filter(contact => {
            const orgMatches = specificOrgs.some(org => contact.branch === org.id);
            return orgMatches && contact.department === dept;
          }).length;
        }
        console.log(`部门"${dept}"的联系人数量(基于具体机构${specificOrgs.map(org => org.label).join(',')}):`, contactCount);
      } else if (hasAllOption) {
        // 只有"全部"标签时，统计所有联系人中该部门的联系人数量
        if (dept === '未分类') {
          // 统计没有部门信息的联系人
          contactCount = this.contactInfos.filter(contact => 
            !contact.department || typeof contact.department !== 'string' || !contact.department.trim()
          ).length;
        } else {
          contactCount = this.contactInfos.filter(contact => 
            contact.department === dept
          ).length;
        }
        console.log(`部门"${dept}"的联系人数量(基于全部):`, contactCount);
      }
      
      return {
        id: dept,
        label: dept,
        type: 'department',
        level: 2,
        description: `${contactCount} 位联系人`,
        organizationId: specificOrgs.length > 0 ? specificOrgs[0].id : 'all'
      };
    });
  }
  
  // 获取岗位选项
  getRoleOptions(query: string): any[] {
    const selectedOrgs = this.selectedActions.filter(action => action.type === 'organization');
    const selectedDepts = this.selectedActions.filter(action => action.type === 'department');
    
    console.log('获取岗位选项，当前选中的机构:', selectedOrgs.map(org => org.label));
    console.log('获取岗位选项，当前选中的部门:', selectedDepts.map(dept => dept.label));
    
    if (selectedOrgs.length === 0 || selectedDepts.length === 0) {
      console.log('机构或部门为空，返回空岗位列表');
      return [];
    }
    
    // 检查是否选择了"全部"
    const hasAllOption = selectedOrgs.some(org => org.id === 'all');
    
    // 收集所有岗位
    const roles = new Set<string>();
    
    this.contactInfos.forEach(contact => {
      // 检查机构匹配
      let orgMatches = false;
      if (hasAllOption) {
        orgMatches = true; // 选择了"全部"，所有机构都匹配
      } else {
        orgMatches = selectedOrgs.some(org => contact.branch === org.id);
      }
      
      // 检查部门匹配
      const deptMatches = selectedDepts.some(dept => {
        if (dept.id === '未分类') {
          return !contact.department || typeof contact.department !== 'string' || !contact.department.trim();
        } else {
          return dept.id === contact.department;
        }
      });
      
      if (orgMatches && deptMatches && contact.title) {
        roles.add(contact.title);
      }
    });
    
    let filteredRoles = Array.from(roles);
    
    // 使用 debouncedQuery 进行筛选
    const searchQuery = this.debouncedQuery.toLowerCase().trim();
    
    if (searchQuery) {
      filteredRoles = filteredRoles.filter(role => 
        role.toLowerCase().includes(searchQuery)
      );
    }
    
    return filteredRoles.map(role => {
      // 统计该岗位的联系人数量
      const contactCount = this.contactInfos.filter(contact => {
        let orgMatches = false;
        if (hasAllOption) {
          orgMatches = true; // 选择了"全部"，所有机构都匹配
        } else {
          orgMatches = selectedOrgs.some(org => contact.branch === org.id);
        }
        
        const deptMatches = selectedDepts.some(dept => {
          if (dept.id === '未分类') {
            return !contact.department || typeof contact.department !== 'string' || !contact.department.trim();
          } else {
            return dept.id === contact.department;
          }
        });
        return orgMatches && deptMatches && contact.title === role;
      }).length;
      
      return {
        id: role,
        label: role,
        type: 'role',
        level: 3,
        description: `${contactCount} 位联系人`
      };
    });
  }
  
  // 获取联系人选项
  getContactOptions(query: string): any[] {
    console.log('=== getContactOptions 开始执行 ===');
    console.log('传入的query参数:', query);
    console.log('当前debouncedQuery:', this.debouncedQuery);
    console.log('总联系人数量:', this.contactInfos.length);
    
    const selectedOrgs = this.selectedActions.filter(action => action.type === 'organization');
    const selectedDepts = this.selectedActions.filter(action => action.type === 'department');
    const selectedRoles = this.selectedActions.filter(action => action.type === 'role');
    
    console.log('选中的机构数量:', selectedOrgs.length);
    console.log('选中的部门数量:', selectedDepts.length);
    console.log('选中的岗位数量:', selectedRoles.length);
    
    // 使用 debouncedQuery 进行筛选
    const searchQuery = this.debouncedQuery.toLowerCase().trim();
    const isPhoneQuery = /^6\d*$/.test(searchQuery);
    
    console.log('最终searchQuery:', searchQuery);
    console.log('是否为电话查询:', isPhoneQuery);
    
    // 如果有搜索内容，允许直接搜索所有联系人，无需先选择机构和部门
    // 如果没有搜索内容，则需要选择机构和部门才能查看联系人列表
    if (!searchQuery && (selectedOrgs.length === 0 || selectedDepts.length === 0)) {
      console.log('没有搜索内容且缺少标签，返回空数组');
      return [];
    }
    
    // 检查是否选择了"全部"
    const hasAllOption = selectedOrgs.some(org => org.id === 'all');
    
    // 检查是否只有"全部"标签而没有具体机构
    const specificOrgs = selectedOrgs.filter(org => org.id !== 'all');
    const hasOnlyAllOrg = selectedOrgs.length > 0 && specificOrgs.length === 0;
    
    // 如果有搜索内容且只有"全部"标签，返回提示信息
    if (searchQuery && hasOnlyAllOrg) {
      console.log('有搜索内容但只有"全部"标签，返回机构选择提示');
      return [{
        id: 'select-org-hint',
        label: '💡 请选择具体机构以精确搜索',
        type: 'hint',
        level: 1,
        description: '点击选择机构标签，然后搜索该机构内的联系人'
      }];
    }

    let results = this.contactInfos.filter(contact => {
      // 搜索匹配检查
      if (searchQuery) {
        // 使用统一的搜索匹配方法
        const searchMatches = this.searchMatchesContact(contact, searchQuery);
        
        // 如果不匹配搜索条件，直接排除
        if (!searchMatches) return false;
      }
      
      // 应用筛选标签（无论是否有搜索内容都需要应用）
      
      // 检查机构匹配 - 下拉菜单搜索中，只基于具体机构进行筛选
      if (specificOrgs.length > 0) {
        // 如果有具体机构标签，必须匹配其中一个
        const orgMatches = specificOrgs.some(org => contact.branch === org.id);
        console.log(`联系人 ${contact.name} 机构匹配：联系人机构=${contact.branch}, 具体选中机构=${specificOrgs.map(o => o.id).join(',')}, 结果=${orgMatches}`);
        if (!orgMatches) return false;
      }
      
      // 检查部门匹配
      if (selectedDepts.length > 0) {
        const deptMatches = selectedDepts.some(dept => {
          if (dept.id === '未分类') {
            // 对于"未分类"部门，匹配没有部门信息的联系人
            return !contact.department || !contact.department.trim();
          } else {
            return dept.id === contact.department;
          }
        });
        if (!deptMatches) return false;
      }
      
      // 检查岗位匹配（如果有选择岗位的话）
      if (selectedRoles.length > 0) {
        const roleMatches = selectedRoles.some(role => role.id === contact.title);
        if (!roleMatches) return false;
      }
      
            return true;
    });

    console.log('过滤后的联系人数量:', results.length);
    
    if (results.length > 0) {
      console.log('过滤后的前3个联系人:', results.slice(0, 3).map(c => c.name));
    } else {
      console.log('没有找到匹配的联系人');
    }

    const finalResults = results.map(contact => ({
      id: contact.path,
      label: contact.name || '未命名联系人',
      type: 'contact',
      level: 4,
      description: contact.phone || '',
      contact: contact
    }));
    
    console.log('最终返回的结果数量:', finalResults.length);
    console.log('=== getContactOptions 执行完成 ===');
    
    return finalResults;
  }
  
  // 渲染多级搜索结果
  renderMultiLevelResults(items: any[]) {
    this.resultsList.empty();
    this.keyboardSelectedIndex = -1;
    
    this.updateLevelHeader(items);
    
    if (items.length === 0) {
      const li = this.resultsList.createEl('li', {cls: 'contact-obcm-search--search-item no-results'});
      li.style.textAlign = 'center';
      li.innerHTML = '未找到匹配项';
      return;
    }
    
    items.forEach((item, index) => {
      const li = this.resultsList.createEl('li', {
        cls: 'contact-obcm-search--search-item',
        attr: {
          'data-item-id': item.id,
          'data-item-type': item.type,
          'data-index': index.toString()
        }
      });
      li.style.animationDelay = `${index * 0.03}s`;
      
      const itemContent = li.createEl('div', {cls: 'contact-obcm-search--item-content'});
      
      const itemLeft = itemContent.createEl('div', {cls: 'contact-obcm-search--item-left'});
      
      // 添加图标
      const itemIcon = itemLeft.createEl('span', {cls: 'contact-obcm-search--item-icon'});
      itemIcon.innerHTML = this.getItemIcon(item);
      
      // 添加标签
      itemLeft.createEl('span', {
        cls: 'contact-obcm-search--item-label',
        text: item.label
      });
      
      // 添加描述信息
      if (item.description) {
        itemLeft.createEl('span', {
          cls: 'contact-obcm-search--item-description',
          text: item.description
        });
      }
      
      const itemRight = itemContent.createEl('div', {cls: 'contact-obcm-search--item-right'});
      
      // 为机构选项添加选中状态显示
      if (item.type === 'organization') {
        let isSelected = false;
        
        // 如果当前在编辑机构标签，优先以编辑的标签为选中状态
        if (this.currentEditingTag && this.currentEditingTag.type === 'organization') {
          isSelected = this.currentEditingTag.id === item.id;
        } else {
          // 否则根据已选择的标签判断
          const selectedOrg = this.selectedActions.find(action => action.type === 'organization');
          isSelected = selectedOrg && selectedOrg.id === item.id;
        }
        
        if (isSelected) {
          li.addClass('contact-obcm-search--selected');
          // 添加选中标识
          const selectedIcon = itemRight.createEl('span', {cls: 'contact-obcm-search--selected-icon'});
          selectedIcon.innerHTML = `<svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>`;
        }
      }
      
      // 为部门选项添加选中状态显示
      if (item.type === 'department') {
        const selectedDepts = this.selectedActions.filter(action => action.type === 'department');
        const isSelected = selectedDepts.some(dept => dept.id === item.id);
        if (isSelected) {
          li.addClass('contact-obcm-search--selected');
          // 添加选中标识
          const selectedIcon = itemRight.createEl('span', {cls: 'contact-obcm-search--selected-icon'});
          selectedIcon.innerHTML = `<svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>`;
        }
      }
      
      // 为岗位选项添加选中状态显示
      if (item.type === 'role') {
        const selectedRoles = this.selectedActions.filter(action => action.type === 'role');
        const isSelected = selectedRoles.some(role => role.id === item.id);
        if (isSelected) {
          li.addClass('contact-obcm-search--selected');
          // 添加选中标识
          const selectedIcon = itemRight.createEl('span', {cls: 'contact-obcm-search--selected-icon'});
          selectedIcon.innerHTML = `<svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
          </svg>`;
        }
      }
      
      // 添加级别信息
      itemRight.createEl('span', {
        cls: 'contact-obcm-search--item-meta',
        text: this.getLevelText(item.type)
      });
      
      // 点击事件
      li.addEventListener('mousedown', (e) => {
        e.preventDefault();
        
        // 如果是提示项，处理特殊逻辑
        if (item.type === 'hint') {
          // 显示机构选择列表
          const orgOptions = this.getOrganizationOptions('');
          this.renderMultiLevelResults(orgOptions);
          this.updateResultsContainerWidth();
          return;
        }
        
        this.addSearchTag(item);
      });
      
      // 鼠标悬停
      li.addEventListener('mouseenter', () => {
        const items = this.resultsList.querySelectorAll('.contact-obcm-search--search-item');
        items.forEach(item => item.removeClass('contact-obcm-search--keyboard-selected'));
        this.keyboardSelectedIndex = -1;
      });
    });
  }
  
  // 获取项目图标
  getItemIcon(item: any): string {
    if (item.type === 'hint') {
      return `<svg width="16" height="16" fill="#FFA500" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
      </svg>`;
    }
    // 所有其他选项都使用对勾图标
    return `<svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
    </svg>`;
  }
  
  // 获取级别文本
  getLevelText(type: string): string {
    switch (type) {
      case 'organization': return '机构';
      case 'department': return '部门';
      case 'role': return '岗位';
      case 'contact': return '联系人';
      case 'hint': return '提示';
      default: return '';
    }
  }
  
  // 获取联系人图标
  getContactIcon(contact: ContactInfo): string {
    return `<svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
      <circle cx="12" cy="7" r="4"></circle>
    </svg>`;
  }
  
  // 更新级别头部
  updateLevelHeader(items: any[]) {
    const currentLevel = this.getCurrentSearchLevel();
    let levelTitle = '';
    
    const selectedOrgs = this.selectedActions.filter(action => action.type === 'organization');
    const selectedDepts = this.selectedActions.filter(action => action.type === 'department');
    const selectedRoles = this.selectedActions.filter(action => action.type === 'role');
    
    // 确定机构显示文本
    let orgText = '';
    if (selectedOrgs.length === 0) {
      orgText = '';
    } else if (selectedOrgs.length === 1) {
      orgText = selectedOrgs[0].label;
    } else {
      // 多个机构时的处理
      const hasAll = selectedOrgs.some(org => org.id === 'all');
      if (hasAll) {
        orgText = '全部';
      } else {
        orgText = `${selectedOrgs.length}个机构`;
      }
    }
    
    if (currentLevel === 1) {
      levelTitle = '选择机构';
    } else if (currentLevel === 2) {
      levelTitle = orgText ? `${orgText} - 选择部门` : '选择部门';
    } else if (currentLevel === 3) {
      let deptText = selectedDepts.length > 1 ? `${selectedDepts.length}个部门` : selectedDepts[0]?.label || '';
      levelTitle = orgText ? `${orgText} > ${deptText} - 选择岗位` : '选择岗位';
    } else if (currentLevel === 4) {
      let deptText = selectedDepts.length > 1 ? `${selectedDepts.length}个部门` : selectedDepts[0]?.label || '';
      let roleText = selectedRoles.length > 1 ? `${selectedRoles.length}个岗位` : selectedRoles[0]?.label || '';
      
      if (orgText && selectedDepts.length > 0) {
        if (selectedRoles.length > 0) {
          levelTitle = `${orgText} > ${deptText} > ${roleText} - 搜索联系人`;
        } else {
          levelTitle = `${orgText} > ${deptText} - 搜索联系人`;
        }
      } else {
        levelTitle = '搜索联系人';
      }
    }
    
    this.levelTitle.textContent = levelTitle;
    this.levelIndicator.textContent = `${items.length} 项`;
  }
  
  // 添加搜索标签
  addSearchTag(item: any) {
    if (this.selectedActions.find(a => a.id === item.id) || this.isRemoving) return;
    
    // 如果是联系人类型，直接打开联系人笔记
    if (item.type === 'contact') {
      console.log('点击多级搜索联系人:', item.contact?.name);
      this.openContactNote(item.contact);
      return;
    }
    
    // 如果是机构类型，需要处理机构选择逻辑
    if (item.type === 'organization') {
      console.log('添加机构标签:', item.label, '当前机构标签:', this.selectedActions.filter(a => a.type === 'organization').map(a => a.label));
      console.log('当前编辑的标签:', this.currentEditingTag?.label);
      
      // 如果选择的是"全部"，清除所有其他机构标签
      if (item.id === 'all') {
        // 移除其他机构标签，保留部门和岗位标签
        this.selectedActions = this.selectedActions.filter(action => 
          action.type !== 'organization' || action.id === 'all'
        );
        
        // 清除对应的标签元素（除了"全部"）
        const tagsToRemove = this.tagsContainer.querySelectorAll('[data-type="organization"]:not([data-action-id="all"])');
        tagsToRemove.forEach(tag => tag.remove());
      } else {
        // 如果选择的是具体机构，需要判断是否为标签替换操作
        if (this.currentEditingTag && this.currentEditingTag.type === 'organization') {
          // 这是标签替换操作，只替换当前编辑的标签
          console.log('标签替换操作，替换标签:', this.currentEditingTag.label, '为:', item.label);
          
          // 移除当前编辑的标签，但保留"全部"等默认标签
          this.selectedActions = this.selectedActions.filter(action => 
            action.id !== this.currentEditingTag.id
          );
          
          // 移除对应的DOM元素
          const tagToRemove = this.tagsContainer.querySelector(`[data-action-id="${this.currentEditingTag.id}"]`);
          if (tagToRemove) {
            tagToRemove.remove();
          }
          
          // 清除部门和岗位标签（因为机构变了）
          this.selectedActions = this.selectedActions.filter(action => 
            action.type !== 'department' && action.type !== 'role'
          );
          const deptRoleTagsToRemove = this.tagsContainer.querySelectorAll('[data-type="department"], [data-type="role"]');
          deptRoleTagsToRemove.forEach(tag => tag.remove());
        } else {
          // 如果没有当前编辑标签，或者编辑的不是机构标签，则需要保留"全部"等默认标签
          console.log('非标签替换操作，保留默认标签');
          
          // 移除非默认的机构标签，但保留默认标签（如"全部"）
          this.selectedActions = this.selectedActions.filter(action => {
            if (action.type === 'organization' && action.isDefault) {
              return true; // 保留默认机构标签
            }
            return action.type !== 'organization' && action.type !== 'department' && action.type !== 'role';
          });
          
          // 清除对应的标签元素，但保留默认标签
          const tagsToRemove = this.tagsContainer.querySelectorAll('[data-type="organization"]:not([data-action-id="all"]), [data-type="department"], [data-type="role"]');
          tagsToRemove.forEach(tag => tag.remove());
        }
      }
    }
    
    // 如果是部门类型，需要清除已有的岗位标签（因为岗位依赖部门）
    if (item.type === 'department') {
      this.selectedActions = this.selectedActions.filter(action => action.type !== 'role');
      const roleTagsToRemove = this.tagsContainer.querySelectorAll('[data-type="role"]');
      roleTagsToRemove.forEach(tag => tag.remove());
    }
    
    // 添加新标签
    const action = {
      id: item.id,
      label: item.label,
      level: item.level,
      type: item.type
    };
    
    this.selectedActions.push(action);
    const tag = this.createSearchTag(action);
    this.tagsContainer.appendChild(tag);
    
    this.updateInputState();
    this.searchInput.value = '';
    this.keyboardSelectedIndex = -1;
    
    // 清除当前编辑标签，因为已经完成操作
    this.currentEditingTag = null;
    console.log('清除当前编辑标签');
    
    // 重新渲染联系人列表以反映标签筛选结果
    this.renderContacts();
    
    // 保存搜索状态
    this.saveSearchState();
    
    setTimeout(() => {
      if (document.activeElement === this.searchInput) {
        this.isSearchFocused = true;
        console.log('添加标签后，当前级别:', this.getCurrentSearchLevel());
        console.log('当前选中的标签:', this.selectedActions.map(a => `${a.type}:${a.label}`));
        console.log('新添加的标签类型:', item.type);
        
        // 根据新添加的标签类型，智能显示下一级选项
        this.showNextLevelOptions(item.type);
      }
    }, 50);
  }
  
  // 智能显示下一级选项 - 根据当前标签状态决定显示什么级别
  showIntelligentNextLevel() {
    // 快速检查，避免不必要的处理
    if (!this.isSearchFocused) {
      console.log('showIntelligentNextLevel - 搜索框未聚焦，跳过');
      return;
    }
    
    // 检查是否有必要的DOM元素
    if (!this.searchResults || !this.resultsList) {
      console.log('showIntelligentNextLevel - DOM元素未准备好，跳过');
      return;
    }
    
    // 检查是否是通过点击标签触发的
    const isFromTagClick = this.currentEditingTag && 
                          Date.now() - (this.currentEditingTag.lastClickTime || 0) < 1000;
    if (isFromTagClick) {
      return;
    }
    
    // 清空查询
    this.debouncedQuery = '';
    
    // 快速获取标签状态
    const allOrgs = this.selectedActions.filter(action => action.type === 'organization');
    const specificOrgs = allOrgs.filter(action => action.id !== 'all');
    const hasAllOrg = allOrgs.some(action => action.id === 'all');
    const hasDept = this.selectedActions.some(action => action.type === 'department');
    const hasRole = this.selectedActions.some(action => action.type === 'role');
    
    // 决策逻辑：焦点进入时显示下一级选项
    let optionsToShow = [];
    
    if (specificOrgs.length > 0 && !hasDept) {
      // 有具体机构但没有部门，显示该机构的部门
      optionsToShow = this.getDepartmentOptions('');
    } else if (specificOrgs.length > 0 && hasDept && !hasRole) {
      // 有具体机构和部门但没有岗位，显示岗位
      optionsToShow = this.getRoleOptions('');
    } else if (specificOrgs.length > 0 && hasDept && hasRole) {
      // 有具体机构、部门和岗位，显示联系人
      optionsToShow = this.getContactOptions('');
    } else if (hasAllOrg && specificOrgs.length > 0 && !hasDept) {
      // 有"全部"和具体机构，优先显示具体机构的部门
      optionsToShow = this.getDepartmentOptions('');
    } else if (hasAllOrg && !hasDept && !hasRole) {
      // 只有"全部"机构且没有部门和岗位，显示机构选择列表
      optionsToShow = this.getOrganizationOptions('');
    } else if (hasAllOrg && hasDept && !hasRole) {
      // 有"全部"和部门但没有岗位，显示岗位
      optionsToShow = this.getRoleOptions('');
    } else if (hasAllOrg && hasDept && hasRole) {
      // 有"全部"、部门和岗位，显示联系人
      optionsToShow = this.getContactOptions('');
    } else {
      // 其他情况，显示机构列表
      optionsToShow = this.getOrganizationOptions('');
    }
    
    // 异步渲染，避免阻塞UI，使用更平滑的时序
    if (optionsToShow.length > 0) {
      // 分阶段渲染，确保动画流畅
      requestAnimationFrame(() => {
        this.renderMultiLevelResults(optionsToShow);
        this.updateResultsContainerWidth();
        
        // 在下一个动画帧显示结果，确保渲染完成
        requestAnimationFrame(() => {
          this.showMultiLevelResults();
        });
      });
    } else {
      this.hideMultiLevelResults();
    }
  }
  

  
  // 根据添加的标签类型显示下一级选项
  showNextLevelOptions(addedTagType: string) {
    console.log('showNextLevelOptions - 添加的标签类型:', addedTagType);
    console.log('当前selectedActions:', this.selectedActions.map(a => `${a.type}:${a.label}`));
    console.log('当前debouncedQuery:', this.debouncedQuery);
    
    // 清空debouncedQuery以确保获取所有选项
    this.debouncedQuery = '';
    
    if (addedTagType === 'organization') {
      // 如果添加的是机构标签，显示部门选项
      console.log('添加了机构标签，显示部门选项');
      console.log('organizations数据:', this.organizations);
      
      const deptOptions = this.getDepartmentOptions('');
      console.log('获取到的部门选项数量:', deptOptions.length);
      console.log('部门选项详情:', deptOptions.map(opt => opt.label));
      
      this.renderMultiLevelResults(deptOptions);
      this.updateResultsContainerWidth();
      this.showMultiLevelResults();
    } else if (addedTagType === 'department') {
      // 如果添加的是部门标签，显示岗位选项
      console.log('添加了部门标签，显示岗位选项');
      const roleOptions = this.getRoleOptions('');
      console.log('获取到的岗位选项数量:', roleOptions.length);
      console.log('岗位选项详情:', roleOptions.map(opt => opt.label));
      
      this.renderMultiLevelResults(roleOptions);
      this.updateResultsContainerWidth();
      this.showMultiLevelResults();
    } else if (addedTagType === 'role') {
      // 如果添加的是岗位标签，显示联系人选项
      console.log('添加了岗位标签，显示联系人选项');
      const contactOptions = this.getContactOptions('');
      console.log('获取到的联系人选项数量:', contactOptions.length);
      
      this.renderMultiLevelResults(contactOptions);
      this.updateResultsContainerWidth();
      this.showMultiLevelResults();
    } else {
      // 其他情况，使用默认的多级搜索逻辑
      console.log('其他情况，使用默认逻辑');
      this.updateMultiLevelResults();
      this.showMultiLevelResults();
    }
  }
  
  // 创建搜索标签
  createSearchTag(action: any): HTMLElement {
    const tag = document.createElement('div');
    tag.className = `contact-obcm-search--tag contact-obcm-search--level-${action.level}`;
    tag.dataset.actionId = action.id;
    tag.dataset.level = action.level.toString();
    tag.dataset.type = action.type;
    
    // 检查是否为机构标签（一级标签）
    const isOrganizationTag = action.type === 'organization';
    
    // 如果是机构标签，添加特殊类名
    if (isOrganizationTag) {
      tag.classList.add('contact-obcm-search--tag-organization');
    }
    
    // 如果是默认标签，添加特殊类名
    if (action.isDefault) {
      tag.classList.add('contact-obcm-search--tag-default');
    }
    
    const iconHtml = action.type === 'contact' && action.contact ? 
      this.getContactIcon(action.contact) : 
      this.getItemIcon(action);
    
    // 所有标签都可以显示删除按钮，除非是默认标签
    const removeButtonHtml = action.isDefault ? '' : `
      <button class="contact-obcm-search--tag-remove" data-action-id="${action.id}">
        <svg width="12" height="12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    `;
    
    tag.innerHTML = `
      <span class="contact-obcm-search--tag-icon">${iconHtml}</span>
      <span class="contact-obcm-search--tag-label">${action.label}</span>
      ${removeButtonHtml}
    `;
    
    // 添加删除按钮事件（如果存在）
    const removeButton = tag.querySelector('.contact-obcm-search--tag-remove') as HTMLElement;
    if (removeButton) {
      removeButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.removeSearchTag(action.id);
      });
    }
    
    // 添加点击标签显示下拉菜单的功能
    tag.addEventListener('click', (e) => {
      e.stopPropagation();
      // 只有当点击的不是删除按钮时才显示下拉菜单
      if (!e.target || !(e.target as HTMLElement).closest('.contact-obcm-search--tag-remove')) {
        // 禁止"全部"标签被点击
        if (action.id === 'all' && action.type === 'organization') {
          console.log('禁止点击"全部"标签');
          return;
        }
        this.showTagDropdown(action, tag);
      }
    });
    
    return tag;
  }
  
  // 移除搜索标签（异步优化版本）
  removeSearchTag(actionId: string) {
    if (this.isRemoving) return;
    
    const tagElement = this.tagsContainer.querySelector(`[data-action-id="${actionId}"]`) as HTMLElement;
    if (!tagElement) return;
    
    // 找到要移除的标签类型
    const actionToRemove = this.selectedActions.find(a => a.id === actionId);
    if (!actionToRemove) {
      return;
    }
    
    // 防止删除默认标签
    if (actionToRemove.isDefault) {
      console.log('默认标签不能删除:', actionToRemove.label);
      return;
    }
    
    this.isRemoving = true;
    
    // 根据移除的标签类型，确定需要移除的相关标签
    let actionsToRemove = [actionId];
    if (actionToRemove.type === 'organization') {
      // 移除机构时，也要移除所有部门和岗位标签
      const relatedItems = this.selectedActions
        .filter(a => a.type === 'department' || a.type === 'role')
        .map(a => a.id);
      actionsToRemove = actionsToRemove.concat(relatedItems);
    } else if (actionToRemove.type === 'department') {
      // 移除部门时，也要移除相关的岗位标签
      const relatedRoles = this.selectedActions
        .filter(a => a.type === 'role')
        .map(a => a.id);
      actionsToRemove = actionsToRemove.concat(relatedRoles);
    }
    
    // 移除所有相关的标签元素
    const elementsToRemove = actionsToRemove
      .map(id => this.tagsContainer.querySelector(`[data-action-id="${id}"]`) as HTMLElement)
      .filter(element => element !== null);
    
    // 立即开始动画，不等待数据处理
    elementsToRemove.forEach(element => {
      element.addClass('contact-obcm-search--removing');
    });
    
    // 立即更新状态数据，无需等待动画
    this.selectedActions = this.selectedActions.filter(a => !actionsToRemove.includes(a.id));
    
    // 如果删除了机构标签，且没有其他机构标签，则添加默认机构标签
    const hasOrgTag = this.selectedActions.some(action => action.type === 'organization');
    if (!hasOrgTag && actionToRemove.type === 'organization') {
      this.setDefaultOrganizationTag();
    }
    
    // 立即更新输入状态
    this.updateInputState();
    
    // 异步处理重型操作：数据筛选和重新渲染
    this.performAsyncTagRemovalTasks(actionsToRemove);
    
    // 设置定时器移除DOM元素
    setTimeout(() => {
      elementsToRemove.forEach(element => {
        if (element && element.parentNode) {
          element.remove();
        }
      });
      this.isRemoving = false;
    }, 150);
  }
  
  // 异步处理标签删除后的重型任务
  private async performAsyncTagRemovalTasks(actionsToRemove: string[]) {
    // 使用 requestAnimationFrame 确保动画帧之后再执行重型操作
    await new Promise(resolve => requestAnimationFrame(resolve));
    
    try {
      // 异步重新渲染联系人列表
      await this.renderContactsAsync();
      
      // 异步保存搜索状态
      this.saveSearchState();
      
      // 异步更新搜索结果
      await this.updateMultiLevelResultsAsync();
      
    } catch (error) {
      console.error('异步处理标签删除任务时出错:', error);
    }
  }
  
  // 异步渲染联系人列表
  private async renderContactsAsync() {
    return new Promise<void>((resolve) => {
      // 使用 setTimeout 让出主线程，允许动画继续
      setTimeout(() => {
        this.renderContacts();
        resolve();
      }, 0);
    });
  }
  
  // 异步更新多级搜索结果
  private async updateMultiLevelResultsAsync() {
    return new Promise<void>((resolve) => {
      // 使用 setTimeout 让出主线程
      setTimeout(() => {
        // 如果还有选中的标签或有输入内容，继续显示搜索结果
        if (this.selectedActions.length > 0 || this.searchInput.value.trim()) {
          this.updateMultiLevelResults();
        } else {
          this.hideMultiLevelResults();
        }
        resolve();
      }, 0);
    });
  }
  
  // 更新输入状态
  updateInputState() {
    if (this.selectedActions.length > 0) {
      this.searchInput.placeholder = '';
    } else {
      this.searchInput.placeholder = '搜索联系人...';
    }
    
    this.updateContainerWidth();
  }
  
  // 更新容器宽度
  private updateContainerWidth() {
    if (!this.inputContainer) return;
    
    // 不再直接设置宽度，而是通过 CSS 类来控制
    const hasSelectedTags = this.selectedActions.length > 0;
    
    if (hasSelectedTags) {
      this.inputContainer.classList.add('contact-obcm-search--expanded');
    } else {
      this.inputContainer.classList.remove('contact-obcm-search--expanded');
    }
  }
  
  // 更新结果容器宽度
  updateResultsContainerWidth() {
    if (this.inputContainer && this.resultsWrapper) {
      const inputWidth = this.inputContainer.offsetWidth;
      this.resultsWrapper.style.width = `${inputWidth}px`;
      this.resultsWrapper.style.maxWidth = `${inputWidth}px`;
    }
  }
  
  // 显示多级搜索结果
  showMultiLevelResults() {
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }
    
    // 移除隐藏相关的类
    this.searchResults.removeClass('contact-obcm-search--hidden');
    this.searchResults.removeClass('contact-obcm-search--hide');
    
    // 添加ready类以确保下拉菜单正确显示
    this.searchResults.addClass('contact-obcm-search--ready');
  }
  
  // 隐藏多级搜索结果
  hideMultiLevelResults() {
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
    }

    // 移除ready类并添加hide类
    this.searchResults.removeClass('contact-obcm-search--ready');
    this.searchResults.addClass('contact-obcm-search--hide');

    this.hideTimeout = setTimeout(() => {
      this.searchResults.addClass('contact-obcm-search--hidden');
      this.searchResults.removeClass('contact-obcm-search--hide');
      this.hideTimeout = null;
    }, 200);
  }

  // 带动画隐藏菜单（滚动触发）
  hideMenuWithAnimation() {
    if (!this.resultsContainer) return;

    // 使用与hideMultiLevelResults相同的动画
    this.searchResults.removeClass('contact-obcm-search--ready');
    this.searchResults.addClass('contact-obcm-search--hide');

    setTimeout(() => {
      if (this.resultsContainer) {
        this.resultsContainer.style.display = 'none';
      }
      this.searchResults.addClass('contact-obcm-search--hidden');
      this.searchResults.removeClass('contact-obcm-search--hide');
    }, 200);
  }

  // 带动画显示菜单（滚动触发）
  showMenuWithAnimation() {
    if (!this.resultsContainer) return;

    // 恢复显示
    this.resultsContainer.style.display = '';

    // 使用与showMultiLevelResults相同的动画
    this.searchResults.removeClass('contact-obcm-search--hidden');
    this.searchResults.removeClass('contact-obcm-search--hide');
    this.searchResults.addClass('contact-obcm-search--ready');
  }
  
  // 设置默认机构选择（已废弃，保留空实现）
  setDefaultOrganization() {
    // 不再设置默认机构选择，保持搜索框为空状态
  }
  
  // 设置默认标签（"全部"为主，"总行"可选）
  setDefaultOrganizationTag() {
    // 检查是否已经有机构标签，如果有则不添加
    const hasOrgTag = this.selectedActions.some(action => action.type === 'organization');
    if (hasOrgTag) {
      console.log('已存在机构标签，跳过添加默认标签');
      return;
    }
    
    // 双重检查：检查DOM中是否已经存在机构标签
    const existingOrgTags = this.tagsContainer.querySelectorAll('[data-type="organization"]');
    if (existingOrgTags.length > 0) {
      console.log('DOM中已存在机构标签，跳过添加默认标签');
      return;
    }
    
    // 1. 添加"全部"标签（主要默认标签）
    console.log('添加默认"全部"标签');
    const allAction = {
      id: 'all',
      label: '全部',
      level: 1,
      type: 'organization',
      isDefault: true // 标记为默认标签
    };
    
    this.selectedActions.push(allAction);
    const allTag = this.createSearchTag(allAction);
    this.tagsContainer.appendChild(allTag);
    
    // 2. 检查是否有"总行"机构，如果有则也添加（但可以被用户关闭）
    const organizations = Object.keys(this.organizations);
    const totalBranchOrg = organizations.find(org => org === '总行');
    
    if (totalBranchOrg) {
      console.log('添加可选"总行"标签');
      const totalBranchAction = {
        id: totalBranchOrg,
        label: totalBranchOrg,
        level: 1,
        type: 'organization',
        isDefault: false, // 标记为可选标签，用户可以关闭
        isOptional: true // 新增标记，表示这是可选的
      };
      
      this.selectedActions.push(totalBranchAction);
      const totalBranchTag = this.createSearchTag(totalBranchAction);
      this.tagsContainer.appendChild(totalBranchTag);
    }
    
    this.updateInputState();
    
    // 确保在添加默认标签后，能够显示下一级菜单
    console.log('默认标签设置完成，当前级别:', this.getCurrentSearchLevel());
    
    // 如果搜索框已经有焦点，使用防抖版本显示下拉菜单
    if (this.isSearchFocused && this.searchInput && document.activeElement === this.searchInput) {
      console.log('搜索框已有焦点，使用防抖版本显示下拉菜单');
      this.debouncedShowIntelligentNextLevel('defaultTag');
    }
  }
  
  // 显示标签下拉菜单
  showTagDropdown(action: any, tagElement: HTMLElement) {
    console.log('点击标签:', action.label, '类型:', action.type);
    
    // 设置当前正在编辑的标签
    this.currentEditingTag = { ...action, lastClickTime: Date.now() };
    console.log('设置当前编辑标签:', action.label);
    
    // 聚焦到搜索输入框
    this.searchInput.focus();
    this.isSearchFocused = true;
    
    // 清空搜索输入框
    this.searchInput.value = '';
    this.debouncedQuery = '';
    
    // 点击标签时，显示对应级别的选择列表（用于更改该级别的选择）
    if (action.type === 'organization') {
      // 点击机构标签，显示机构选择列表
      console.log('点击机构标签 - 显示机构选择列表');
      console.log('当前organizations数据:', this.organizations);
      const orgOptions = this.getOrganizationOptions('');
      console.log('获取到的机构选项数量:', orgOptions.length);
      console.log('机构选项详情:', orgOptions);
      
      if (orgOptions.length === 0) {
        console.error('机构选项为空！');
        return;
      }
      
      this.renderMultiLevelResults(orgOptions);
      this.updateResultsContainerWidth();
      this.showMultiLevelResults();
      
      // 强制检查菜单是否显示
      setTimeout(() => {
        console.log('菜单显示状态检查:');
        console.log('searchResults类名:', this.searchResults.className);
        console.log('searchResults可见性:', this.searchResults.offsetHeight > 0);
        console.log('resultsList子元素数量:', this.resultsList.children.length);
      }, 100);
    } else if (action.type === 'department') {
      // 点击部门标签，显示部门选择列表
      console.log('点击部门标签 - 显示部门选择列表');
      const deptOptions = this.getDepartmentOptions('');
      this.renderMultiLevelResults(deptOptions);
      this.updateResultsContainerWidth();
      this.showMultiLevelResults();
    } else if (action.type === 'role') {
      // 点击岗位标签，显示岗位选择列表
      console.log('点击岗位标签 - 显示岗位选择列表');
      const roleOptions = this.getRoleOptions('');
      this.renderMultiLevelResults(roleOptions);
      this.updateResultsContainerWidth();
      this.showMultiLevelResults();
    } else {
      // 其他类型标签显示当前级别的选项
      console.log('点击其他标签 - 显示当前级别选项');
      this.updateMultiLevelResults();
      this.showMultiLevelResults();
    }
  }
  
  // 根据选中的标签筛选联系人
  applyTagFilters() {
    // 获取选中的标签
    const selectedOrgs = this.selectedActions.filter(action => action.type === 'organization');
    const selectedDepts = this.selectedActions.filter(action => action.type === 'department');
    const selectedRoles = this.selectedActions.filter(action => action.type === 'role');
    
    // 安全地获取搜索关键词
    const query = this.searchInput?.value?.toLowerCase().trim() || '';
    const isPhoneQuery = /^6\d*$/.test(query);
    
    console.log('开始筛选联系人，总数:', this.contactInfos.length);
    console.log('选中的机构:', selectedOrgs.map(org => org.label));
    console.log('选中的部门:', selectedDepts.map(dept => dept.label));
    
    // 筛选联系人 - 使用与下拉菜单一致的机构筛选逻辑
    this.filteredContacts = this.contactInfos.filter((contact) => {
      // 获取具体的机构标签（排除"全部"）
      const specificOrgs = selectedOrgs.filter(org => org.id !== 'all');
      
      // 机构筛选：只基于具体选择的机构进行筛选
      if (specificOrgs.length > 0) {
        // 如果有具体机构标签，必须匹配其中一个
        const orgMatches = specificOrgs.some(org => contact.branch === org.id);
        console.log(`主窗口机构筛选 - 联系人 ${contact.name}：机构=${contact.branch}, 选中机构=${specificOrgs.map(o => o.id).join(',')}, 结果=${orgMatches}`);
        if (!orgMatches) {
          return false;
        }
      } else {
        // 如果没有具体机构标签（只有"全部"或没有机构标签），显示所有联系人
        console.log(`主窗口机构筛选 - 联系人 ${contact.name}：没有具体机构限制，通过`);
      }
      
      // 部门筛选
      if (selectedDepts.length > 0) {
        const deptMatches = selectedDepts.some(dept => {
          if (dept.id === '未分类') {
            // 对于"未分类"部门，匹配没有部门信息的联系人
            return !contact.department || !contact.department.trim();
          } else {
            return dept.id === contact.department;
          }
        });
        if (!deptMatches) {
          console.log('部门筛选未通过:', contact.name, '部门:', contact.department);
          return false;
        }
      }
      
      // 岗位筛选
      if (selectedRoles.length > 0) {
        const roleMatches = selectedRoles.some(role => role.id === contact.title);
        if (!roleMatches) {
          console.log('岗位筛选未通过:', contact.name, '岗位:', contact.title);
          return false;
        }
      }
      
      // 搜索关键词筛选 - 使用统一的搜索匹配逻辑
      if (query) {
        return this.searchMatchesContact(contact, query);
      }
      
      return true;
    });
    
    console.log('筛选完成，结果数量:', this.filteredContacts.length);
    console.log('筛选后的联系人:', this.filteredContacts.map(c => c.name));
    
    // 检查是否有查询内容且结果唯一
    if (query && this.filteredContacts.length === 1) {
      this.plugin.addRecentSearch(this.filteredContacts[0]);
    }
  }
  
  // 保存搜索状态
  saveSearchState() {
    try {
      const searchState: SearchState = {
        selectedActions: this.selectedActions.map(action => ({
          id: action.id,
          label: action.label,
          level: action.level,
          type: action.type,
          isDefault: action.isDefault
        })),
        searchQuery: this.searchInput?.value || '',
        lastUsed: Date.now(),
        // 保存界面状态
        collapseStates: this.getCollapseStates(),
        scrollPosition: this.getScrollPosition(),
        selectedOrganization: this.selectedOrganization,
        selectedDepartment: this.selectedDepartment,
        selectedRole: this.selectedRole
      };
      
      this.plugin.settings.lastSearchState = searchState;
      this.plugin.saveSettings();
      
      console.log('搜索状态已保存:', searchState);
    } catch (error) {
      console.error('保存搜索状态失败:', error);
    }
  }
  
  // 防抖保存搜索状态
  debouncedSaveSearchState() {
    if (this.saveStateDebounceTimer) {
      clearTimeout(this.saveStateDebounceTimer);
    }
    this.saveStateDebounceTimer = setTimeout(() => {
      this.saveSearchState();
    }, 1000); // 1秒后保存
  }
  
  // 获取折叠状态
  getCollapseStates(): Record<string, boolean> {
    const collapseStates: Record<string, boolean> = {};
    
    // 获取机构折叠状态
    const branchTitles = this.resultContainer.querySelectorAll('.branch-title');
    branchTitles.forEach(title => {
      const branchName = title.querySelector('.branch-name')?.textContent;
      if (branchName) {
        const isCollapsed = title.getAttribute('data-collapsed') === 'true';
        collapseStates[`branch:${branchName}`] = isCollapsed;
      }
    });
    
    // 获取部门折叠状态
    const deptTitles = this.resultContainer.querySelectorAll('.department-title');
    deptTitles.forEach(title => {
      const deptName = title.querySelector('.department-name')?.textContent;
      if (deptName) {
        const isCollapsed = title.getAttribute('data-collapsed') === 'true';
        collapseStates[`dept:${deptName}`] = isCollapsed;
      }
    });
    
    // 获取最近搜索折叠状态
    const recentTitle = this.resultContainer.querySelector('.recent-searches-title');
    if (recentTitle) {
      const isCollapsed = recentTitle.getAttribute('data-collapsed') === 'true';
      collapseStates['recent-searches'] = isCollapsed;
    }
    
    return collapseStates;
  }
  
  // 获取滚动位置
  getScrollPosition(): number {
    return this.contentEl?.scrollTop || 0;
  }
  
  // 恢复折叠状态
  restoreCollapseStates(collapseStates: Record<string, boolean>) {
    if (!collapseStates) return;
    
    // 延迟执行，确保DOM已渲染
    setTimeout(() => {
      Object.entries(collapseStates).forEach(([key, isCollapsed]) => {
        if (key.startsWith('branch:')) {
          const branchName = key.replace('branch:', '');
          const branchTitle = Array.from(this.resultContainer.querySelectorAll('.branch-title'))
            .find(title => title.querySelector('.branch-name')?.textContent === branchName) as HTMLElement;
          
          if (branchTitle && isCollapsed) {
            this.collapseBranch(branchTitle);
          }
        } else if (key.startsWith('dept:')) {
          const deptName = key.replace('dept:', '');
          const deptTitle = Array.from(this.resultContainer.querySelectorAll('.department-title'))
            .find(title => title.querySelector('.department-name')?.textContent === deptName) as HTMLElement;
          
          if (deptTitle && isCollapsed) {
            this.collapseDepartment(deptTitle);
          }
        } else if (key === 'recent-searches') {
          const recentTitle = this.resultContainer.querySelector('.recent-searches-title') as HTMLElement;
          
          if (recentTitle && isCollapsed) {
            this.collapseRecentSearches(recentTitle);
          }
        }
      });
    }, 100);
  }
  
  // 恢复滚动位置
  restoreScrollPosition(scrollPosition: number) {
    if (scrollPosition && this.contentEl) {
      setTimeout(() => {
        this.contentEl.scrollTop = scrollPosition;
      }, 200);
    }
  }
  
  // 折叠机构
  collapseBranch(titleElement: HTMLElement) {
    const contentElement = titleElement.parentElement?.nextElementSibling as HTMLElement;
    if (contentElement) {
      titleElement.setAttribute('data-collapsed', 'true');
      contentElement.style.display = 'none';
      const arrow = titleElement.querySelector('.collapse-arrow') as HTMLElement;
      if (arrow) {
        arrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
        </svg>`;
      }
      titleElement.addClass('collapsed');
    }
  }
  
  // 折叠部门
  collapseDepartment(titleElement: HTMLElement) {
    const contentElement = titleElement.parentElement?.nextElementSibling as HTMLElement;
    if (contentElement) {
      titleElement.setAttribute('data-collapsed', 'true');
      contentElement.style.display = 'none';
      const arrow = titleElement.querySelector('.collapse-arrow') as HTMLElement;
      if (arrow) {
        arrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
        </svg>`;
      }
      titleElement.addClass('collapsed');
    }
  }
  
  // 折叠最近搜索
  collapseRecentSearches(titleElement: HTMLElement) {
    const contentElement = titleElement.parentElement?.nextElementSibling as HTMLElement;
    if (contentElement) {
      titleElement.setAttribute('data-collapsed', 'true');
      contentElement.style.display = 'none';
      const arrow = titleElement.querySelector('.collapse-arrow') as HTMLElement;
      if (arrow) {
        arrow.innerHTML = `<svg width="12" height="12" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
        </svg>`;
      }
      titleElement.addClass('collapsed');
    }
  }
  
  // 恢复搜索状态
  restoreSearchState() {
    try {
      const lastState = this.plugin.settings.lastSearchState;
      
      // 临时强制使用新的默认设置（可以在后续版本中移除）
      // 这是为了确保用户看到新的"全部"+"总行"默认设置
      const forceNewDefaults = true; // 设置为true强制使用新默认设置
      
      if (forceNewDefaults) {
        console.log('强制使用新的默认设置："全部"+"总行"');
        this.setDefaultOrganizationTag();
        return;
      }
      
      // 如果没有保存的状态，设置默认状态
      if (!lastState) {
        this.setDefaultOrganizationTag();
        return;
      }
      
      // 检查状态是否过期（超过7天）
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      if (lastState.lastUsed < sevenDaysAgo) {
        console.log('搜索状态已过期，使用默认状态');
        this.setDefaultOrganizationTag();
        return;
      }
      
      // 清空现有的标签容器，防止重复
      this.tagsContainer.empty();
      this.selectedActions = [];
      
      // 恢复搜索标签
      if (lastState.selectedActions && lastState.selectedActions.length > 0) {
        // 检查保存的状态是否包含我们想要的默认标签
        const hasAllTag = lastState.selectedActions.some(action => action.id === 'all');
        const hasTotalBranchTag = lastState.selectedActions.some(action => action.id === '总行');
        
        // 如果保存的状态不包含"全部"标签，则强制使用新的默认设置
        if (!hasAllTag) {
          console.log('保存的状态缺少"全部"标签，使用新的默认设置');
          this.setDefaultOrganizationTag();
        } else {
          // 恢复保存的标签
          this.selectedActions = [...lastState.selectedActions];
          
          // 重新创建标签元素
          this.selectedActions.forEach(action => {
            const tag = this.createSearchTag(action);
            this.tagsContainer.appendChild(tag);
          });
          
          this.updateInputState();
        }
      } else {
        // 没有保存的标签，使用默认设置
        this.setDefaultOrganizationTag();
      }
      
      // 恢复搜索关键词
      if (lastState.searchQuery && this.searchInput) {
        this.searchInput.value = lastState.searchQuery;
      }
      
      // 恢复传统筛选器状态
      if (lastState.selectedOrganization !== undefined) {
        this.selectedOrganization = lastState.selectedOrganization;
      }
      if (lastState.selectedDepartment !== undefined) {
        this.selectedDepartment = lastState.selectedDepartment;
      }
      if (lastState.selectedRole !== undefined) {
        this.selectedRole = lastState.selectedRole;
      }
      
      // 延迟恢复界面状态，确保DOM已渲染
      setTimeout(() => {
        // 恢复折叠状态
        if (lastState.collapseStates) {
          this.restoreCollapseStates(lastState.collapseStates);
        }
        
        // 恢复滚动位置
        if (lastState.scrollPosition) {
          this.restoreScrollPosition(lastState.scrollPosition);
        }
      }, 300);
      
      console.log('搜索状态已恢复:', lastState);
    } catch (error) {
      console.error('恢复搜索状态失败:', error);
      // 出错时使用默认状态
      this.setDefaultOrganizationTag();
    }
  }

  // 设置 sticky 状态检测和阴影显示
  setupStickyDetection(titleContainer: HTMLElement) {
    // 创建一个哨兵元素来检测 sticky 状态
    const sentinel = document.createElement('div');
    sentinel.style.height = '1px';
    sentinel.style.position = 'absolute';
    sentinel.style.top = '0';
    sentinel.style.left = '0';
    sentinel.style.width = '1px';
    sentinel.style.pointerEvents = 'none';
    sentinel.style.visibility = 'hidden';
    
    // 将哨兵元素插入到部门容器的开始位置
    const departmentContainer = titleContainer.parentElement;
    if (departmentContainer) {
      departmentContainer.insertBefore(sentinel, titleContainer);
    }

    // 使用 IntersectionObserver 观察哨兵元素
    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      entries.forEach(entry => {
        if (entry.target === sentinel) {
          // 当哨兵元素不在视窗内时，说明 sticky 元素正在固定状态
          const isSticky = !entry.isIntersecting;
          
          if (isSticky) {
            // 使用CSS类
            titleContainer.addClass('sticky-active');
          } else {
            // 移除sticky状态类
            titleContainer.removeClass('sticky-active');
          }
        }
      });
    };

    // 创建观察器
    const observer = new IntersectionObserver(observerCallback, {
      root: this.contentEl, // 观察相对于内容容器的交叉情况
      rootMargin: '0px', // 不需要边距
      threshold: [0] // 当元素进入或离开视窗时触发
    });

    // 开始观察哨兵元素
    observer.observe(sentinel);

    // 当模态框关闭时清理观察器和哨兵元素
    const originalClose = this.close.bind(this);
    this.close = () => {
      observer.disconnect();
      if (sentinel.parentElement) {
        sentinel.parentElement.removeChild(sentinel);
      }
      originalClose();
    };
  }

  // 添加防抖版本的showIntelligentNextLevel函数
  debouncedShowIntelligentNextLevel(eventType: string) {
    const now = Date.now();
    
    // 如果距离上次显示菜单的时间太短，清除之前的定时器
    if (this.showMenuDebounceTimer) {
      clearTimeout(this.showMenuDebounceTimer);
      this.showMenuDebounceTimer = null;
    }
    
    // 在初始化阶段，只允许'setup'事件类型的调用
    if (this.isInitializing && eventType !== 'setup') {
      console.log(`${eventType}事件 - 初始化阶段忽略非setup调用`);
      return;
    }
    
    // 如果距离上次显示菜单时间很短（200ms内），忽略这次调用
    if (now - this.lastMenuShowTime < 200) {
      console.log(`${eventType}事件 - 忽略重复调用 showIntelligentNextLevel (距离上次${now - this.lastMenuShowTime}ms)`);
      return;
    }
    
    // 设置防抖定时器
    this.showMenuDebounceTimer = setTimeout(() => {
      console.log(`${eventType}事件 - 防抖后显示下一级选项`);
      this.lastMenuShowTime = Date.now();
      this.showIntelligentNextLevel();
      this.showMenuDebounceTimer = null;
    }, 100); // 增加到100ms防抖延迟，确保各种异步操作都完成
  }

  /**
   * 就地转换草稿卡片为正式联系人卡片
   * @param draftId 草稿ID
   * @param contactPath 正式联系人文件路径
   */
  async convertDraftCardInPlace(draftId: string, contactPath: string) {
    console.log('🔥 [ContactModal] 开始就地转换草稿卡片');
    console.log('🔥 [ContactModal] 草稿ID:', draftId);
    console.log('🔥 [ContactModal] 联系人路径:', contactPath);
    
    try {
      // 🔥 方法1：查找DOM中的草稿卡片
      const draftPath = `draft://${draftId}`;
      console.log('🔥 [ContactModal] 查找草稿路径:', draftPath);
      
      // 在当前显示的最近搜索区域中查找草稿卡片
      const recentSearchSection = this.resultContainer?.querySelector('.contact-obcm--recent-searches-section');
      if (!recentSearchSection) {
        console.log('🔥 [ContactModal] 未找到最近搜索区域，执行完全刷新');
        await this.plugin.refreshContactCache();
        this.renderContacts();
        return;
      }
      
      // 查找草稿卡片元素（通过data属性或其他标识）
      const draftCards = Array.from(recentSearchSection.querySelectorAll('[data-contact-path]'));
      let draftCardElement: HTMLElement | null = null;
      
      for (const card of draftCards) {
        const cardPath = (card as HTMLElement).getAttribute('data-contact-path');
        if (cardPath === draftPath) {
          draftCardElement = card as HTMLElement;
          break;
        }
      }
      
      if (draftCardElement) {
        console.log('🔥 ✅ [ContactModal] 找到草稿卡片，开始就地转换');
        
        // 🔥 关键：刷新缓存以获取新创建的联系人信息
        await this.plugin.refreshContactCache();
        
        // 查找新创建的联系人信息
        const newContact = this.plugin.contactCache.infos.find(info => info.path === contactPath);
        if (newContact) {
          console.log('🔥 [ContactModal] 找到新联系人信息:', newContact.name);
          
          // 🔥 关键：就地替换草稿卡片
          this.replaceDraftCardWithContact(draftCardElement, newContact);
          
          console.log('🔥 ✅ [ContactModal] 草稿卡片就地转换完成');
        } else {
          console.warn('🔥 ⚠️ [ContactModal] 未找到新联系人信息，执行完全刷新');
          this.renderContacts();
        }
      } else {
        console.log('🔥 [ContactModal] 未找到草稿卡片，执行完全刷新');
        await this.plugin.refreshContactCache();
        this.renderContacts();
      }
      
    } catch (error) {
      console.error('🔥 ❌ [ContactModal] 草稿转换过程出错:', error);
      // 出错时回退到完全刷新
      await this.plugin.refreshContactCache();
      this.renderContacts();
    }
  }

  /**
   * 替换草稿卡片为正式联系人卡片
   * @param draftCardElement 草稿卡片DOM元素
   * @param contact 新的联系人信息
   */
  private replaceDraftCardWithContact(draftCardElement: HTMLElement, contact: ContactInfo) {
    console.log('🔥 [ContactModal] 开始替换卡片DOM');
    
    try {
      // 创建新的联系人卡片容器
      const newCardContainer = document.createElement('div');
      
      // 🔥 使用现有的createContactCard方法创建正式联系人卡片
      this.createContactCard(newCardContainer, contact, true);
      
      // 获取新创建的卡片元素
      const newCardElement = newCardContainer.firstElementChild as HTMLElement;
      
      if (newCardElement) {
        // 🔥 关键：保持原有的动画类和样式，平滑过渡
        newCardElement.style.opacity = '0';
        newCardElement.style.transform = 'scale(0.95)';
        
        // 插入新卡片到草稿卡片之前
        draftCardElement.parentNode?.insertBefore(newCardElement, draftCardElement);
        
        // 🔥 添加转换动画
        requestAnimationFrame(() => {
          // 淡入新卡片
          newCardElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
          newCardElement.style.opacity = '1';
          newCardElement.style.transform = 'scale(1)';
          
          // 同时淡出草稿卡片
          draftCardElement.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
          draftCardElement.style.opacity = '0';
          draftCardElement.style.transform = 'scale(0.95)';
          
          // 动画完成后移除草稿卡片
          setTimeout(() => {
            draftCardElement.remove();
            console.log('🔥 ✅ [ContactModal] 草稿卡片已移除，转换完成');
          }, 300);
        });
        
        console.log('🔥 ✅ [ContactModal] 卡片替换完成');
      } else {
        console.error('🔥 ❌ [ContactModal] 创建新卡片失败');
        // 回退：直接移除草稿卡片并重新渲染
        draftCardElement.remove();
        this.renderContacts();
      }
      
    } catch (error) {
      console.error('🔥 ❌ [ContactModal] 替换卡片过程出错:', error);
      // 出错时直接移除草稿卡片并重新渲染
      draftCardElement.remove();
      this.renderContacts();
    }
  }

} 