# 搜索框动画实现说明

## 概述
参考 `clone.html` 页面的 Airbnb 风格搜索框动画效果，成功实现了插件主窗口搜索框的放大缩小动画。

## 实现的动画效果

### 1. 滚动触发动画
- **滚动阈值**: 50px（参考 clone.html 的敏感响应）
- **触发条件**: 当页面向下滚动超过 50px 时，搜索框进入紧凑模式
- **恢复条件**: 当滚动位置回到 50px 以内时，搜索框恢复正常模式

### 2. 尺寸变化动画
- **正常状态**: 
  - 宽度: 850px
  - 高度: 66px
  - 圆角: 32px
- **紧凑状态**:
  - 宽度: 300px
  - 高度: 46px
  - 圆角: 24px
  - 缩放: scale(0.35)

### 3. 位置变化动画
- **正常状态**: 相对定位，位于搜索容器内
- **紧凑状态**: 固定定位，居中显示（top: 50%, left: 50%）

### 4. 内容变化动画
- **正常状态**: 显示完整的搜索输入框和标签
- **紧凑状态**: 
  - 隐藏搜索输入框和标签（opacity: 0, scale: 0.9）
  - 显示简化文本"搜索联系人"（通过 ::before 伪元素）

### 5. 图标变化动画
- **正常状态**: 
  - 图标容器: 48px × 48px
  - SVG 图标: 16px × 16px
- **紧凑状态**:
  - 图标容器: 30px × 30px
  - SVG 图标: 12px × 12px

### 6. 悬停效果
- **紧凑状态悬停**: scale(0.38) - 微妙的放大效果
- **正常状态悬停**: 轻微上移和阴影增强

## 技术实现

### CSS 关键样式
```css
/* 主要过渡效果 */
transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);

/* 滚动状态样式 */
.contact-obcm-search--input-container.contact-obcm-search--scrolled {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.35);
  width: 300px;
  height: 46px;
  border-radius: 24px;
}

/* 悬停效果 */
.contact-obcm-search--input-container.contact-obcm-search--scrolled:hover {
  transform: translate(-50%, -50%) scale(0.38);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
```

### JavaScript 关键逻辑
```javascript
// 滚动阈值设置
const scrollThreshold = 50;

// 滚动处理函数
private handleScrollAnimation() {
  const scrollTop = this.contentEl.scrollTop;
  
  if (scrollTop > scrollThreshold) {
    this.inputContainer.classList.add('contact-obcm-search--scrolled');
  } else {
    this.inputContainer.classList.remove('contact-obcm-search--scrolled');
  }
}

// 悬停效果
this.inputContainer.addEventListener('mouseenter', () => {
  if (this.inputContainer.classList.contains('contact-obcm-search--scrolled')) {
    this.inputContainer.style.transform = 'translate(-50%, -50%) scale(0.38)';
  }
});
```

## 动画特点

### 1. 流畅的缓动函数
- 主要动画: `cubic-bezier(0.16, 1, 0.3, 1)` - 提供自然的弹性效果
- 悬停动画: `cubic-bezier(0.25, 0.46, 0.45, 0.94)` - 快速响应

### 2. 分层动画
- 搜索框容器的位置和尺寸变化
- 内部内容的透明度和缩放变化
- 图标的尺寸变化
- 所有动画同步进行，创造流畅的视觉效果

### 3. 交互反馈
- 点击紧凑搜索框可回到顶部
- 悬停时的微妙缩放反馈
- 平滑的滚动触发响应

### 4. 性能优化
- 使用 `requestAnimationFrame` 优化滚动监听
- CSS `transform` 和 `opacity` 属性确保硬件加速
- 防抖处理避免过度触发

## 文件修改清单

### 1. styles.css
- 更新搜索框尺寸参数（参考 clone.html）
- 优化缓动函数
- 添加悬停效果样式
- 统一滚动状态样式定义

### 2. modal.ts
- 调整滚动阈值为 50px
- 优化悬停事件处理
- 增强滚动动画逻辑
- 添加调试日志

### 3. 测试文件
- 创建 `test-search-animation.html` 用于验证动画效果

## 效果对比

### 原始效果
- 滚动阈值较大（270px）
- 动画较为生硬
- 缺少悬停反馈

### 优化后效果
- 敏感的滚动响应（50px）
- 流畅的 Airbnb 风格动画
- 丰富的交互反馈
- 精确的尺寸控制

## 总结
成功实现了参考 clone.html 的搜索框放大缩小动画效果，提供了流畅、自然的用户体验。动画效果包括尺寸变化、位置变化、内容变化和交互反馈，完全符合现代 Web 应用的设计标准。
