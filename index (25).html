<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Level Action Search Bar</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #fff;
            color: #111;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        @media (prefers-color-scheme: dark) {
            body {
                background: #000;
                color: #fff;
            }
        }

        .contact-obcm-add--container {
            width: 100%;
            max-width: 64rem;
            margin: 0 auto;
        }

        .contact-obcm-add--search-wrapper {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 300px;
        }

        .contact-obcm-add--input-container {
            display: flex;
            align-items: center;
            width: 400px;
            min-width: 400px;
            max-width: 90vw;
            min-height: 44px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            padding: 8px;
            background: #fff;
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .contact-obcm-add--input-container:focus-within {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        @media (prefers-color-scheme: dark) {
            .contact-obcm-add--input-container {
                border-color: #374151;
                background: #000;
            }
        }

        .contact-obcm-add--tags-input-wrapper {
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            min-height: 36px;
            padding: 4px 0;
            overflow: hidden;
        }

        .contact-obcm-add--tags-container {
            display: flex;
            align-items: center;
            gap: 4px;
            flex-shrink: 0;
        }

        .contact-obcm-add--tag {
            display: flex;
            align-items: center;
            gap: 4px;
            height: 28px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            animation: contact-obcm-add--tagSlideIn 0.3s ease-out;
            transition: all 0.3s ease;
        }

        .contact-obcm-add--tag.contact-obcm-add--level-1 {
            background: #dbeafe;
            color: #1e40af;
        }

        .contact-obcm-add--tag.contact-obcm-add--level-2 {
            background: #fef08a;
            color: #92400e;
        }

        .contact-obcm-add--tag.contact-obcm-add--level-3 {
            background: #dcfce7;
            color: #166534;
        }

        @media (prefers-color-scheme: dark) {
            .contact-obcm-add--tag.contact-obcm-add--level-1 {
                background: #1e3a8a;
                color: #bfdbfe;
            }
            .contact-obcm-add--tag.contact-obcm-add--level-2 {
                background: #92400e;
                color: #fef08a;
            }
            .contact-obcm-add--tag.contact-obcm-add--level-3 {
                background: #166534;
                color: #dcfce7;
            }
        }

        .contact-obcm-add--tag.contact-obcm-add--removing {
            animation: contact-obcm-add--tagSlideOut 0.4s ease-out forwards;
        }

        .contact-obcm-add--tag-icon {
            width: 14px;
            height: 14px;
            opacity: 0.7;
        }

        .contact-obcm-add--tag-remove {
            width: 14px;
            height: 14px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s ease;
        }

        .contact-obcm-add--tag-remove:hover {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            transform: scale(1.1);
        }

        .contact-obcm-add--search-input {
            flex: 1;
            min-width: 80px;
            height: 28px;
            border: none;
            outline: none;
            background: transparent;
            font-size: 14px;
            color: inherit;
        }

        .contact-obcm-add--search-input::placeholder {
            color: #6b7280;
        }

        .contact-obcm-add--icon-button {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
            border: none;
            background: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.15s ease;
        }

        .contact-obcm-add--icon-button:hover {
            background: #f3f4f6;
        }

        @media (prefers-color-scheme: dark) {
            .contact-obcm-add--icon-button:hover {
                background: #1f2937;
            }
        }

        .contact-obcm-add--icon {
            width: 16px;
            height: 16px;
            stroke: #9ca3af;
            transition: all 0.15s ease;
        }

        .contact-obcm-add--results-container {
            position: relative;
            z-index: 50;
            display: flex;
            justify-content: center;
            margin-top: 4px;
        }
        
        .contact-obcm-add--results-wrapper {
            position: relative;
            width: 100%;
            transition: width 0.3s ease;
            transform-origin: center;
        }
        
        .contact-obcm-add--search-results {
            position: absolute;
            top: 4px;
            left: 0;
            right: 0;
            z-index: 50;
            max-height: 320px;
            overflow-y: auto;
            background: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            animation: contact-obcm-add--slideDown 0.25s ease-out;
            scroll-behavior: auto;
        }

        @media (prefers-color-scheme: dark) {
            .contact-obcm-add--search-results {
                background: #000;
                border-color: #374151;
            }
        }

        .contact-obcm-add--search-results.contact-obcm-add--hidden {
            display: none;
        }

        .contact-obcm-add--search-results.contact-obcm-add--hide {
            animation: contact-obcm-add--slideUp 0.2s ease-out;
        }

        .contact-obcm-add--level-header {
            position: sticky;
            top: 0;
            z-index: 20;
            padding: 8px 12px;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        @media (prefers-color-scheme: dark) {
            .contact-obcm-add--level-header {
                background: #111827;
                border-bottom-color: #374151;
            }
        }

        .contact-obcm-add--level-title {
            font-size: 12px;
            font-weight: 500;
            color: #6b7280;
        }

        .contact-obcm-add--level-indicator {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            background: #f3f4f6;
            color: #4b5563;
            font-weight: 500;
        }

        @media (prefers-color-scheme: dark) {
            .contact-obcm-add--level-indicator {
                background: #1f2937;
                color: #9ca3af;
            }
        }

        .contact-obcm-add--results-list {
            list-style: none;
        }

        .contact-obcm-add--search-item {
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.15s ease;
            animation: contact-obcm-add--fadeInUp 0.2s ease-out;
            animation-fill-mode: both;
        }

        .contact-obcm-add--search-item:hover {
            background: #f3f4f6;
            transform: translateY(-1px);
        }

        @media (prefers-color-scheme: dark) {
            .contact-obcm-add--search-item:hover {
                background: #1f2937;
            }
        }

        .contact-obcm-add--search-item.contact-obcm-add--keyboard-selected {
            background: #3b82f6 !important;
            color: #fff !important;
        }

        .contact-obcm-add--search-item.contact-obcm-add--keyboard-selected * {
            color: #fff !important;
        }

        .contact-obcm-add--item-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .contact-obcm-add--item-left {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            min-width: 0;
        }

        .contact-obcm-add--item-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .contact-obcm-add--item-label {
            font-size: 14px;
            font-weight: 500;
            color: inherit;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .contact-obcm-add--item-description {
            font-size: 12px;
            color: #6b7280;
            flex-shrink: 0;
        }

        .contact-obcm-add--item-right {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }

        .contact-obcm-add--item-meta {
            font-size: 12px;
            color: #9ca3af;
        }

        /* Scrollbar styling */
        .contact-obcm-add--search-results::-webkit-scrollbar {
            width: 8px;
        }

        .contact-obcm-add--search-results::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .contact-obcm-add--search-results::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .contact-obcm-add--search-results::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        @media (prefers-color-scheme: dark) {
            .contact-obcm-add--search-results::-webkit-scrollbar-track {
                background: #0f172a;
            }
            .contact-obcm-add--search-results::-webkit-scrollbar-thumb {
                background: #334155;
            }
            .contact-obcm-add--search-results::-webkit-scrollbar-thumb:hover {
                background: #475569;
            }
        }

        /* Animations */
        @keyframes contact-obcm-add--slideDown {
            from {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes contact-obcm-add--slideUp {
            from {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
            to {
                opacity: 0;
                transform: translateY(-8px) scale(0.95);
            }
        }

        @keyframes contact-obcm-add--fadeInUp {
            from {
                opacity: 0;
                transform: translateY(8px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes contact-obcm-add--tagSlideIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes contact-obcm-add--tagSlideOut {
            from {
                opacity: 1;
                transform: scale(1);
            }
            to {
                opacity: 0;
                transform: scale(0.8);
            }
        }
        
        /* Responsive */
        @media (max-width: 640px) {
            .contact-obcm-add--input-container {
                width: 320px;
                min-width: 320px;
                max-width: 95vw;
            }
            .contact-obcm-add--search-input {
                min-width: 60px;
            }
            .contact-obcm-add--search-results {
                max-height: 250px;
            }
            .contact-obcm-add--icon-button {
                width: 24px;
                height: 24px;
            }
            .contact-obcm-add--icon {
                width: 14px;
                height: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="contact-obcm-add--container">
        <div class="contact-obcm-add--search-wrapper">
            <div class="contact-obcm-add--input-container">
                <div class="contact-obcm-add--tags-input-wrapper">
                    <div class="contact-obcm-add--tags-container" id="tags-container"></div>
                            <input
                                type="text"
                                id="search"
                                placeholder="What's up?"
                        class="contact-obcm-add--search-input"
                            />
                        </div>
                <button class="contact-obcm-add--icon-button" id="action-button">
                    <svg id="search-icon" class="contact-obcm-add--icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                    <svg id="send-icon" class="contact-obcm-add--icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="display: none;">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                </button>
                </div>
                
            <div class="contact-obcm-add--results-container">
                <div class="contact-obcm-add--results-wrapper">
                    <div id="search-results" class="contact-obcm-add--search-results contact-obcm-add--hidden">
                        <div class="contact-obcm-add--level-header">
                            <span id="level-title" class="contact-obcm-add--level-title">Level 1 - Main Categories</span>
                            <span class="contact-obcm-add--level-indicator" id="level-indicator">7 items</span>
                                </div>
                        <ul class="contact-obcm-add--results-list" id="results-list"></ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class SearchBar {
            constructor() {
                this.elements = {
                    searchInput: document.getElementById('search'),
                    searchResults: document.getElementById('search-results'),
                    resultsList: document.getElementById('results-list'),
                    searchIcon: document.getElementById('search-icon'),
                    sendIcon: document.getElementById('send-icon'),
                    tagsContainer: document.getElementById('tags-container'),
                    levelTitle: document.getElementById('level-title'),
                    levelIndicator: document.getElementById('level-indicator'),
                    inputContainer: document.querySelector('.contact-obcm-add--input-container'),
                    resultsWrapper: document.querySelector('.contact-obcm-add--results-wrapper')
                };

                this.state = {
                    isFocused: false,
                    selectedActions: [],
                    isMouseDownOnResults: false,
                    hideTimeout: null,
                    isRemoving: false,
                    debouncedQuery: '',
                    keyboardSelectedIndex: -1
                };

                this.actions = this.generateActions();
                this.init();
            }

            generateActions() {
            const actions = [];
                const categories = ['a', 'b', 'c', 'd', 'e', 'f', 'g'];
            
                categories.forEach(cat => {
                actions.push({
                        id: cat,
                        label: `Category ${cat.toUpperCase()}`,
                        description: 'Main category',
                    level: 1,
                    dependencies: []
                });
                
                for (let i = 1; i <= 10; i++) {
                        const subId = `${cat}${i}`;
                    actions.push({
                        id: subId,
                            label: `${cat.toUpperCase()}${i} Option`,
                            description: 'Sub option',
                        level: 2,
                            dependencies: [cat]
                    });
                    
                    for (let j = 1; j <= 20; j++) {
                        actions.push({
                                id: `${cat}${i}-${j}`,
                                label: `${cat.toUpperCase()}${i}-${j} Item`,
                                description: 'Detail item',
                            level: 3,
                            dependencies: [subId]
                        });
                    }
                }
            });
            
            return actions;
        }

            init() {
                this.bindEvents();
                this.updateIcon(false);
                this.updateInputState();
            }

            bindEvents() {
                const { searchInput, searchResults } = this.elements;

                searchInput.addEventListener('input', this.debounce((e) => {
                    const value = e.target.value;
                    this.updateIcon(value.length > 0);
                    this.state.debouncedQuery = value;
                    this.updateResults();
                }, 150));

                searchInput.addEventListener('focus', () => {
                    this.state.isFocused = true;
                    this.updateResults();
                });

                searchInput.addEventListener('blur', () => {
                    if (!this.state.isMouseDownOnResults) {
                        setTimeout(() => {
                            if (document.activeElement !== searchInput) {
                                this.state.isFocused = false;
                                this.hideResults();
                            }
        }, 150);
                    }
                });

                searchInput.addEventListener('keydown', (e) => this.handleKeydown(e));

                searchResults.addEventListener('mousedown', () => {
                    this.state.isMouseDownOnResults = true;
                });

                searchResults.addEventListener('mouseup', () => {
                    this.state.isMouseDownOnResults = false;
                });

                document.addEventListener('mouseup', () => {
                    this.state.isMouseDownOnResults = false;
                });

                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        searchInput.blur();
                        this.hideResults();
                        this.state.keyboardSelectedIndex = -1;
                    }
                    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                        e.preventDefault();
                        searchInput.focus();
                    }
                });

                window.addEventListener('resize', this.debounce(() => {
                    this.updateContainerWidth();
                }, 100));
            }

            debounce(func, delay) {
                let timeoutId;
                return (...args) => {
                    clearTimeout(timeoutId);
                    timeoutId = setTimeout(() => func.apply(this, args), delay);
                };
            }

            handleKeydown(e) {
                const items = this.elements.resultsList.querySelectorAll('.contact-obcm-add--search-item');
                
                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    if (items.length > 0) {
                        this.updateKeyboardSelection(Math.min(this.state.keyboardSelectedIndex + 1, items.length - 1));
                    }
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    if (items.length > 0) {
                        this.updateKeyboardSelection(Math.max(this.state.keyboardSelectedIndex - 1, 0));
                    }
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (this.state.keyboardSelectedIndex >= 0 && this.state.keyboardSelectedIndex < items.length) {
                        const selectedItem = items[this.state.keyboardSelectedIndex];
                        const actionIndex = parseInt(selectedItem.dataset.index);
                        const filteredActions = this.filterActions();
                        if (filteredActions[actionIndex]) {
                            this.addTag(filteredActions[actionIndex]);
                        }
                    }
                } else if (e.key === 'Backspace' && this.elements.searchInput.value === '' && 
                          this.state.selectedActions.length > 0 && !this.state.isRemoving) {
                    const lastAction = this.state.selectedActions[this.state.selectedActions.length - 1];
                    this.removeTag(lastAction.id);
                }
            }

            updateKeyboardSelection(newIndex) {
                const items = this.elements.resultsList.querySelectorAll('.contact-obcm-add--search-item');
                
                items.forEach(item => item.classList.remove('contact-obcm-add--keyboard-selected'));
                
                if (newIndex >= 0 && newIndex < items.length) {
                    this.state.keyboardSelectedIndex = newIndex;
                    items[newIndex].classList.add('contact-obcm-add--keyboard-selected');
                } else {
                    this.state.keyboardSelectedIndex = -1;
                }
            }

            getCurrentLevel() {
                if (this.state.selectedActions.length === 0) return 1;
                
                const lastAction = this.state.selectedActions[this.state.selectedActions.length - 1];
                const lastActionData = this.actions.find(a => a.id === lastAction.id);
            
            if (lastActionData) {
                if (lastActionData.level === 3) return 3;
                return lastActionData.level + 1;
            }
            
            return 1;
        }

            getCurrentParentDependencies() {
                const currentLevel = this.getCurrentLevel();
            
            if (currentLevel === 1) return [];
            
            if (currentLevel === 2) {
                    return this.state.selectedActions
                    .filter(action => {
                            const actionData = this.actions.find(a => a.id === action.id);
                        return actionData && actionData.level === 1;
                    })
                    .map(action => action.id);
            }
            
            if (currentLevel === 3) {
                    return this.state.selectedActions
                    .filter(action => {
                            const actionData = this.actions.find(a => a.id === action.id);
                        return actionData && actionData.level === 2;
                    })
                    .map(action => action.id);
            }
            
            return [];
        }

            filterActions() {
                const currentLevel = this.getCurrentLevel();
                const parentDependencies = this.getCurrentParentDependencies();
                
                let availableActions = this.actions.filter(action => {
                    if (action.level !== currentLevel) return false;
                    if (this.state.selectedActions.find(selected => selected.id === action.id)) return false;
                    
                    if (currentLevel === 1) {
                        return action.dependencies.length === 0;
                    } else {
                        return action.dependencies.some(depId => parentDependencies.includes(depId));
                    }
                });

                if (this.state.debouncedQuery && this.state.debouncedQuery.trim()) {
                    const normalizedQuery = this.state.debouncedQuery.toLowerCase().trim();
                    availableActions = availableActions.filter(action => 
                        action.label.toLowerCase().includes(normalizedQuery) ||
                        action.id.toLowerCase().includes(normalizedQuery)
                    );
                }
                
                return availableActions;
            }

            createTag(action) {
                const tag = document.createElement('div');
                tag.className = `contact-obcm-add--tag contact-obcm-add--level-${action.level}`;
                tag.dataset.actionId = action.id;
                tag.dataset.level = action.level;
                
                tag.innerHTML = `
                    <span class="contact-obcm-add--tag-icon">${this.getIcon(action.level)}</span>
                    <span>${action.label}</span>
                    <button class="contact-obcm-add--tag-remove" onclick="searchBar.removeTag('${action.id}')">
                        <svg width="12" height="12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                `;
                
                return tag;
            }

            getIcon(level) {
                const icons = {
                    1: '<svg width="14" height="14" fill="currentColor" viewBox="0 0 24 24"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>',
                    2: '<svg width="14" height="14" fill="currentColor" viewBox="0 0 24 24"><path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/></svg>',
                    3: '<svg width="14" height="14" fill="currentColor" viewBox="0 0 24 24"><path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/></svg>'
                };
                return icons[level] || icons[1];
            }

            addTag(action) {
                if (this.state.selectedActions.find(a => a.id === action.id) || this.state.isRemoving) return;
                
                this.state.selectedActions.push(action);
                const tag = this.createTag(action);
                this.elements.tagsContainer.appendChild(tag);
                
                this.updateInputState();
                this.elements.searchInput.value = '';
                this.updateIcon(false);
                this.state.keyboardSelectedIndex = -1;
                
                                setTimeout(() => {
                    if (document.activeElement === this.elements.searchInput) {
                        this.state.isFocused = true;
                        this.updateResults();
                    }
                }, 50);
            }

            removeTag(actionId) {
                if (this.state.isRemoving) return;
                
                const tagElement = this.elements.tagsContainer.querySelector(`[data-action-id="${actionId}"]`);
                if (!tagElement) return;
                
                this.state.isRemoving = true;
                
                const dependentActionIds = this.findDependentActions(actionId);
                const actionsToRemove = [actionId, ...dependentActionIds];
                
                const elementsToRemove = actionsToRemove
                    .map(id => this.elements.tagsContainer.querySelector(`[data-action-id="${id}"]`))
                    .filter(element => element !== null);
                
                elementsToRemove.forEach(element => {
                    element.classList.add('contact-obcm-add--removing');
                    setTimeout(() => {
                        if (element && element.parentNode) {
                            element.remove();
                        }
                    }, 400);
                });
                
                setTimeout(() => {
                    this.state.selectedActions = this.state.selectedActions.filter(a => !actionsToRemove.includes(a.id));
                    this.updateInputState();
                    this.updateResults();
                    this.state.isRemoving = false;
                }, 400);
            }

            findDependentActions(actionId) {
                const dependents = [];
                
                const findRecursive = (id) => {
                    this.actions.forEach(action => {
                        if (action.dependencies.includes(id)) {
                            dependents.push(action.id);
                            findRecursive(action.id);
                        }
                    });
                };
                
                findRecursive(actionId);
                return dependents;
            }

            updateResults() {
                if (!this.state.isFocused || this.state.isRemoving) return;
                
                const filteredActions = this.filterActions();
                this.renderResults(filteredActions);
                this.updateResultsContainerWidth();
                this.showResults();
            }

            renderResults(actions) {
                this.elements.resultsList.innerHTML = '';
                this.state.keyboardSelectedIndex = -1;
                const currentLevel = this.getCurrentLevel();
                
                this.updateLevelHeader(actions, currentLevel);
            
            if (actions.length === 0) {
                const li = document.createElement('li');
                    li.className = 'contact-obcm-add--search-item';
                    li.style.textAlign = 'center';
                    li.style.color = '#6b7280';
                li.innerHTML = currentLevel > 1 ? 
                    'No more options available at this level' : 
                    'No available options';
                    this.elements.resultsList.appendChild(li);
                return;
            }
            
            actions.forEach((action, index) => {
                const li = document.createElement('li');
                    li.className = 'contact-obcm-add--search-item';
                li.style.animationDelay = `${index * 0.03}s`;
                li.dataset.index = index;
                
                li.innerHTML = `
                        <div class="contact-obcm-add--item-content">
                            <div class="contact-obcm-add--item-left">
                                <span class="contact-obcm-add--item-icon">${this.getIcon(action.level)}</span>
                                <span class="contact-obcm-add--item-label">${action.label}</span>
                                <span class="contact-obcm-add--item-description">${action.description}</span>
                        </div>
                            <div class="contact-obcm-add--item-right">
                                <span class="contact-obcm-add--item-meta">Level ${action.level}</span>
                        </div>
                    </div>
                `;
                
                li.addEventListener('mousedown', (e) => {
                    e.preventDefault();
                        this.addTag(action);
                });
                
                li.addEventListener('mouseenter', () => {
                        const items = this.elements.resultsList.querySelectorAll('.contact-obcm-add--search-item');
                        items.forEach(item => item.classList.remove('contact-obcm-add--keyboard-selected'));
                        this.state.keyboardSelectedIndex = -1;
                    });
                    
                    this.elements.resultsList.appendChild(li);
                });
            }

            updateLevelHeader(actions, currentLevel) {
                const levelNames = {
                    1: 'Level 1 - Main Categories',
                    2: 'Level 2 - Sub Options',
                    3: 'Level 3 - Detail Items'
                };
                
                this.elements.levelTitle.textContent = levelNames[currentLevel] || `Level ${currentLevel}`;
                this.elements.levelIndicator.textContent = `${actions.length} items`;
            }

            updateContainerWidth() {
                const tagsWidth = this.elements.tagsContainer.scrollWidth;
                const inputMinWidth = 80;
                const iconWidth = 36;
                const padding = 16;
                
                const baseWidth = 400;
                const contentWidth = tagsWidth + inputMinWidth + iconWidth + padding;
                const totalMinWidth = Math.max(baseWidth, contentWidth);
                const maxWidth = window.innerWidth * 0.9;
                
                const finalWidth = Math.min(totalMinWidth, maxWidth);
                
                this.elements.inputContainer.style.width = `${finalWidth}px`;
                this.elements.inputContainer.style.minWidth = `${finalWidth}px`;
                
                this.updateResultsContainerWidth();
            }

            updateResultsContainerWidth() {
                if (this.elements.inputContainer && this.elements.resultsWrapper) {
                    const inputWidth = this.elements.inputContainer.offsetWidth;
                    this.elements.resultsWrapper.style.width = `${inputWidth}px`;
                    this.elements.resultsWrapper.style.maxWidth = `${inputWidth}px`;
                }
            }

            updateInputState() {
                if (this.state.selectedActions.length > 0) {
                    this.elements.searchInput.placeholder = '';
            } else {
                    this.elements.searchInput.placeholder = "What's up?";
                }
                
                this.updateContainerWidth();
            }

            updateIcon(hasValue) {
                if (hasValue) {
                    this.elements.searchIcon.style.display = 'none';
                    this.elements.sendIcon.style.display = 'block';
            } else {
                    this.elements.searchIcon.style.display = 'block';
                    this.elements.sendIcon.style.display = 'none';
                }
            }

            showResults() {
                if (this.state.hideTimeout) {
                    clearTimeout(this.state.hideTimeout);
                    this.state.hideTimeout = null;
                }
                
                this.elements.searchResults.classList.remove('contact-obcm-add--hidden');
                this.elements.searchResults.classList.remove('contact-obcm-add--hide');
            }

            hideResults() {
                if (this.state.hideTimeout) {
                    clearTimeout(this.state.hideTimeout);
                }
                
                this.elements.searchResults.classList.add('contact-obcm-add--hide');
                this.state.hideTimeout = setTimeout(() => {
                    this.elements.searchResults.classList.add('contact-obcm-add--hidden');
                    this.elements.searchResults.classList.remove('contact-obcm-add--hide');
                    this.state.hideTimeout = null;
                }, 200);
            }
        }

        // Initialize the search bar
        const searchBar = new SearchBar();
    </script>
</body>
</html>