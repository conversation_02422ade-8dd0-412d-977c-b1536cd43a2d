<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Airbnb风格布局 - 精简版</title>
  <style>
    :root {
      --header-large-height: 160px;
      --header-compact-height: 70px;
      --brand-color: #FF385C;
      --horizontal-padding: 80px;
      --search-large-height: 66px;
      --search-compact-height: 46px;
    }
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: #222;
      background: #fff;
    }
    .header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: white;
      z-index: 1000;
      height: var(--header-large-height);
      transition: height 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border-bottom: 1px solid transparent;
    }
    .header.scrolled {
      height: var(--header-compact-height);
    }
    .unified-search-container {
      position: absolute;
      left: 50%;
      bottom: 20px;
      transform: translateX(-50%);
      transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
      z-index: 50;
      transform-origin: center center;
    }
    .header.scrolled .unified-search-container {
      bottom: auto;
      top: 50%;
      transform: translate(-50%, -50%) scale(0.8);
    }
    .search-container-unified {
      display: flex;
      align-items: center;
      background: #fff;
      border: 1px solid #dddddd;
      border-radius: 32px;
      width: 850px;
      height: var(--search-large-height);
      box-shadow: 0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05);
      transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
      cursor: pointer;
      position: relative;
      overflow: hidden;
    }
    .header.scrolled .search-container-unified {
      width: 300px;
      height: var(--search-compact-height);
      border-radius: 24px;
    }
    .search-items-container {
      display: flex;
      align-items: center;
      width: 100%;
      transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    }
    .header.scrolled .search-items-container {
      opacity: 0;
      pointer-events: none;
      transform: scale(0.9);
    }
    .search-compact-content {
      opacity: 0;
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%) scale(1.5);
      display: flex;
      align-items: center;
      white-space: nowrap;
      transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    }
    .header.scrolled .search-compact-content {
      opacity: 1;
      pointer-events: auto;
      transform: translateY(-50%) scale(1);
    }
    .search-compact-text {
      font-weight: 600;
      font-size: 14px;
      color: #222;
      margin-right: 12px;
    }
    .search-button-unified {
      background: var(--brand-color);
      border: none;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      cursor: pointer;
      transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
    .header.scrolled .search-button-unified {
      width: 30px;
      height: 30px;
      right: 8px;
    }
    .search-button-unified svg {
      color: white;
      width: 16px;
      height: 16px;
      transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
    }
    .header.scrolled .search-button-unified svg {
      width: 12px;
      height: 12px;
    }
    .main-content {
      padding-top: var(--header-large-height);
    }
    .scrollable-content {
      height: 2000px;
    }
  </style>
</head>
<body>
  <header class="header" id="header">
    <div class="unified-search-container">
      <div class="search-container-unified">
        <div class="search-items-container"></div>
        <div class="search-compact-content"></div>
        <button class="search-button-unified">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" style="stroke:white;stroke-width:4;fill:none"><path d="M13 24a11 11 0 1 0 0-22 11 11 0 0 0 0 22zm8-3 9 9"></path></svg>
        </button>
      </div>
    </div>
  </header>
  <main class="main-content">
    <div class="scrollable-content"></div>
  </main>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const header = document.getElementById('header');
      const unifiedSearchContainer = document.querySelector('.unified-search-container');
      const scrollThreshold = 50;
      let isScrolled = window.pageYOffset > scrollThreshold;

      if (isScrolled) {
        header.classList.add('scrolled');
      }

      const handleScroll = () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        if (scrollTop > scrollThreshold && !isScrolled) {
          header.classList.add('scrolled');
          isScrolled = true;
        } else if (scrollTop <= scrollThreshold && isScrolled) {
          header.classList.remove('scrolled');
          isScrolled = false;
        }
      };

      let ticking = false;
      const onScroll = () => {
        if (!ticking) {
          window.requestAnimationFrame(() => {
            handleScroll();
            ticking = false;
          });
          ticking = true;
        }
      };

      window.addEventListener('scroll', onScroll, { passive: true });

      if (unifiedSearchContainer) {
        unifiedSearchContainer.addEventListener('click', (e) => {
          if (isScrolled) {
            e.preventDefault();
            window.scrollTo({
              top: 0,
              behavior: 'smooth'
            });
          }
        });

        unifiedSearchContainer.addEventListener('mouseenter', () => {
          if (isScrolled) {
            unifiedSearchContainer.style.transform = 'translate(-50%, -50%) scale(0.38)';
          }
        });

        unifiedSearchContainer.addEventListener('mouseleave', () => {
          if (isScrolled) {
            unifiedSearchContainer.style.transform = 'translate(-50%, -50%) scale(0.35)';
          }
        });
      }
    });
  </script>
</body>
</html>