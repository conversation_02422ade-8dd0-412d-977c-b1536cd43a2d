import { __awaiter } from "tslib";
import { PluginSettingTab, Setting, Notice } from 'obsidian';
import { FolderSelectorModal } from './components/folder-selector-modal';
import { ContactModal } from './modal';
export class ContactSettingTab extends PluginSettingTab {
    constructor(app, plugin) {
        super(app, plugin);
        this.plugin = plugin;
    }
    display() {
        const { containerEl } = this;
        containerEl.empty();
        containerEl.createEl("h2", { text: "联系人插件设置" });
        // 联系人文件夹路径设置
        new Setting(containerEl)
            .setName("联系人文件夹路径")
            .setDesc("设置存放联系人笔记的文件夹路径")
            .addText(text => text
            .setPlaceholder("例如: Contacts")
            .setValue(this.plugin.settings.contactFolderPath)
            .onChange((value) => __awaiter(this, void 0, void 0, function* () {
            this.plugin.settings.contactFolderPath = value;
            yield this.plugin.saveSettings();
            // 更改路径后刷新缓存
            yield this.plugin.refreshContactCache();
        })));
        // 文件夹浏览按钮
        new Setting(containerEl)
            .setName("浏览文件夹")
            .setDesc("选择存放联系人的文件夹")
            .addButton(button => button
            .setButtonText("浏览")
            .onClick(() => __awaiter(this, void 0, void 0, function* () {
            const folderSelector = new FolderSelectorModal(this.app, (folder) => {
                this.plugin.settings.contactFolderPath = folder;
                this.plugin.saveSettings();
                this.display(); // 刷新设置界面
            });
            folderSelector.open();
        })));
        // 快捷键设置
        new Setting(containerEl)
            .setName("打开联系人管理器快捷键")
            .setDesc("设置打开联系人管理器的快捷键")
            .addHotkey(hotkey => {
            hotkey.setPlaceholder("未设置")
                .setValue(this.plugin.settings.openContactsHotkey)
                .onChange((value) => __awaiter(this, void 0, void 0, function* () {
                this.plugin.settings.openContactsHotkey = value;
                yield this.plugin.saveSettings();
                // 更新命令的快捷键
                const commandId = this.plugin.manifest.id + ":open-contact-manager";
                if (this.app.commands.commands[commandId]) {
                    this.app.commands.commands[commandId].hotkeys =
                        value ? [value] : [];
                }
            }));
        });
        // 测试联系人文件夹按钮
        new Setting(containerEl)
            .setName("测试联系人文件夹")
            .setDesc("检查联系人文件夹是否存在并包含笔记")
            .addButton(button => button
            .setButtonText("测试")
            .onClick(() => __awaiter(this, void 0, void 0, function* () {
            const files = yield this.plugin.getContactFiles();
            if (files.length > 0) {
                new Notice(`成功! 找到 ${files.length} 个联系人笔记。`);
            }
            else {
                new Notice(`未找到联系人笔记。请检查文件夹路径是否正确。`);
            }
        })));
        // 打开联系人管理器按钮
        new Setting(containerEl)
            .setName("打开联系人管理器")
            .setDesc("直接从设置中打开联系人管理器")
            .addButton(button => button
            .setButtonText("打开")
            .onClick(() => {
            new ContactModal(this.app, this.plugin).open();
        }));
    }
}
//# sourceMappingURL=data:application/json;base64,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