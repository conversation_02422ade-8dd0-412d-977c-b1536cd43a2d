.modal{
  /* background-color: red !important; */
}
/* 防止样式泄漏，添加样式隔离 */
.Contact_Page,
.Contact_Page * {
  box-sizing: border-box;
}
.Contact_Page .embedded-backlinks .nav-header {
  position: relative;
  top: 13px;
}
/* 最近搜索样式 - 与department-title-container保持一致 */
.recent-searches-title-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 104%;
  position: sticky;
  top: -1px;
  gap: 12px;
  background: var(--background-primary);
  z-index: 1;
  padding: 8px 20px;
  margin: 20px -20px 8px -24px;
  transition: box-shadow 0.2s ease, background 0.2s ease;
  background: rgb(255, 255, 255);
  box-shadow: rgba(16, 24, 40, 0.04) 0px 12px 16px -4px, rgba(16, 24, 40, 0.03) 0px 4px 6px -2px;
}

/* Sticky状态下的样式 */
.recent-searches-title-container.sticky-active {
  box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.04), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);
}

.theme-light .recent-searches-title-container.sticky-active {
  background: #fff;
}

.theme-dark .recent-searches-title-container.sticky-active {
  background: var(--background-primary);
}
/*----------------------------theme-light/chat view 单独效果------------------*/


.theme-light .Contact_Page .chat-view-bubble::after {
  background-color: #f9f9f9;
}
.theme-light .Contact_Page .chat-view-align-right,
.theme-light .Contact_Page .chat-view-bubble::before {
  /* background-color: #e0dcfb!important; */
  background-color: #e8e8fd!important;
}

.theme-light .Contact_Page .chat-view-align-left,
.theme-light .Contact_Page .chat-view-align-left::before {
  /* background-color: #eaeaeb!important; */
  background-color: #f0f0f2!important;

}
.theme-light .Contact_Page .chat-view-align-left .chat-view-message,
.theme-light .Contact_Page .chat-view-align-right .chat-view-message,
.theme-light .Contact_Page .chat-view-align-right h6{
  color: #232324!important;

}
.theme-light .Contact_Page .chat-view-align-right a{
  color: #7969d8!important;

}
.Contact_Page .chat-view-align-right h6.chat-view-header,
.Contact_Page .chat-view-align-right h4.chat-view-header{
  display: none;
}
div.Contact_Page .chat-view-align-left{
  margin-left: 30px;
}

.Contact_Page .chat-view-align-left h6.chat-view-header a,
.Contact_Page .chat-view-align-left h4.chat-view-header a{
  font-size: smaller;
  font-weight: 300;
  color: #232324;
  position: absolute;
  top:-3px;
  left: -51px;
}
.theme-dark .Contact_Page .chat-view-align-left h6.chat-view-header a,
.theme-dark .Contact_Page .chat-view-align-left h4.chat-view-header a{
  color: #d8dae1;
  opacity: .7;
  font-weight: 400;
}

/*----------------------------theme-dark/chat view 单独效果------------------*/

.Contact_Page .block-language-chat-transcript{
      position: relative!important;
}

.Contact_Page .chat-view-bubble-mode-default{
  border:none!important;
  border-radius: 20px!important;
  position: relative!important;

}

/*时间戳垂直居中*/
.Contact_Page .chat-view-subtext {
position: absolute;
  width: 100%;
  left: 0;
  top: 0;
  transform: translateY(-100%);
  text-align: center;
}


  .Contact_Page .chat-view-comment{
  font-size: smaller;
  width: auto!important;
  max-width: 100%!important;
}
.markwhen-view .Contact_Page .chat-view-default-vertical-margin,
.markwhen-view .Contact_Page .chat-view-small-vertical-margin{
  margin-top: 40px;
}


.Contact_Page .chat-view-bubble-mode-default > p.chat-view-message {
  margin: 0!important;
}
.Contact_Page .chat-view-bubble-mode-default .file-embed-icon,
.Contact_Page .chat-view-message .embed-title{
  margin: 0!important;
  width: 18px;
  font-weight: 400!important;
}


.Contact_Page .chat-view-align-right{
  background-color: #474474!important; /* 发送者气泡颜色 */

}
.Contact_Page .chat-view-align-left .chat-view-message,
.Contact_Page .chat-view-align-right .chat-view-message,
.Contact_Page .chat-view-align-right h6{
  color: #dedede!important;

}

.Contact_Page .chat-view-align-left,
.Contact_Page .chat-view-align-left::before {
  background-color: #1f2228!important;

}
.Contact_Page .chat-view-align-right a{
  color: #a3a2f9 !important;

}

/*不需要修改左侧气泡定位*/

.Contact_Page .chat-view-align-left::after {
  left: -10px;
  right: unset;
  border-bottom-left-radius: unset!important;
  border-bottom-right-radius: 10px;
}

.Contact_Page .chat-view-align-left::before {
  left: -8px;
  right: unset;
  border-bottom-left-radius: unset!important;
  border-bottom-right-radius: 15px;
}


/*不要修改气泡填补的背景色*/
.Contact_Page .chat-view-bubble::before {
  content: "";
  position: absolute;
  bottom: 0;
  height: 20px;
  width: 20px;
  background-color: #474474;
  right: -8px; /* 调整气泡尖角位置 */
  border-bottom-left-radius: 15px;
}
.Contact_Page .chat-view-bubble::after {
  content: "";
  position: absolute;
  bottom: -1px;
  z-index: 1;
  height: 21px;
  width: 10px;
  background-color: #282c34;
  right: -10px;
  border-bottom-left-radius: 10px;
}
.Contact_Page .block-language-chat-transcript .chat-view-header a::before {
  content: "";
  display: block;
  font-size: 20px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-color: var(--background-modifier-border);
  transform: translate(13px, 30px);

}
.Contact_Page .block-language-chat-transcript .chat-view-header a {
  position: absolute !important;
  left: 5px;  /* 贴紧左侧 */
  bottom: 12px; /* 贴紧底部 */
  font-size: 0; /* 隐藏原文本 */
  width: 26px;
  height: 26px;
  z-index: 999;
}
/*--------------------------------联系人页面css------------------*/
.Contact_Page .chat-view-align-right .chat-view-header{
  display: none;
}
.Contact_Page .chat-view-message a{
  height: 28px;
  white-space: nowrap;         /* 防止文本换行 */
  overflow: hidden;            /* 隐藏超出的部分 */
  text-overflow: ellipsis;     /* 超出部分显示省略号 */
}

.Contact_Page .chat-view-bubble{
  min-width: 25%!important;
  /* padding: 8px!important; */
}
.Contact_Page .chat-view-default-vertical-margin,
.Contact_Page .chat-view-small-vertical-margin{
    margin-top: 40px!important;
    font-size: 14px;
}
.Contact_Page .block-language-chat-transcript{
  padding: 10px;
}
.laborer-root-info .tabs-container .tabs-contents{
  padding: 17px 0!important;
}
.laborer-root-info .mod-header,
.laborer-root-info .metadata-container
{
  display: none!important;
}


.laborer-root-info .tabs-container .tabs-contents > .tabs-content > p:not(:has(span)){
  float: left;
  display: flex;
  justify-content: left; /* 水平居中 */
  align-items: center;

}


.laborer-root-info .tabs-container .tabs-contents > .tabs-content > p:has(> span){
  float: left;
}
.laborer-root-info .tabs-container .tabs-contents > .tabs-content p .markdown-embed {
  margin: 0;
  padding: 0;
  border: none;
}
.laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(1) span p{
  font-size: 13px;
  color: #898f9d;
  line-height: 16px;
}
.laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:not(span p) {
  width: 118px;
  height: 71px;
  float: left;
  font-size: 13px;
  border-radius: 4px;
  padding: 5px 5px 0 ;
  background-color: #9e9e9e1a;
  margin: 0 7px 7px 0 ;
  border-radius: 15px 0px 10px 0px;
}
.theme-dark .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(75),
.theme-dark .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(23),
.theme-dark .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(28),
.theme-dark .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(37),
.theme-dark .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(42),
.theme-dark .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(52),
.theme-dark .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(57),
.theme-dark .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(62),
.theme-dark .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(67),
.theme-dark .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(72){
  font-size: 18px;
  color: #d8dae1;
  text-align: center;
  line-height: 71px!important;
  clear: both;
  background-color: transparent;
}
.theme-light .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(75),
.theme-light .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(23),
.theme-light .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(28),
.theme-light .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(37),
.theme-light .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(42),
.theme-light .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(52),
.theme-light .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(57),
.theme-light .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(62),
.theme-light .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(67),
.theme-light .laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(72){
  font-size: 18px;
  color: #000;
  text-align: center;
  line-height: 71px!important;
  clear: both;
  background-color: transparent;
}
.laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(1),
.laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(2),
.laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(3),
.laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(4),
.laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(5),
.laborer-root-info .tabs-container .tabs-contents .tabs-content:nth-child(1) p:nth-child(22){
  margin-bottom: 20px;
}

.theme-light .laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(2) span p,
.theme-light .laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(3) span p,
.theme-light .laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(4) span p,
.theme-light .laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(5) p{
  color: #8e8e90;
}
.laborer-root-info .tabs-container .tabs-contents > .tabs-content > p:not(:has(span)),
.laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(2) span p,
.laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(3) span p,
.laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(4) span p
{
  width: 152px;
  height: 108px;
  line-height: 20px;
  font-size: 13px;
  color: #898f9d;
  margin: 0 7px 7px 0 ;
  border-radius: 4px;
  padding: 4px 5px 4px 6px;
  background-color: #9e9e9e1a;
  overflow-wrap: break-word;
  word-break: break-all;
}
.laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(1) span p a:nth-child(1),
.laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(2) span p a:nth-child(1),
.laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(3) span p a:nth-child(1),
.laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(4) span p a:nth-child(1)
{
  float: right;
  text-decoration:none;
  font-weight: 600;
}
.laborer-root-info .tabs-container .tabs-contents > .tabs-content:nth-child(5) pre{
  width: 48%;
  height: auto;
  float: left;
  font-size: 15px;
  color: #898f9d;
  margin: 0 10px 10px 0 ;
  background-color: #9e9e9e1a;
  border-radius: 4px;
  padding: 8px 10px  10px;
}



.Contact_Page .tabs-content-active table,
.Contact_Page .tabs-content-active table th,
.Contact_Page .tabs-content-active table td {
  border: none !important;
  outline: none !important;
  background-clip: padding-box;
}
.Contact_Page .tabs-content-active table td {
    width: 50%!important;
  }
/* 针对 Contact_Page 中的表格 */
.Contact_Page .tabs-content-active table {
    width: 100%!important;
    border-collapse: separate;
    padding: 0 35px 15px!important;
  }
.Contact_Page .tabs-content-active table thead th:not(:has(code)),
.Contact_Page .tabs-content-active table td:not(:has(code)) {
    padding: 10px 0 0 10px;
    font-size: 12px;
    color: #898f9d;
    font-weight: 400;
  }
  .Contact_Page .tabs-content-active table thead th{
    line-height: 20px;
    height: 34px;
  }

  .Contact_Page .tabs-content-active table tr:not(:nth-child(2)) td:first-child {
    display: block;
    width: 95%!important;
    background-color: var(--document-item-alt-bg);
    padding: 9px 10px 8px;
    border-radius: 6px;
    color: var(--text-normal);
    font-size: 14px;
  }
  .Contact_Page .tabs-content-active table tr:nth-child(1) td:nth-child(2) {
    line-height: 20px;
    background-color: var(--document-item-alt-bg);
    color: var(--text-normal);
    width: 95%!important;
    border-radius: 6px;
    padding: 8px  8px 8px 8px;
  }
  
  /* 清除右侧纯文字格式 */
  .Contact_Page table td:nth-child(2) {
    color: #94a3b8;
    font-weight: 400;
    line-height: 2;
  }

/*--------------------------联系人头像------------------------*/














/*--------------------------------待办事项页面css------------------*/
.Contact_Page .dataview.dataview-container{
  font-size: 14px;
}

.dataview.task-list-item span br,
.dataview.task-list-item span br ~ * {
  display: none;
}
.Contact_Page .backlink-pane{
  margin: 0px 15px 15px!important;
  width: auto;
  /* background-color: #f7f7f7; */
  border-radius: 0 0 10px 10px ;
  padding-bottom: 90px;
  /* height: calc(100% + 15px) !important; */
}
.theme-dark .Contact_Page .backlink-pane{
  margin: 0px 15px 0;
  width: auto;
  /* background-color: #282c34; */
  border-radius: 0 0 10px 10px ;
  padding-bottom: 90px;
  /* height: 100%!important; */
}
.markdown-source-view.mod-cm6 > .cm-editor > .cm-scroller > .cm-sizer > .cm-contentContainer > .cm-content > .cm-preview-code-block.cm-embed-block.markdown-rendered:has(>.tabs-container):hover {
box-shadow:
 -3px 0 5px -3px rgba(0,0,0,0.1),    /* 左侧阴影 */
 3px 0 5px -3px rgba(0,0,0,0.1),     /* 右侧阴影 */
 0 -3px 5px -3px rgba(0,0,0,0.1);    /* 上边阴影 把0 3px改为0 -3px */
}

/*--------------------------------待办事项页面css------------------*/

.Contact_Page .dataview-container ul.contains-task-list li span .callout {
  display: none;
}
.popover .Contact_Page .tabs-contents > .tabs-content:nth-child(3) span pre,
.popover .Contact_Page .tabs-contents > .tabs-content:nth-child(3) span p{
    font-size: 14px;
}
.Contact_Page .cm-preview-code-block.cm-embed-block.markdown-rendered + .cm-line{
    display: none;
}
.Contact_Page .backlink-pane.node-insert-event{
    height: 625px!important;
}


/*--------------------------------联系人页面css------------------*/


.Contact_Page .cm-preview-code-block .tabs-container{
  padding: 1px!important;
  margin: 14px!important;
  background-color: #fff;
  margin-bottom: 0!important;
}
.theme-dark .Contact_Page .cm-preview-code-block .tabs-container{
  padding: 1px!important;
  margin: 14px!important;
  background-color: #21252c;
  margin-bottom: 0!important;
}
.Contact_Page .block-language-dataview{
   border-radius:0  ;
  margin-top:0!important;
  background-color: #f7f7f7;

}
.theme-dark .Contact_Page .block-language-dataview{
   border-radius:0  ;
  margin-top:0!important;
  background-color: #282c34;

}
.Contact_Page .dataview-container{
   border-radius:0 0 10px 10px ;
  background-color: #f7f7f7;
  margin-bottom: 15px;
}
.theme-dark .Contact_Page .dataview-container{
   border-radius:0 0 10px 10px ;
  background-color: #282c34;
  margin-bottom: 15px;
}
.Contact_Page .dataview-container .contains-task-list{
 padding: 20px;
  margin: 15px!important;
  margin-bottom: 15px!important;
  background-color: #fff;
  margin-bottom: 0!important;
   border-radius:10px ;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.theme-dark .Contact_Page .dataview-container .contains-task-list{
 padding: 20px;
  margin: 15px!important;
  margin-bottom: 15px!important;
  background-color: #21252c;
  margin-bottom: 0!important;
   border-radius:10px ;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.Contact_Page.mod-cm6 .edit-block-button{
  margin-right: 15px!important;
}

.Contact_Page .tabs-container .tabs-contents{

  background-color:#f7f7f7;
  padding: 0;

}
.theme-dark .Contact_Page .tabs-container .tabs-contents{

  background-color:#282c34;
  padding: 0;
}

/* 容器基础样式 */
.Contact_Page .el-pre:has(.tabs-container){
  padding: 14px;
}
.Contact_Page .cm-preview-code-block,
.Contact_Page .el-pre
{
  border-radius: 10px 10px 0 0!important;
  background-color:#ffffff;
  box-shadow:
 -3px 0 5px -3px rgba(0,0,0,0.1),    /* 左侧阴影 */
 3px 0 5px -3px rgba(0,0,0,0.1),     /* 右侧阴影 */
 0 -3px 5px -3px rgba(0,0,0,0.1);    /* 上边阴影 把0 3px改为0 -3px */
}


.theme-dark .Contact_Page .cm-preview-code-block {
  border-radius: 10px 10px 0 0!important;
  background-color:#21252c;
  box-shadow:
 -3px 0 5px -3px rgba(0,0,0,0.1),    /* 左侧阴影 */
 3px 0 5px -3px rgba(0,0,0,0.1),     /* 右侧阴影 */
 0 -3px 5px -3px rgba(0,0,0,0.1);    /* 上边阴影 把0 3px改为0 -3px */
}


/* Tab导航样式 */
.Contact_Page .tabs-nav {
  display: flex;
  justify-content: center;
  padding: 20px;
  border-radius: 10px 10px 0 0;
}
.theme-dark .Contact_Page .tabs-nav {
  background-color: #282c34;
}
.theme-light .Contact_Page .tabs-nav {
  background-color: #f7f7f7;
}
.Contact_Page .tabs-nav-item-wrapper {
  display: flex;
  justify-content: center;
  border-radius: 8px;
  padding: 4px 0!important;
  gap: 4px;
  width: 91%;
}
.theme-dark .Contact_Page .tabs-nav-item-wrapper {
  background-color: #21252c;
}
.theme-light .Contact_Page .tabs-nav-item-wrapper {
  background-color: #fff;

  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.Contact_Page .tabs-nav-item {
  width: calc(33% - 3px);
  cursor: pointer;
  color: #666;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  border-radius: 4px;

}
.Contact_Page div.chat-view-align-right{
  margin-right: 15px;
}

.Contact_Page .chat-view-align-right .chat-view-header a::before{
  display: none;
}
.Contact_Page div.chat-view-align-left{
  margin-left: 45px;
}

.Contact_Page .block-language-chat-transcript {
  margin-bottom: 30px;
}
.Contact_Page .tree-item.search-result{
  margin: 0 15px;
}
.popover .tabs-nav-item {
  width: 32%;
}
/* 激活状态的样式 */
.Contact_Page .tabs-nav-item.active {
/*    background-color: #fff;*/
  color: #333;
  font-weight: 500;
/*    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);*/
}

/* 非激活状态的hover效果 */
.Contact_Page .tabs-nav-item:not(.active):hover {
  background-color: rgba(255, 255, 255, 0.5);
}

/* 移除激活状态的下划线 */
.Contact_Page .tabs-nav-item-active::after {
  display: none;
}

/* Tab 文字容器 */
.Contact_Page .tabs-nav-item-md {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Tab 文字样式 */
.Contact_Page .tabs-nav-item-md p {
  margin: 0;
  font-size: 14px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 400;
}

/* 添加图标 */
.Contact_Page .tabs-nav-item:nth-child(1) .tabs-nav-item-md p::before {
content: "⚪️";
  opacity: 0.5;  /* 直接使用透明度 */
  transition: all 0.3s ease;
}

.Contact_Page .tabs-nav-item-active:nth-child(1) .tabs-nav-item-md p::before {
content: "⚪️";
  opacity: 1;  /* 直接使用透明度 */
}

.Contact_Page .tabs-nav-item:nth-child(2) .tabs-nav-item-md p::before {
  content: "💬";
  font-size: 16px;
  opacity: 0.5;  /* 直接使用透明度 */
  transition: all 0.3s ease;
}
.Contact_Page .tabs-nav-item-active:nth-child(2) .tabs-nav-item-md p::before {
  content: "💬";
  font-size: 16px;
  opacity: 1;  /* 直接使用透明度 */
}

.Contact_Page .tabs-nav-item:nth-child(3) .tabs-nav-item-md p::before {
content: '';
color: #a0a1a8;
margin-right: 7px!important;
width: 4px;
height: 4px;
background-color: currentColor;
border-radius: 50%;
/* 使用 box-shadow 创建左右两个点 */
box-shadow:
  -8px 0 0 0 currentColor,
   8px 0 0 0 currentColor;
     transition: all 0.3s ease;
  opacity: 0.5;  /* 直接使用透明度 */
}
.Contact_Page .tabs-nav-item-active:nth-child(3) .tabs-nav-item-md p::before {
  opacity: 1;  /* 直接使用透明度 */
}

.theme-dark .Contact_Page .tabs-nav-item:nth-child(3) .tabs-nav-item-md p::before {
content: '';
color: #fff;
margin-right: 7px!important;
width: 4px;
height: 4px;
background-color: currentColor;
border-radius: 50%;
/* 使用 box-shadow 创建左右两个点 */
box-shadow:
  -8px 0 0 0 currentColor,
   8px 0 0 0 currentColor;
     transition: all 0.3s ease;
}


.theme-dark .Contact_Page .tabs-nav-item:hover,
.theme-dark .Contact_Page .tabs-nav-item-active{
/*    border-radius: 10px 10px 0 0 ;*/
  background-color: #282c34;
}

/* 悬停效果 */
.Contact_Page .tabs-nav-item:hover {
/*    border-radius: 10px 10px 0 0 ;*/
  background-color: #f8f9fa;

}

/* 激活状态 */
.Contact_Page .tabs-nav-item-active,
.Contact_Page .tabs-nav-item-active .tabs-nav-item-md p {
  color: #fff;
  font-weight: 500;
}
/* 代码块水平布局 */
.Contact_Page .tabs-content-active pre:nth-of-type(-n+2) {

  width: calc(50%);
  background: #fff;
  border-radius: 5px;
  padding: 0;
  line-height: 30px!important;
  min-height:20px;
  margin: 15px auto!important;
  text-align: center;
  width: fit-content;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.theme-dark .Contact_Page .tabs-content-active pre:nth-of-type(-n+2) {

  width: calc(50%);
  background: #21252c;
  border-radius: 5px;
  padding: 0;
  line-height: 30px!important;
  min-height:20px;
  margin: 15px auto!important;
  text-align: center;
  width: fit-content;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.Contact_Page .tabs-contents > .tabs-content:nth-child(1) .tabs-content-active pre:nth-of-type(-n+2):hover {
  box-shadow: 0px 4px 15px rgba(0, 0, 0, 0.1); /* 加大阴影效果 */
  transform: translateY(-2px); /* 添加轻微上浮效果 */
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) pre code {
  display: block;
  text-align: center;
  padding: 1px 20px;
  font-family: monospace;
  color: #000;
  font-family: ui-sans-serif !important;
  font-size: 20px!important;
  font-weight: 500;

}
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) pre code::before {
content: "✉️ ";
font-size: 18px;

}
.theme-dark .Contact_Page .tabs-contents > .tabs-content:nth-child(1) pre code {
  display: block;
  text-align: center;
  padding: 1px 20px;
  font-family: monospace;
  color: #fff;
  font-family: ui-sans-serif !important;
  font-size: 20px!important;
  font-weight: 500;

}

.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(2)::before {
  content: "📞";
  font-size: 14px;
  margin-right: 7px;
}
.theme-dark .Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(2)::before {
  content: "☎️";
  font-size: 14px;
  margin-right: 7px;
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(3)::before {
  content: "+";
  font-size: 18px;
  margin-right: 3px;
}


/* 复制按钮样式 */
.Contact_Page .copy-code-button {
  position: absolute;
  right: -5px!important;
  top: -5px!important;
  padding: 4px 8px;
  font-size: 12px;
  color: #fff!important;
  font-weight: 400!important;
  background-color: #7170d2!important;
  border-radius: 3px;
  cursor: pointer;
  opacity: 0.9!important;
  transition: all 0.2s;
}

.Contact_Page pre:hover .copy-code-button {
  opacity: 1;
}

/* 个人信息样式 */
.Contact_Page p[dir="auto"] {
/*    margin: 8px 0;*/
  text-align: center;
  line-height: 1.6;
}

/* 名字、电话和地址样式 */
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(1) span.mod-empty-attachment{
  font-size: 0;
background-image: url("data:image/svg+xml;base64,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");
background-size: cover; /* 确保图片缩放 */

}
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(1) span{
  display: block;
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(1) span.mod-empty-attachment,
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(1) span img{
  height: 90px!important;
  width: 90px!important;
  border-radius: 50%!important;
  margin: 40px auto 25px!important;
/*    border: 3px solid #fff;*/
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) pre + p span {
  display: inline-block;
  font-weight: 400;
  margin: 7px auto;
  color: var(--text-normal);
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(1){
   font-size:22px!important;
   font-weight: bold;
   display: block;
   border-radius: 10px ;
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(2),
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(3){
   font-size:16px!important;
   color: var(--text-muted);
   font-weight: 400;
   margin: 7px auto;
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(4) {
color: #8a8a8a;
font-size: 14px!important;
margin-bottom: 50px;
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(1) p:nth-of-type(2) {
      font-size: 17px!important;
   color: #409eff!important;
}
.Contact_Page .tabs-nav-button{
  display: none;
}

.Contact_Page .tabs-container .tabs-nav .tabs-nav-item.tabs-nav-item-active{
  border-color: transparent;
}




.Contact_Page .tabs-contents > .tabs-content:nth-child(3){
    width: 100%;
    text-align: center;     /* 使inline-block元素居中 */
    font-size: 0;
    padding: 10px 30px;
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(3) span{
    display: inline-block; /* 行内块元素 */
    width: 46%;
    margin-top:5px;      /* 宽度50% */
    text-align: center;    /* 文本居中 */
    box-sizing: border-box;/* 确保padding不会增加宽度 */
    font-size: 16px;      /* 重置字体大小 */
    vertical-align: top;
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(3) span p{
      text-align: left;
      padding: 10px 0 0 10px;
      font-size: 12px;
      color: #898f9d;
}

.Contact_Page .tabs-contents > .tabs-content:nth-child(3) span + span p:nth-of-type(2){
  display: block;
   width: calc(100% - 20px)!important;
  white-space: nowrap;         /* 强制一行显示 */
  overflow: hidden;            /* 超出隐藏 */
  text-overflow: ellipsis;     /* 显示省略号 */
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(3) span + span a{
  display: block;
  padding:5px 10px 5px 0px;
  white-space: nowrap;         /* 强制一行显示 */
  overflow: hidden;            /* 超出隐藏 */
  text-overflow: ellipsis;     /* 显示省略号 */
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(3) span +span p:nth-of-type(4){
  color: #8a8a8a;
  border-radius: 5px;
  padding: 0 0 0 20px;
  line-height: 30px!important;
  min-height: 30px;
  margin:8px 15px 0 5px!important;
  padding:3px 0 3px 10px;
  white-space: nowrap;         /* 强制一行显示 */
  overflow: hidden;            /* 超出隐藏 */
  text-overflow: ellipsis;     /* 显示省略号 */
}
.theme-dark .Contact_Page .tabs-contents > .tabs-content:nth-child(3) span +span p:nth-of-type(4){
  background: #21252c;

}
.theme-light .Contact_Page .tabs-contents > .tabs-content:nth-child(3) span +span p:nth-of-type(4){
  background: #fff;

}

.Contact_Page .tabs-contents > .tabs-content:nth-child(3) span pre{
  width: 90%;
  margin: 5px 0 0 10px!important;
  text-align: left;
  padding:3px 0 3px 10px;
  white-space: nowrap;         /* 强制一行显示 */
  overflow: hidden;            /* 超出隐藏 */
  text-overflow: ellipsis;     /* 显示省略号 */
}
.Contact_Page .tabs-contents > .tabs-content:nth-child(3) span pre code{
color: #8a8a8a;
}

.Contact_Page .tabs-contents > .tabs-content:nth-child(3) hr {
    width: calc(100% - 4%)!important;          /* 占满容器宽度 */
    margin: 30px auto 30px;
    text-align:center;    /* 上下间距 */
    border: none;
    border-top: 1px solid #363b45;
  }

.theme-light .Contact_Page .tabs-contents > .tabs-content:nth-child(3) hr {

    border-top: 1px solid #dbdbdc;
  }

  .Contact_Page .callout{
    padding: 0;
    border-radius: 0 0 0px 0px!important;
}
.Contact_Page .cm-callout {
    position: relative;
    padding: 0!important;
    background-color: #fff;
    padding: 0 15px!important;
    border-radius: 0;
    box-shadow:
    -3px 0 5px -3px rgba(0,0,0,0.1),
    3px 0 5px -3px rgba(0,0,0,0.1) !important;
    clip-path: inset(1 -5px) !important;  /* 添加这行 */
}
.theme-dark .Contact_Page .cm-callout {
    position: relative;
    padding: 0!important;
    background-color: #21252c;
    padding: 0px 15px 0!important;
    border-radius: 0;
    box-shadow:
    -3px 0 5px -3px rgba(0,0,0,0.1),
    3px 0 5px -3px rgba(0,0,0,0.1) !important;
    clip-path: inset(1 -5px) !important;  /* 添加这行 */
}


.Contact_Page .callout .callout-title{
    background-color: #f7f7f7!important;
    padding-left: 10px;
    padding-top: 10px;
}
.Contact_Page .HyperMD-quote.HyperMD-quote-1.cm-line * {
    margin-left: 2px;
}

.theme-dark .Contact_Page .callout .callout-title{
    background-color: #282c34!important;
    padding-left: 10px;
}
.Contact_Page .markdown-source-view.mod-cm6 .cm-embed-block:not(.cm-table-widget):hover{
    box-shadow: 0 0 0 rgba(0,0,0,0) !important;  /* 清除底部阴影 */
}
.Contact_Page .callout-title-inner{
font-weight: 500;
}
.Contact_Page .callout-icon{
    display: none;
}
.Contact_Page .cm-lineWrapping{
    padding-bottom: 0!important;
}
.Contact_Page .embedded-backlinks{
    border: none;
    background-color: #fff;
 box-shadow:
   -3px 0 5px -3px rgba(0,0,0,0.1),   /* 左侧阴影 */
   3px 0 5px -3px rgba(0,0,0,0.1),    /* 右侧阴影 */
   0 3px 5px -3px rgba(0,0,0,0.1);       /* 底部阴影 保持不变 */
     border-radius:0 0 10px 10px ;
     margin-bottom: auto;
     margin-top: -14px;
}
.theme-dark .Contact_Page .embedded-backlinks{
    border: none;
    background-color: #21252c;
 box-shadow:
   -3px 0 5px -3px rgba(0,0,0,0.1),   /* 左侧阴影 */
   3px 0 5px -3px rgba(0,0,0,0.1),    /* 右侧阴影 */
   0 3px 5px -3px rgba(0,0,0,0.1);       /* 底部阴影 保持不变 */
     border-radius:0 0 10px 10px ;
     margin-bottom: auto;
     margin-top: -14px;
}
.Contact_Page .nav-buttons-container{
    margin-right: 20px;
    margin-top: -5px;
}
.Contact_Page .dataview.dataview-error-message{
    padding-left: 10px;
}


.Contact_Page .nav-header{
    height: 190px;
    background-color: #f7f7f7;
    margin: 0 15px!important;
    border-radius: 0 0 10px 10px;
    padding-bottom: 20px;
}
.theme-dark .Contact_Page .nav-header{
    height: 190px;
    background-color: #282c34;
    margin: 0 15px!important;
    border-radius: 0 0 10px 10px;
    padding-bottom: 20px;

}
.Contact_Page .embedded-backlinks .nav-buttons-container{
    height: fit-content;
    top:initial;
    bottom: 0px!important;
    background-color: #f7f7f7!important;
    padding-bottom: 20px;
}
.theme-dark .Contact_Page .embedded-backlinks .nav-buttons-container{
    height: fit-content;
    top:initial;
    bottom: 0px!important;
    background-color: #282c34!important;
    padding-bottom: 20px;
}
.Contact_Page .embedded-backlinks .nav-header > .search-input-container{
width: 74%;
margin: -10px 15px 0!important;
position: relative;
bottom: -140px;
}
.Contact_Page .backlink-pane.node-insert-event > .tree-item-self.is-clickable:first-of-type {
    margin-top: 20px;
  }




/*--------------------------------联系人页面css------------------*/




/* 防止内容下移的关键样式 - 只针对插件相关内容 */
.modal-container .workspace-leaf-content,
.modal-container .view-content {
  position: relative !important;
  top: 0 !important;
  margin-top: 0 !important;
}

/* 只针对当前模态框的修复 */
.modal-container .Contact_Page {
  overflow-y: auto !important;
}


/* 防止模态框影响全局样式 */
.workspace-split.mod-root {
  position: relative !important;
}

/* 防止body样式被干扰 */

/* 确保插件样式不影响笔记内容 - 仅应用于联系人模态框打开时 */
.modal-container .markdown-source-view,
.modal-container .markdown-preview-view,
.modal-container .workspace-leaf-content[data-type="markdown"] .view-content {
  margin: 0 auto !important;
  padding: var(--file-margins) !important;
  transform: none !important;
  left: auto !important;
  right: auto !important;
  width: 100% !important;
  max-width: var(--file-line-width) !important;
  position: relative !important;
  top: 0 !important;
}

/* 删除全局滚动修复，它可能影响了其他页面 */
/* 修复编辑区域滚动问题 - 改为更精确的选择器 */
.modal-container .cm-scroller {
  position: relative !important;
  z-index: 1 !important;
}

/* 修复预览模式滚动问题 - 改为更精确的选择器 */
.modal-container .markdown-preview-view {
  position: relative !important;
  z-index: 1 !important;
}



/* 确保插件自身内容的滚动正常 */
.Contact_Page {
  /* width: 90vw; */
  height: 80vh;
  display: flex;
  flex-direction: column;
  /* max-width: 1800px; */
  overflow-x: hidden;
  overflow-y: auto;
  position: relative; /* 为浮动按钮提供定位基准 */
}
.Contact_Page .search-container{
  background-color: #fff;
}
.modal-content.Contact_Page h2 {
  padding-left: 20px;
  margin: 10px 0 15px;
}

/* 搜索栏样式 */
.contact-search-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: sticky;
  z-index: 9;
  gap: 15px;
  padding: 10px 15px 30px;
  width: 100%;

}

/* 输入组件通用样式 */
.contact-search-input,
.organization-select select,
.department-select select,
.role-select select {
  height: 30px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-normal);
  font-size: 14px;
  line-height: 28px;
}

/* 搜索输入框 */
.contact-search-input {
  width: 300px;
  padding: 0 12px;
}

.contact-search-input:focus {
  outline: none;
  border-color: var(--interactive-accent);
}

/* 重置按钮样式 */
.reset-button {
  height: 30px;
  padding: 0 16px;
  border-radius: 4px;
  border: 1px solid var(--background-modifier-border);
  background-color: var(--background-primary);
  color: var(--text-normal);
  font-size: 14px;
  cursor: pointer;
  line-height: 28px;
  transition: all 0.2s ease;
}

.reset-button:hover {
  background-color: var(--interactive-hover);
  border-color: var(--interactive-hover);
}

/* 下拉列表样式 */
.organization-select,
.department-select,
.role-select {
  position: relative;
  min-width: 180px;
  height: 30px;
}

/* 下拉菜单选择器 */
.organization-select select,
.department-select select,
.role-select select {
  width: 100%;
  padding: 0 30px 0 12px;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 自定义下拉菜单容器 */
.custom-select-container {
  position: relative;
  min-width: 180px;
  height: 30px;
  z-index: 20;
}

/* 创建一个伪元素来填充下拉菜单和触发器之间的空隙 */
.custom-select-container:hover::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: -15px;
  left: 0;
  z-index: 99;
}

/* 自定义下拉菜单触发器 */
.custom-select-trigger {
  width: 100%;
  height: 30px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background-color: var(--background-primary);
  color: var(--text-normal);
  font-size: 14px;
  line-height: 28px;
  padding: 0 30px 0 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 自定义下拉菜单触发器箭头 */
.custom-select-trigger::after {
  content: '▼';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 12px;
  color: var(--text-muted);
  transition: transform 0.2s ease;
}

/* 旋转箭头 */
.custom-select-container:hover .custom-select-trigger::after {
  transform: translateY(-50%) rotate(180deg);
}

/* 自定义下拉菜单选项列表的基础样式 */
.custom-select-options {
  position: absolute;
  top: calc(100% + 10px); /* 减小与触发器的距离 */
  left: -50%;
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  background-color: var(--background-primary);
  z-index: 100;
  margin-top: 0; /* 移除上边距，使用top的计算值替代 */
  transition: opacity 0.2s ease, max-height 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 400px;
  backdrop-filter: blur(4px);
  padding-top: 5px; /* 添加上边距 */
}

/* 标准下拉菜单悬停样式 */
.custom-select-container:hover .custom-select-options {
  max-height: none;
  opacity: 1;
  overflow-y: visible;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 5px;
  padding: 12px;
  
  /* 标准下拉定位 */
  position: absolute;
  top: calc(100% + 10px); /* 保持与上面设置的一致 */
  bottom: auto;
  left: 50%;
  transform: translateX(-50%);
}

/* 部门选择器的容器样式 */
.department-select {
  position: relative;
}

/* 部门选择器的特殊样式 - 覆盖标准样式 */
.department-select:hover .custom-select-options {
  width: 600px;
  display: flex;
  flex-wrap: wrap;
  grid-template-columns: unset;
  max-height: none;
  padding: 15px;
  justify-content: space-between;
  align-content: flex-start;
  overflow-y: visible;
  
  /* 特殊定位，确保可见 */
  left: 50%;
  transform: translateX(-50%);
  top: calc(100% + 10px);
  position: absolute;
  border: 1px solid var(--background-modifier-border);
}

/* 部门选项的特殊样式 */
.department-select .custom-select-option {
  padding: 10px 15px;
  white-space: normal;
  line-height: 1.5;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  text-align: left;
  font-size: 14px;
  margin: 6px 3px;
  border-radius: 6px;
  flex: 0 0 calc(50% - 12px);
  transition: all 0.2s ease;
  overflow: visible;
  border: 1px solid transparent;
  position: relative;
}

.department-select .custom-select-option:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  border-radius: 6px 0 0 6px;
  background-color: transparent;
  transition: background-color 0.2s ease;
}

.department-select .custom-select-option:hover {
  background-color: var(--interactive-hover);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
  border-color: var(--background-modifier-border);
}

.department-select .custom-select-option.selected {
  background-color: var(--background-primary);
  color: var(--text-normal);
  font-weight: 500;
  border-color: var(--interactive-accent);
}

.department-select .custom-select-option.selected:before {
  background-color: var(--interactive-accent);
}

/* 自定义下拉菜单触发器 - 部门选择器特殊处理 */
.department-select .custom-select-trigger {
  text-align: left;
  font-weight: 500;
  position: relative;
}

/* 部门选择器优先部门样式 */
.department-select .custom-select-option[data-priority="true"] {
  font-weight: 500;
  background-color: rgba(var(--interactive-accent-rgb), 0.05);
}

/* 角色选择器的特殊样式 */
.role-select:hover .custom-select-options {
  width: 250px;
  padding: 10px;
  left: auto;
  right: 0;
  transform: none;
  top: calc(100% + 10px);
  grid-template-columns: 1fr;
}

/* 自定义下拉菜单选项的通用样式 */
.custom-select-option {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  margin: 2px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: background-color 0.2s ease;
}

.custom-select-option:hover {
  background-color: var(--background-modifier-hover);
}

.custom-select-option.selected {
  background-color: var(--interactive-accent-hover);
  color: var(--text-on-accent);
}

/* 未选择状态的文字颜色 */
.custom-select-trigger.unselected {
  color: var(--text-muted);
}

/* 联系人列表样式 */
.contact-results {
  flex: 1;
  width: 100%;
  padding: 0 24px;
  height: 100%;
  max-height: calc(100vh - 120px);
}

.contact-list {
  display: grid;
  grid-template-columns: repeat(5, minmax(235px, 1fr));
  gap: 26px;
  width: 100%;
  padding: 15px 0;
  margin: 0 auto;
  justify-content: center;
}

/* 搜索结果标题 */
.search-results-title {
  padding: 10px 15px;
  margin: 0 0 0 -25px;
  width: 104%;
  font-size: 1em;
  color: var(--text-normal);
  font-weight: 500;
  border-bottom: 1px solid var(--background-modifier-border);
  background-color: #ffffff;
  border-radius: 4px 4px 0 0;
  text-align: center;
}

/* 搜索结果列表样式 - 当结果不满一行时居中显示 */
.all-contacts {
  display: flex !important;
  flex-wrap: wrap !important;
  justify-content: center !important;
  gap: 18px;
  padding: 15px 0;
}

.all-contacts .contact-item {
  flex: 0 0 235px;
  margin: 0;
  max-width: 235px;
  min-width: 235px;
}

  .contact-item {
  width: 100%;
  display: inline-block;
  vertical-align: top;
  position: relative;
  cursor: pointer;
  /* 性能优化：减少过渡效果，使用硬件加速 */
  transition: transform 0.12s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  will-change: transform;
  /* 启用硬件加速，防止重绘 */
  backface-visibility: hidden;
  transform: translateZ(0);
}

  .contact-item:hover {
    transform: translateY(-5px);
    border-radius: 8px;
  }

  .contact-item:active:not(:has(.contact-delete-btn:active)) {
    transform: translateY(-5px) scale(0.95);
    transition: transform 0.15s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }
  
  /* 当删除按钮被点击时，阻止卡片的active状态 */
  .contact-item:has(.contact-delete-btn:active) {
    transform: translateY(-5px);
    transition: none;
  }

/* 重复的样式已统一管理 */



/* 联系人预览相关样式已统一在上方管理 */

.contact-preview-title {
  margin: 0 auto 5px;
  padding: 2px 4px;
  font-size: 18px;
  color: #7170d2;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  position: relative;
}

.contact-preview-title:hover {
  text-decoration: underline;
  color: #5a59b8;

}


.contact-preview-title:hover::after {
  opacity: 1;
}

.theme-dark .contact-preview-title {
  font-weight: 300;
}

.contact-preview-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin: 0 auto 10px;
  background-color: rgb(0 0 0 / 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-preview-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}
.contact-preview-avatar:has(img) {
  border: 2px solid var(--text-on-accent);
}

.contact-preview-avatar span {
  color: #6f6de8;
  transform: translateX(1px);
  font-size: 14px;
  font-weight: 400;
}

.contact-preview-phone {
  font-size: 0.9em;
  color: #409EFF;
  margin-bottom: 5px;
}

.contact-preview-mobile,
.contact-preview-email {
  font-size: 0.9em;
  margin-bottom: 5px;
}

.contact-preview-email {
  font-size: 0.85em;
  margin-bottom: 8px;
  word-break: break-all;
}
.contact-preview-email.contact-draft-email-placeholder{
  margin-bottom: 5px!important;
}


.contact-draft-location-block {
  margin: 0px auto 5px!important;
  width: 60px;
  height: 12px;
  background-color: #d8d8d880;
}
.contact-preview-org,
.contact-preview-location,
.contact-preview-subtitle {
  font-size: 0.85em;
  margin-bottom: 1px;
  color: #898f9d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


/* 响应式布局 */
@media (max-width: 1200px) {
  .contact-list {
    grid-template-columns: repeat(auto-fill, minmax(235px, 1fr));
    gap: 10px;
  }
}

@media (max-width: 900px) {
  .contact-list {
    grid-template-columns: repeat(auto-fill, minmax(235px, 1fr));
    gap: 8px;
  }
}

@media (max-width: 600px) {
  .contact-list {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .Contact_Page {
    width: 95vw;
  }
  
  .contact-item {
    min-width: 100%;
    max-width: 100%;
  }
  
  /* contact-preview 样式已统一管理 */
  
  .search-results-title + .contact-list {
    justify-content: center;
  }
  
  .search-results-title + .contact-list .contact-item {
    flex: 0 0 100%;
    max-width: 100%;
    min-width: 100%;
  }
  
  .all-contacts .contact-item {
    flex: 0 0 100%;
    max-width: 100%;
    min-width: 100%;
  }
}

/* 表单样式 */
form {
  margin-top: 20px;
}

form label {
  display: block;
  margin-bottom: 4px;
  font-weight: bold;
}

form input, form textarea {
  width: 100%;
  padding: 8px;
  margin-bottom: 12px;
  border-radius: 4px;
  border: 1px solid var(--background-modifier-border);
  background-color: var(--background-primary);
  color: var(--text-normal);
}

form textarea {
  min-height: 100px;
  resize: vertical;
}

form button {
  padding: 8px 16px;
  margin-right: 8px;
  border-radius: 4px;
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
  cursor: pointer;
}

form button[type="submit"] {
  background-color: var(--interactive-accent);
}

form button:not([type="submit"]) {
  background-color: var(--interactive-normal);
  color: var(--text-normal);
}

/* 联系人详情样式 */
.contact-details {
  margin: 20px 0;
  padding: 15px;
  background-color: var(--background-secondary);
  border-radius: 4px;
}

.contact-details p {
  margin: 8px 0;
}

/* 文件夹选择器样式 */
.folder-list {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 10px;
}

.folder-option {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 4px;
}

.folder-option:hover {
  background-color: var(--background-modifier-hover);
}

/* 加载指示器样式 */
.contact-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  width: 100%;
}

.contact-loading-spinner {
  border: 4px solid var(--background-modifier-border);
  border-top: 4px solid var(--interactive-accent);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 进度相关样式已删除 */

/* 联系人卡片样式隔离 */
body:not(.plugin-contact-active) .markdown-reading-view .contact-preview {
  margin: 0 !important;
  padding: 10px !important;
  position: static !important;
  transform: none !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
}



/* 部门标题容器样式 */
.department-title-container {
  display: flex;
  align-items: center;
  margin: 6px 0 6px 0;
  padding: 8px 12px;
  background: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;

}




/* 部门标题样式 */
.department-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-normal);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
  flex-shrink: 0;
}

.department-title:hover {
  color: var(--interactive-accent);
}

.department-name {
  font-weight: 600;
}

/* 空部门名称样式 */
.department-name-empty {

  color: var(--text-muted);

}

.department-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  margin-right: 40px; /* 为右侧复制按钮留出空间 */
}

/* 部门分隔线 */
.department-divider {
  width: 100%;
  height: 1px;
  background-color: var(--background-modifier-border);
  margin: 20px 0;
}

/* 部门联系人列表样式 */
.department-contacts, 
.unclassified-contacts {
  margin-bottom: 15px;
  padding-bottom: 5px;
}



.recent-searches-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-normal);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  user-select: none;
  flex-shrink: 0;
}

.recent-searches-title:hover {
  color: var(--interactive-accent);
}

.recent-searches-name {
  font-weight: 600;
}

.recent-searches-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  margin-right: 40px;
}

.recent-searches-divider {
  width: 100%;
  height: 1px;
  background-color: var(--background-modifier-border);
  margin: 20px 0;
}

.recent-searches {
  margin-bottom: 15px;
}

/* 联系人卡片点击效果 */
.contact-item-clicked {
  transform: scale(0.98);
  transition: transform 0.2s ease;
}



/* 邮箱动画系统 - 波浪切换效果（整体动画） */

/* 波浪淡出动画 */
.email-wave-fadeout {
  animation: emailWaveFadeOut 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes emailWaveFadeOut {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1) skewX(0deg);
  }
  25% {
    opacity: 0.7;
    transform: translateY(-2px) scale(0.98) skewX(-1deg);
  }
  50% {
    opacity: 0.4;
    transform: translateY(-4px) scale(0.96) skewX(1deg);
  }
  75% {
    opacity: 0.2;
    transform: translateY(-6px) scale(0.94) skewX(-0.5deg);
  }
  100% {
  opacity: 0;
    transform: translateY(-8px) scale(0.92) skewX(0deg);
  }
}

/* 波浪淡入动画 */
.email-wave-fadein {
  animation: emailWaveFadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes emailWaveFadeIn {
  0% {
    opacity: 0;
    transform: translateY(8px) scale(0.92) skewX(0deg);
  }
  25% {
    opacity: 0.2;
    transform: translateY(6px) scale(0.94) skewX(0.5deg);
  }
  50% {
    opacity: 0.4;
    transform: translateY(4px) scale(0.96) skewX(-1deg);
  }
  75% {
    opacity: 0.7;
    transform: translateY(2px) scale(0.98) skewX(1deg);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1) skewX(0deg);
  }
}

/* 淡出动画 */
.email-fadeout {
    opacity: 0;
  transition: opacity 0.3s ease;
}

/* 淡入动画 */
.email-fadein {
    opacity: 1;
  transition: opacity 0.3s ease;
}

/* 成功状态样式 */
.email-success-state {
  display: flex !important;
  align-items: center;
  justify-content: center;
  gap: 6px;
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* 成功图标样式 */
.email-success-icon {
  display: inline-flex;
  align-items: center;
  flex-shrink: 0;
}

.email-success-icon svg {
  display: block;
}

.email-success-icon svg circle {
  fill: var(--color-green);
}

/* 成功文本样式 - 使用CSS变量而不是内联样式 */
.email-success-text {
  color: var(--color-green);
  font-weight: 500;
}




/* 联系人项目删除按钮 */
.contact-delete-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 28px;
  height: 28px;
  background-color: rgb(0 0 0 / 0.08);
  color: var(--text-on-accent);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease, background-color 0.2s ease;
  /* 阻止点击事件传播到父元素 */
  isolation: isolate;
  /* 创建独立的点击区域 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.contact-delete-btn:hover {
  background-color: var(--interactive-accent);
}

.contact-delete-btn-clicked {
  transform: scale(0.9);
  background-color: #ff3742 !important;
  transition: transform 0.1s ease, background-color 0.1s ease;
}

/* 创建删除按钮的扩展点击区域，防止误触卡片 */
.contact-delete-btn::before {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border-radius: 50%;
  background: transparent;
  z-index: 11;
}

/* 防止卡片在删除按钮区域的点击效果 */
.contact-item:has(.contact-delete-btn:hover) .contact-item-clicked {
  animation: none !important;
  transform: translateY(-5px) !important;
}

.contact-delete-btn .trash-icon {
  width: 16px;
  height: 16px;
}

/* contact-preview 位置样式已统一管理 */

.contact-item:hover .contact-delete-btn {
  opacity: 1;
}

/* 为不同主题调整垃圾桶图标的颜色 */
.theme-light .contact-delete-btn {
  color: #ffffff;
}

.theme-dark .contact-delete-btn {
  color: #ffffff;
}

/* 拖拽相关样式 */
.contact-avatar-dropzone {
  border: 2px dashed var(--interactive-accent);
  border-radius: 8px;
  background-color: rgba(var(--interactive-accent-rgb), 0.1);
  transition: all 0.3s ease;
  position: relative;
  transform: scale(1.02);
  box-shadow: 0 0 10px rgba(var(--interactive-accent-rgb), 0.4);
  z-index: 100;
}

.contact-avatar-dropzone::after {
  content: "拖放图片到此处";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 101;
  font-weight: bold;
}

.contact-avatar-dropzone .contact-preview-avatar {
  transform: scale(1.1);
  box-shadow: 0 0 0 4px rgba(var(--interactive-accent-rgb), 0.3);
}

/* 可拖拽头像样式 */
.contact-avatar-droppable {
  cursor: pointer;
  position: relative;
}
.contact-avatar-droppable img{
  margin: 0;
}
.contact-avatar-droppable::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  transition: opacity 0.3s ease;
}

.contact-avatar-droppable:hover::after {
  opacity: 1;
}

.contact-preview-avatar:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
  cursor: pointer;
}

/* 头像确认对话框 */
.avatar-confirm-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--background-primary);
  border-radius: 8px;
  padding: 20px;
  width: 300px;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  text-align: center;
}

.avatar-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.avatar-confirm-dialog h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
}

.avatar-confirm-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.avatar-confirm-buttons button {
  padding: 8px 16px;
  border-radius: 4px;
  background-color: var(--background-modifier-border);
  color: var(--text-normal);
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.avatar-confirm-buttons button.mod-cta {
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
}

.avatar-confirm-buttons button:hover {
  background-color: var(--interactive-hover);
}

/* 在内联链接中显示头像 */
.markdown-rendered .kks a[data-href]::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 5px;
  border-radius: 50%;
  background-color: var(--background-modifier-border);
  vertical-align: middle;
}

/* 使联系人头像在链接中显示为圆形 */
.markdown-rendered a[data-href]::before {
  background-size: cover;
  background-position: center center;
}

/* 对于组织选择器的修复 */
.organization-select:hover::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: -15px;
  left: 0;
  z-index: 99;
}

/* 对于部门选择器的修复 */
.department-select:hover::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: -15px;
  left: 0;
  z-index: 99;
}

/* 对于角色选择器的修复 */
.role-select:hover::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 20px;
  bottom: -15px;
  left: 0;
  z-index: 99;
}

/* 组织选择器的特殊样式 */
.organization-select:hover .custom-select-options {
  width: 450px;
  padding: 15px;
  left: 50%;
  transform: translateX(-50%);
  top: calc(100% + 10px);
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  max-height: none;
  overflow-y: visible;
  grid-gap: 10px;
}

/* 部门选择器的特殊样式 - 覆盖标准样式 */
.department-select:hover .custom-select-options {
  width: 600px;
  display: flex;
  flex-wrap: wrap;
  grid-template-columns: unset;
  max-height: none;
  padding: 15px;
  justify-content: space-between;
  align-content: flex-start;
  overflow-y: visible;
  
  /* 特殊定位，确保可见 */
  left: 50%;
  transform: translateX(-50%);
  top: calc(100% + 10px);
  position: absolute;
  border: 1px solid var(--background-modifier-border);
}

/* 角色选择器的特殊样式 */
.role-select:hover .custom-select-options {
  width: 250px;
  padding: 10px;
  left: auto;
  right: 0;
  transform: none;
  top: calc(100% + 10px);
  grid-template-columns: 1fr;
}

/* 组织选择器选项样式 */
.organization-select .custom-select-option {
  padding: 10px 15px;
  border-radius: 6px;
  margin: 5px;
  white-space: normal !important;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  min-height: 40px;
  text-align: left;
  font-size: 14px;
  overflow: hidden;
  flex: 0 1 auto;
}

.organization-select .custom-select-option:hover {
  background-color: var(--interactive-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border-color: var(--background-modifier-border);
}

.organization-select .custom-select-option.selected {
  background-color: rgba(var(--interactive-accent-rgb), 0.1);
  border-color: var(--interactive-accent);
  font-weight: 500 !important;
  color: var(--text-normal);
}

/* 确保通用选项样式不会覆盖特定的组织样式 */
.custom-select-option {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 拖拽提示样式 */
.contact-avatar-drag-tip {
  position: absolute;
  bottom: 0;
  right: 0;
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: scale(0.8);
  transition: opacity 0.3s ease, transform 0.3s ease;
  pointer-events: none;
  z-index: 10;
}

.contact-preview-avatar:hover .contact-avatar-drag-tip {
  opacity: 1;
  /* transform: scale(1); */
}

/* 修改联系人头像样式，确保可以正确显示拖拽提示 */
.contact-preview-avatar {
  position: relative;
  overflow: visible !important;
}

/* 新建联系人样式 - 优化版 */
.new-contact-item {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease-in-out;

}

/* new-contact-item 相关样式已统一管理 */

.new-contact-drop-zone {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 4px dashed var(--background-modifier-border) ;
  background-color: var(--background-primary);
  border-radius: 16px;
  padding: 16px;
  width: 100%;
  height: 100%;
  transition: all 0.2s ease;
}

.new-contact-drop-zone .plus-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  /* background: linear-gradient(135deg, var(--interactive-accent-hover), var(--interactive-accent)); */
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 12px;
}
.new-contact-drop-zone .plus-circle svg{
  color: var(--interactive-accent-hover);
}
.new-contact-drop-zone svg {
  color: white;
  width: 24px;
  height: 24px;
  transition: transform 0.2s ease;
}

.drop-text {
  font-size: 0.9em;
  color: var(--text-muted);
  font-weight: 500;
  text-align: center;
  padding: 0 5px;
}

.new-contact-item .contact-name {
  font-size: 1.1em;
  font-weight: 600;
  color: var(--text-normal);
  margin-top: 8px;
}

/* dragover .contact-preview 样式已统一管理 */

.new-contact-item.dragover .new-contact-drop-zone {
  border-color: var(--interactive-accent);
  background-color: var(--background-primary);
  transform: scale(1.02);
}

.new-contact-item.dragover .plus-circle {
  background: var(--interactive-accent);
  transform: scale(1.1) rotate(90deg);
}

/* 无匹配联系人提示样式 */
.no-contacts-message {
  text-align: center;
  color: var(--text-muted);
  margin: 16px 0;
  font-size: 1.1em;
}

/* 多级搜索样式 - 从 index(25).html 移植 */
.contact-obcm-search--container {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 50;
  transform-origin: center center;
  margin-top: 20px;
}

.contact-obcm-search--container.contact-obcm-search--scrolled {
  position: fixed;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.35);
}

.contact-obcm-search--input-container {
  display: flex;
  align-items: center;
  background: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 32px;
  width: 700px;
  min-width: 700px;
  height: 66px;
  padding: 12px 16px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05);
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  position: relative;
  overflow: hidden;
}

.contact-obcm-search--scrolled .contact-obcm-search--input-container {
  width: 350px;
  min-width: 350px;
  height: 46px;
  border-radius: 24px;
  padding: 8px 12px 8px 16px;
}

.contact-obcm-search--content {
  display: flex;
  align-items: center;
  width: 100%;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.contact-obcm-search--scrolled .contact-obcm-search--content {
  opacity: 0;
  pointer-events: none;
  transform: scale(0.9);
}

.contact-obcm-search--compact-content {
  opacity: 0;
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%) scale(1.5);
  display: flex;
  align-items: center;
  white-space: nowrap;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  pointer-events: none;
}

.contact-obcm-search--scrolled .contact-obcm-search--compact-content {
  opacity: 1;
  pointer-events: auto;
  transform: translateY(-50%) scale(1);
}

/* 搜索按钮样式 */
.contact-obcm-search--button {
  background: var(--interactive-accent);
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.contact-obcm-search--scrolled .contact-obcm-search--button {
  width: 30px;
  height: 30px;
  right: 8px;
}

.contact-obcm-search--button svg {
  color: white;
  width: 16px;
  height: 16px;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.contact-obcm-search--scrolled .contact-obcm-search--button svg {
  width: 12px;
  height: 12px;
}

.contact-obcm-search--inner-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: var(--background-primary);
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  opacity: 1;
}

/* 滚动状态下的内部容器样式 */
.contact-obcm-search--scrolled .contact-obcm-search--inner-container {
  background-color: transparent;
  opacity: 0.85;
}

/* 悬停时恢复完全不透明 */
.contact-obcm-search--scrolled:hover .contact-obcm-search--inner-container {
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.contact-obcm-search--inner-container .contact-obcm-search--results-container {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 40px);
  z-index: 9999;
}


.contact-obcm-search--input-container {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 32px;
  width: 700px;
  min-width: 700px;
  height: 66px;
  padding: 12px 16px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05);
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: center center;
  margin-top: 20px;
}

/* 初始加载状态 - 窄宽度 */
.contact-obcm-search--input-container.contact-obcm-search--initializing {
  max-width: 260px;
  border-radius: 50px;
}

/* 加载完成状态 - 展开宽度 */
.contact-obcm-search--input-container.contact-obcm-search--ready {
  max-width: 580px;
  border-radius: 44px;
  z-index: 9999;
}

/* 聚焦状态 */
.contact-obcm-search--input-container:focus-within {
  border-color: var(--interactive-accent);
  background-color: #fff;
}

/* 滚动状态 - 紧凑模式 */
.contact-obcm-search--input-container.contact-obcm-search--scrolled {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.35);
  max-width: 350px;
  min-width: 350px;
  width: 350px;
  border-radius: 24px;
  height: 46px;
  min-height: 46px;
  padding: 8px 12px 8px 16px;
  transform-origin: center center;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1),
              transform 0.6s cubic-bezier(0.16, 1, 0.3, 1),
              width 0.5s cubic-bezier(0.23, 1, 0.32, 1),
              height 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: 0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05);
  z-index: 1000;
  background: var(--background-primary);
  overflow: hidden;
  cursor: pointer;
  margin-top: 0;
}

/* 悬停状态 */
.contact-obcm-search--input-container.contact-obcm-search--scrolled:hover {
  transform: translate(-50%, -50%) scale(0.38);
}

/* 滚动状态下的内容隐藏 - 标签容器 (Airbnb风格分层动画) */
.contact-obcm-search--input-container.contact-obcm-search--scrolled .contact-obcm-search--tags-container {
  opacity: 0;
  transform: scale(0.9);
  pointer-events: none;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 滚动状态下的搜索输入框样式 (Airbnb风格分层动画) */
.contact-obcm-search--input-container.contact-obcm-search--scrolled .contact-obcm-search--search-input {
  opacity: 0;
  transform: scale(0.9);
  pointer-events: none;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 搜索框内容包装器（用于分层动画） */
.contact-obcm-search--input-container .contact-obcm-search--tags-input-wrapper {
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: center center;
  width: 100%;
}

/* 滚动状态下的内容包装器动画 */
.contact-obcm-search--input-container.contact-obcm-search--scrolled .contact-obcm-search--tags-input-wrapper {
  opacity: 0;
  transform: scale(0.9);
  pointer-events: none;
}

/* 紧凑内容 - 初始状态 (Airbnb风格：从大scale开始) */
.contact-obcm-search--input-container::before {
  content: "搜索联系人";
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%) scale(1.5);
  font-size: 14px;
  font-weight: 600;
  color: var(--text-muted);
  opacity: 0;
  pointer-events: none;
  white-space: nowrap;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  display: flex;
  align-items: center;
}

/* 滚动状态下的紧凑内容 - 显示状态 (Airbnb风格：平滑缩放到正常大小) */
.contact-obcm-search--input-container.contact-obcm-search--scrolled::before {
  opacity: 1;
  pointer-events: auto;
  transform: translateY(-50%) scale(1);
}

/* 滚动状态下的搜索图标调整 (Airbnb风格：保持流畅的尺寸过渡) */
.contact-obcm-search--input-container.contact-obcm-search--scrolled .contact-obcm-search--right-icon {
  width: 30px;
  height: 30px;
  right: 8px;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

.contact-obcm-search--input-container.contact-obcm-search--scrolled .contact-obcm-search--right-icon svg {
  width: 12px;
  height: 12px;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

/* 搜索组件其他元素 */

.contact-obcm-search--left-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  margin-right: 4px;
  flex-shrink: 0;
  color: var(--text-muted);
  transition: color 0.2s ease;
}

.contact-obcm-search--left-icon svg {
  width: 20px;
  height: 20px;
  color: #6b6a80;

}

.contact-obcm-search--input-container:focus-within .contact-obcm-search--left-icon {
  color: var(--interactive-accent);
}

.contact-obcm-search--tags-input-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-height: 36px;
  overflow: hidden;
}

.contact-obcm-search--tags-container {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.contact-obcm-search--tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  height: 28px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
  background-color: #e9e6fd;
  color: #5043b9;
  
  /* 使用更丝滑的动画 */
  animation: contact-obcm-search--tagSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 深色模式下的标签样式 */
.theme-dark .contact-obcm-search--tag {
  background-color: #2d2a4a;
  border: 1px solid #3d3a5a;
  color: #b8b3e6;
}

/* 悬停效果 */
.contact-obcm-search--tag:hover {
  background-color: #ddd6fc;
  border-color: #c4b9f5;
}

.theme-dark .contact-obcm-search--tag:hover {
  background-color: #363354;
  border-color: #4a4768;
}

.contact-obcm-search--tag.contact-obcm-search--removing {
  animation: contact-obcm-search--tagSlideOut 0.15s cubic-bezier(0.4, 0.0, 1, 1) forwards;
  pointer-events: none;
}

.contact-obcm-search--tag-icon {
  width: 14px;
  height: 14px;
}

.contact-obcm-search--tag-remove {
  width: 14px;
  height: 14px;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
  color: inherit;
  background: none!important;
  box-shadow: none!important;
}

.contact-obcm-search--tag-remove:hover {
  background: rgba(255, 255, 255, 0.2);
}

.contact-obcm-search--search-input {
  flex: 1;
  min-width: 120px;
  height: 28px;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  font-size: 14px;
  color: var(--text-normal);
  font-family: var(--font-interface);
  line-height: 1.4;
  padding: 0;
  box-shadow: none !important;
}

.contact-obcm-search--search-input:focus,
.contact-obcm-search--search-input:active,
.contact-obcm-search--search-input:focus-visible {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.contact-obcm-search--search-input::placeholder {
  color: var(--text-muted);
}



.contact-obcm-search--results-container {
  position: relative;
  z-index: 50;
  display: flex;
  justify-content: center;
  margin-top: 4px;
}

.contact-obcm-search--results-wrapper {
  position: relative;
  width: 100%;
  transform-origin: center;
}

.contact-obcm-search--search-results {
  position: absolute;
  top: 4px;
  left: 0;
  right: 0;
  max-height: 320px;
  overflow-y: auto;
  overflow-x: hidden;
  background: #ffffff;
  border: 1px solid var(--background-modifier-border);
  border-radius: 12px;
  
  /* 淡淡的阴影效果 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.04);
  
  /* 性能优化 */
  will-change: transform;
  
  /* 初始隐藏状态 */
  opacity: 0;
  transform: translateY(-8px);
  pointer-events: none;
  
  /* 流畅过渡 */
  transition: all 0.2s ease-out;
}

.contact-obcm-search--search-results.contact-obcm-search--hidden {
  display: none;
}

.contact-obcm-search--search-results.contact-obcm-search--hide {
  opacity: 0;
  transform: translateY(-4px);
  pointer-events: none;
}

/* 显示状态 */
.contact-obcm-search--search-results.contact-obcm-search--ready:not(.contact-obcm-search--hidden):not(.contact-obcm-search--hide) {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;

}

.contact-obcm-search--level-header {
  position: sticky;
  top: 0;
  z-index: 20;
  padding: 8px 12px;
  background: var(--background-secondary);
  border-bottom: 1px solid var(--background-modifier-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.contact-obcm-search--level-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-muted);
}

.contact-obcm-search--level-indicator {
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  background: var(--background-modifier-border);
  color: var(--text-muted);
  font-weight: 500;
}

.contact-obcm-search--results-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.contact-obcm-search--search-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.15s ease;
  animation: contact-obcm-search--fadeInUp 0.2s ease-out;
  animation-fill-mode: both;
}

.contact-obcm-search--search-item:hover {
  background: var(--background-modifier-hover);
  background-color: #e4e1fd;
  transform: translateY(-1px);
}

.contact-obcm-search--search-item.contact-obcm-search--keyboard-selected {
  background: var(--interactive-accent) !important;
  color: var(--text-on-accent) !important;
}

.contact-obcm-search--search-item.contact-obcm-search--keyboard-selected * {
  color: var(--text-on-accent) !important;
}

.contact-obcm-search--item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.contact-obcm-search--item-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.contact-obcm-search--item-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.contact-obcm-search--item-label {
  font-size: 14px;
  font-weight: 500;
  color: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-obcm-search--item-description {
  font-size: 12px;
  color: var(--text-muted);
  flex-shrink: 0;
}

.contact-obcm-search--item-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.contact-obcm-search--item-meta {
  font-size: 12px;
  color: var(--text-muted);
}

/* 动画 */
@keyframes contact-obcm-search--slideDown {
  from {
    transform: translateY(-8px);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes contact-obcm-search--slideUp {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-8px);
  }
}

@keyframes contact-obcm-search--fadeInUp {
  from {
    transform: translateY(8px);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes contact-obcm-search--tagSlideIn {
  from {
    transform: translateY(-2px);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes contact-obcm-search--tagSlideOut {
  from {
    opacity: 1;
    transform: scale(1) translateX(0);
  }
  to {
    opacity: 0;
    transform: scale(0.8) translateX(8px);
  }
}

/* 折叠功能和高级搜索样式 - 从 modal.ts 移植 */
.branch-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  background: #ffffff;
  padding: 8px 0px;
  border-radius: 6px;

}

.branch-title.collapsible-title {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  transition: all 0.2s ease;
}

.branch-title.collapsible-title:hover {
  color: var(--text-accent);
}

.collapse-arrow {
  transition: transform 0.2s ease;
  font-size: 0.8em;
  color: var(--text-muted);
}
.collapse-arrow svg{
  height: 20px!important;
  width: 20px!important;
}

.branch-name {
  font-weight: 600;
}

/* 空机构名称样式 */
.branch-name-empty {
  color: var(--text-muted);

}

.branch-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: inline-block!important;
  flex:none!important;
  padding: 0!important;
  align-items: center;
  margin-left: 12px;
  gap: 4px;
  color: var(--text-muted);
  font-size: 0.9em;
}
.stat-item:hover{
  background: none!important;
}
.stat-item svg {
  opacity: 0.7;
}

.stat-number {
  font-weight: 500;
  color: var(--text-normal);
}

.branch-content {
  transition: all 0.3s ease;
  overflow: visible;
  position: relative;
}

/* 折叠状态控制 */
/* 机构折叠：标题容器的下一个兄弟元素是内容容器 */
.branch-title-container:has(.branch-title[data-collapsed="true"]) + .branch-content {
  display: none;
}

/* 部门折叠：标题容器的下一个兄弟元素是联系人列表 */
.department-title-container:has(.department-title[data-collapsed="true"]) + .contact-list {
  display: none;
}

/* 最近搜索折叠：标题容器的下一个兄弟元素是联系人列表 */
.recent-searches-title-container:has(.recent-searches-title[data-collapsed="true"]) + .contact-list {
  display: none;
}

.branch-container {
  margin-bottom: 20px;
  border-radius: 8px;
  position: relative;
}

.department-container {
  position: relative;
}

/* 部门标题容器布局调整 */
.department-title-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 103%;
  position: sticky;
  top: -1px; /* 为机构标题留出空间 */
  gap: 12px;
  background: #ffffff;
  z-index: 1;
  padding: 8px 20px;
  margin-bottom: 8px;
  margin-left: -20px;
  margin-right: -20px;

}

/* 部门复制邮箱按钮 */
.department-copy-email-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  transition: color 0.2s ease, transform 0.1s ease;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  box-shadow: none!important;
  background-color: #ffffff!important;
}



.department-copy-email-btn:hover {
  color: var(--text-normal);
  background: none;
  box-shadow: none;
}

.department-copy-email-btn svg {
  width: 16px;
  height: 16px;
}

/* 复制按钮点击动画 */
.department-copy-btn-clicked {
  transform: translateY(-50%) scale(0.9);
  background: none;
  box-shadow: none;
}

/* 复制按钮成功状态 */
.department-copy-btn-success {
  color: var(--color-green);
  background: none;
  box-shadow: none;
}

.department-copy-btn-success svg {
  color: var(--color-green);
}



/* 高级搜索功能样式 */
.advanced-search-container {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 320px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.advanced-search-input {
  padding-right: 40px !important;
  transition: all 0.25s ease;
}

.advanced-search-input:focus {
  box-shadow: 0 0 0 2px var(--interactive-accent-hover) !important;
  border-color: var(--interactive-accent) !important;
}

.search-icon-container {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  z-index: 2;
}

.search-icon,
.send-icon {
  position: absolute;
  width: 18px;
  height: 18px;
  color: var(--text-muted);
  transition: all 0.2s ease;
  cursor: pointer;
}

.search-icon.hidden,
.send-icon.hidden {
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

.search-icon-svg,
.send-icon-svg {
  width: 100%;
  height: 100%;
}

.search-icon:hover,
.send-icon:hover {
  color: var(--interactive-accent);
}

/* 搜索下拉列表样式 */
.search-results-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.15);
  z-index: 1002;
  max-height: 300px;
  overflow-y: auto;
  backdrop-filter: blur(8px);
  animation: searchDropdownFadeIn 0.2s ease-out;
}

.search-results-dropdown.hidden {
  display: none;
}

.search-results-dropdown.show {
  display: block;
}

.search-results-dropdown.hide {
  animation: searchDropdownFadeOut 0.3s ease-in;
}

@keyframes searchDropdownFadeIn {
  from {
    transform: translateY(-8px);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes searchDropdownFadeOut {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-8px);
  }
}

/* 搜索下拉列表项样式 */
.search-dropdown-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.15s ease;
  border-bottom: 1px solid var(--background-modifier-border-hover);
  position: relative;
}

.search-dropdown-item:last-child {
  border-bottom: none;
}

.search-dropdown-item:hover,
.search-dropdown-item.selected {
  background: var(--background-modifier-hover);
  transform: translateX(2px);
}

.search-dropdown-item.no-results {
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
  cursor: default;
}

.search-dropdown-item.no-results:hover {
  background: transparent;
  transform: none;
}

.contact-info-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.contact-left-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.contact-dropdown-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--background-modifier-border);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;
}

.contact-dropdown-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.contact-dropdown-avatar span {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.contact-name-info {
  flex: 1;
  min-width: 0;
}

.contact-dropdown-name {
  font-weight: 500;
  color: var(--text-normal);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
}

.contact-dropdown-org {
  font-size: 12px;
  color: var(--text-muted);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.contact-right-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  flex-shrink: 0;
}

.contact-dropdown-phone,
.contact-dropdown-mobile {
  font-size: 12px;
  color: var(--text-muted);
  white-space: nowrap;
}

.contact-dropdown-phone {
  color: var(--interactive-accent);
}

/* 多级搜索选中状态样式 */
.contact-obcm-search--search-item.contact-obcm-search--selected {
  background-color: var(--background-modifier-hover);
  background-color: #e4e1fd;
  border-left: 3px solid var(--interactive-accent);
}

.contact-obcm-search--selected-icon {
  color: var(--interactive-accent);
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.contact-obcm-search--search-item.contact-obcm-search--selected .contact-obcm-search--item-label {
  color: var(--interactive-accent);
  font-weight: 500;
}

.contact-obcm-search--search-item.contact-obcm-search--selected .contact-obcm-search--item-meta {
  color: var(--interactive-accent);
}

/* 默认标签样式 */
.contact-obcm-search--tag-default {
  background-color: var(--interactive-accent);
  color: var(--text-on-accent);
  cursor: pointer;
}

.contact-obcm-search--tag-default:hover {
  background-color: var(--interactive-accent-hover);
}

.contact-obcm-search--tag-default .contact-obcm-search--tag-label {
  font-weight: 500;
}

.contact-obcm-search--tag-default .contact-obcm-search--tag-icon {
  color: var(--text-on-accent);
}

/* 机构标签（一级标签）样式 */
.contact-obcm-search--tag-organization {
  background-color: #e9e6fd;
  color: #5043b9;
  cursor: pointer;
  position: relative;
  font-weight: 500;
  margin-left: 5px;
}

.contact-obcm-search--tag-organization:hover {
  background-color: #ddd6fc;
  transform: translateY(-1px);
}

.contact-obcm-search--tag-organization .contact-obcm-search--tag-label {
  color: #5043b9;
  font-weight: 500;
  font-size: 14px;
}

.contact-obcm-search--tag-organization .contact-obcm-search--tag-icon {
  color: #5043b9;
}

/* 深色模式下的机构标签样式 */
.theme-dark .contact-obcm-search--tag-organization {
  background-color: var(--background-secondary-alt);
  border: 1px solid var(--background-modifier-border);
  color: var(--text-accent);
}

.theme-dark .contact-obcm-search--tag-organization:hover {
  background-color: var(--background-modifier-hover);
}

.theme-dark .contact-obcm-search--tag-organization .contact-obcm-search--tag-label {
  color: var(--text-accent);
}

.theme-dark .contact-obcm-search--tag-organization .contact-obcm-search--tag-icon {
  color: var(--text-accent);
}

/* 为机构标签添加点击提示 */
.contact-obcm-search--tag-organization::after {
  content: "点击更换";
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--background-primary);
  color: var(--text-muted);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  border: 1px solid var(--background-modifier-border);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.contact-obcm-search--tag-organization:hover::after {
  opacity: 1;
}

/* 新建联系人模态框样式 */
.new-contact-modal {
  padding: 20px;
}

.new-contact-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-section {
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  padding: 16px;
  background: var(--background-primary-alt);
}

.form-section h3 {
  margin: 0 0 16px 0;
  color: var(--text-accent);
  font-size: 16px;
  font-weight: 600;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 16px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  font-weight: 500;
  color: var(--text-normal);
  font-size: 14px;
}

.form-input {
  padding: 8px 12px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--interactive-accent);
  box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

.form-input::placeholder {
  color: var(--text-muted);
}

.avatar-group {
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

.avatar-preview {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--background-modifier-border);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  color: var(--text-muted);
  overflow: hidden;
}

.avatar-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.avatar-input {
  display: none;
}

.avatar-button {
  padding: 8px 16px;
  background: var(--interactive-normal);
  color: var(--text-on-accent);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.avatar-button:hover {
  background: var(--interactive-hover);
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid var(--background-modifier-border);
}

.form-button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background: var(--background-secondary);
  color: var(--text-normal);
}

.cancel-button:hover {
  background: var(--background-modifier-hover);
}

.save-button {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
}

.save-button:hover {
  background: var(--interactive-accent-hover);
}

/* 模板文件选择器样式 */
.template-file-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  margin-top: 10px;
}

.template-file-option {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--background-modifier-border);
  transition: background-color 0.2s ease;
}

.template-file-option:last-child {
  border-bottom: none;
}

.template-file-option:hover {
  background: var(--background-modifier-hover);
}

.template-file-option span {
  color: var(--text-normal);
  font-size: 14px;
}

.no-templates-message {
  padding: 20px;
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
}

/* ========================= 添加联系人模态框样式 ========================= */
/* 从111.html完整整合的样式 */

/* CSS变量定义 */
.contact-obcm-add--contact-form {
  /* 只保留引用Obsidian原生变量的定义 */
  --text-color: var(--text-normal);
  --text-muted: var(--text-muted);
  --bg-color: var(--background-primary);
  --card-bg: var(--background-secondary);
  --section-bg: var(--background-secondary);
  --input-bg: var(--background-modifier-form-field);
  --border-color: var(--background-modifier-border);
  --subtle-border: var(--background-modifier-border-hover);
  --border-radius: 8px;
  --shadow-color: var(--background-modifier-box-shadow);
  --icon-stroke: 1.5px;
  --transition-speed: 0.2s;
  
  /* 主容器样式 */
  box-sizing: border-box;
  transition: all var(--transition-speed) ease;
  max-width: 660px;
  background-color: var(--card-bg);
  border: none;
  animation: contact-obcm-add--fadeIn 0.5s ease;
  position: relative;
}

.contact-obcm-add--contact-form *,
.contact-obcm-add--contact-form *::before,
.contact-obcm-add--contact-form *::after {
  box-sizing: border-box;
  transition: all var(--transition-speed) ease;
}

/* 动画定义 */
@keyframes contact-obcm-add--fadeIn {
  from { opacity: 0; transform: translateY(8px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes contact-obcm-add--highlight {
  0% { border-bottom-color: var(--interactive-accent); }
  100% { border-bottom-color: var(--border-color); }
}

@keyframes contact-obcm-add--shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* 标题样式 */
.contact-obcm-add--contact-form h2 {
  margin: 0;
  font-size: 18px;
  color: var(--text-color);
  text-align: center;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: left;
  letter-spacing: 0.5px;
}

.contact-obcm-add--contact-form h2 svg {
  margin-right: 12px;
  color: var(--interactive-accent);
  stroke-width: var(--icon-stroke);
}

/* 表单组样式 */
.contact-obcm-add--form-group {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

/* 标签和输入框样式 */
.contact-obcm-add--contact-form label {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  position: relative;
}

.contact-obcm-add--contact-form label span {
  font-weight: 400;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  color: var(--text-muted);
  letter-spacing: 0.3px;
}

.contact-obcm-add--contact-form label span svg {
  display: none;
}

.contact-obcm-add--label-text {
  color: var(--text-muted);
  font-size: 14px;
  font-weight: 400;
}

/* 输入框通用样式 */
.contact-obcm-add--contact-form input,
.contact-obcm-add--contact-form textarea {
  padding: 6px 12px 8px;
  border: none;
  color: var(--text-color);
  font-size: 15px;
}

.contact-obcm-add--contact-form input:focus,
.contact-obcm-add--contact-form textarea:focus {
  outline: none;
  box-shadow: none;
}

.contact-obcm-add--contact-form input::placeholder,
.contact-obcm-add--contact-form textarea::placeholder {
  color: var(--text-muted);
  opacity: 0.7;
  font-size: 12px;
}

/* 文本域特殊样式 */
.contact-obcm-add--contact-form textarea {
  resize: vertical;
  font-family: inherit;
}

.contact-obcm-add--contact-form textarea::placeholder {
  font-size: 15px;
}

/* 智能识别区域样式 */
.contact-obcm-add--auto-parse-section {
  border: none;
  box-shadow: none;
  transition: all 0.3s ease;
  position: relative;
  height: auto;
  overflow: visible;
}

.contact-obcm-add--auto-parse-section h3 {
  margin: 0 0 16px 0;
  font-size: 15px;
  color: var(--text-color);
  display: flex;
  align-items: center;
  font-weight: 500;
}

.contact-obcm-add--auto-parse-section h3 svg {
  margin-right: 10px;
  color: var(--interactive-accent);
  width: 18px;
  height: 18px;
  stroke-width: var(--icon-stroke);
}

.contact-obcm-add--auto-parse-container {
  display: flex;
  flex-direction: column;
  border: 2px dashed var(--border-color);
  width: 100%;
  margin: 0 auto;
  border: 2px dashed #c2c2c2;
  border-radius: 20px;
  overflow: hidden;
  background-color: var(--input-bg);
  text-align: center;
  transition: all 0.3s ease;


}

.contact-obcm-add--auto-parse-container.contact-obcm-add--drag-over {
  border: 2px dashed var(--interactive-accent);
  background-color: rgba(85, 91, 255, 0.05);
  box-shadow: 0 0 10px rgba(85, 91, 255, 0.2);
  transform: translateY(-2px);
}

.contact-obcm-add--auto-parse-container textarea {
  width: 100%;
  min-height: 100px;
  padding: 20px 20px 10px;
  box-sizing: border-box;
  border: none;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  background-color: var(--input-bg);
  background-color: #f2f5f8;
  font-size: 15px;
  color: var(--text-color);
  resize: none;
  overflow: hidden;
  transition: all 0.3s ease;
  white-space: pre-wrap;
  word-wrap: break-word;
  text-align: left;
}

.contact-obcm-add--auto-parse-container textarea:focus {
  outline: none;
  box-shadow: 0 0 0 1px var(--interactive-accent);
}

.contact-obcm-add--auto-parse-container textarea.contact-obcm-add--has-content {
  color: var(--text-color);
}

/* 按钮容器样式 */
.contact-obcm-add--buttons-container {
  display: flex;
  padding: 0 20px 20px;
  background-color: var(--input-bg);
  background-color: #f2f5f8;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.contact-obcm-add--upload-btn {
  width: 90px;
  height: 30px;
  padding: 0;
  background-color: var(--input-bg);
  color: var(--text-muted);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: row!important;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
  font-size: 14px;
}

.contact-obcm-add--upload-btn:hover {
  background-color: var(--input-bg);
  color: var(--text-normal);
  border-color: var(--border-color);
}

.contact-obcm-add--upload-btn .contact-obcm-add--button-icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}

/* 表单分组样式 */
.contact-obcm-add--form-section {
  margin-bottom: 18px;
  padding: 22px;
  background-color: var(--section-bg);
  border-radius: var(--border-radius);
  box-shadow: none;
  position: relative;
  border: none;
  transition: all 0.3s ease;
}

.contact-obcm-add--form-section-title {
  margin: 18px 0 10px 0;
  font-size: 15px;
  font-weight: 500;
  color: var(--text-color);
  display: flex;
  align-items: center;
}

.contact-obcm-add--form-section-title svg {
  margin-right: 7px;
  width: 18px;
  height: 18px;
  stroke-width: var(--icon-stroke);
}

/* 合并的表单区域 */
.contact-obcm-add--combined-sections {
  display: flex;
  gap: 20px;
  /* padding: 22px 0px 32px; */
  border-radius: 20px;
  box-shadow: none;
  position: relative;
  border: none;
  transition: all 0.3s ease;
}

.contact-obcm-add--combined-sections .contact-obcm-add--form-section {
  flex: 1;
  margin-bottom: 0;
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
}

/* 确保combined-sections中的input元素有边框 */
.contact-obcm-add--combined-sections input {
  border: 1px solid var(--background-modifier-border) !important;
}

.contact-obcm-add--combined-sections input:focus {
  border-color: var(--interactive-accent) !important;
}

.contact-obcm-add--combined-sections .contact-obcm-add--form-section:hover {
  background-color: transparent;
}

/* 表单操作区域 */
.contact-obcm-add--form-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.contact-obcm-add--form-tip {
  font-size: 12px;
  color: var(--text-muted);
  padding: 0;
  opacity: 0.8;
  margin-right: auto;
  display: flex;
  align-items: center;
}

.contact-obcm-add--buttons-group {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 30px;
}

/* 重置按钮样式 */
.contact-obcm-add--reset-all-btn {
  padding: 10px 16px;
  background-color: var(--interactive-normal);
  color: var(--text-muted);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  min-height: 42px;
  line-height: 1;
  box-shadow: none!important;
}

.contact-obcm-add--reset-all-btn:hover {
  background-color: rgba(255, 100, 100, 0.1);
  color: #ff6464;
  border-color: rgba(255, 100, 100, 0.2);
}

.contact-obcm-add--reset-all-btn:active {
  background-color: var(--interactive-active);
}

.contact-obcm-add--reset-all-btn svg {
  width: 16px;
  height: 16px;
}

/* 提交按钮样式 */
.contact-obcm-add--submit-btn {
  padding: 10px 10px;
  background-color: var(--interactive-normal);
  color: var(--text-muted);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 42px;
  line-height: 1;
  box-shadow: none!important;
}

.contact-obcm-add--submit-btn.contact-obcm-add--active {
  background-color: var(--interactive-accent);
  color: white;
  border-color: var(--interactive-accent);
  transform: translateY(0);
}

.contact-obcm-add--submit-btn.contact-obcm-add--active:hover {
  background-color: var(--interactive-accent-hover);
  border-color: var(--interactive-accent-hover);
}

/* 字段验证样式 */
.contact-obcm-add--contact-form input:required {
  border-bottom-color: var(--border-color);
}

.contact-obcm-add--contact-form input:required.contact-obcm-add--invalid {
  border-bottom-color: #ff6464;
}

.contact-obcm-add--label-text.contact-obcm-add--invalid {
  color: #ff6464 !important;
  font-weight: 500;
}

.contact-obcm-add--shake-animation {
  animation: contact-obcm-add--shake 0.5s ease;
}

.contact-obcm-add--required-field {
  color: #ff6464;
  font-size: 12px;
  margin-left: 5px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.contact-obcm-add--required-field.contact-obcm-add--visible {
  opacity: 1;
}

.contact-obcm-add--field-highlight {
  animation: contact-obcm-add--highlight 1.2s;
}

/* 邮箱修正提示样式 */
.contact-obcm-add--email-correction-hint {
  color: #6BCB77;
  font-size: 0.75rem;
  padding-left: 0.5rem;
  display: block;
  margin-top: 0.25rem;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.contact-obcm-add--email-correction-hint:hover {
  color: #5aad65;
}

.contact-obcm-add--email-correction-hint::before {
  content: "✓ ";
  font-weight: bold;
}

.contact-obcm-add--email-correction-hint::after {
  content: "点击切换";
  font-size: 0.7rem;
  opacity: 0;
  margin-left: 4px;
  transition: opacity 0.2s ease;
}

.contact-obcm-add--email-correction-hint:hover::after {
  opacity: 0.8;
}

.contact-obcm-add--email-original-value {
  color: #ffa500;
  font-style: italic;
}

input[type="email"].contact-obcm-add--corrected {
  background-color: transparent;
  border-bottom-color: #6BCB77;
  transition: all 0.3s ease;
}

/* 主题适配 */
.theme-light .contact-obcm-add--contact-form {
  background-color: var(--modal-background);
}

.theme-dark .contact-obcm-add--contact-form {
  /* 移除重复的变量定义，让其继承主要定义中的变量 */
}

/* 响应式设计 */
@media (max-width: 600px) {
  .contact-obcm-add--form-group {
    gap: 16px;
  }
  
  .contact-obcm-add--auto-parse-container {
    flex-direction: column;
  }
  
  .contact-obcm-add--combined-sections {
    flex-direction: column;
    gap: 20px;
    padding: 22px;
  }
  
  .contact-obcm-add--combined-sections .contact-obcm-add--form-section {
    padding: 0;
  }
}

/* ========================= 添加联系人按钮样式 ========================= */
.Contact_Page .contact-add-button {
  position: absolute;
  bottom: 30px;
  right: 30px;
  width: 56px;
  height: 56px;
  background-color: var(--interactive-accent);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  opacity: 0.5;
}

.Contact_Page .contact-add-button:hover {
  background-color: var(--interactive-accent-hover);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  opacity: 1;
}

.Contact_Page .contact-add-button:active {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.Contact_Page .contact-add-button svg {
  width: 28px;
  height: 28px;
  stroke-width: 2.5;
  transition: transform 0.2s ease;
}

.Contact_Page .contact-add-button:hover svg {
  transform: rotate(90deg);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .Contact_Page .contact-add-button {
    width: 48px;
    height: 48px;
    bottom: 20px;
    right: 20px;
  }
  
  .Contact_Page .contact-add-button svg {
    width: 24px;
    height: 24px;
  }
}

/* 主题适配 */
.theme-light .Contact_Page .contact-add-button {
  background-color: var(--interactive-accent);
  color: white;
  /* box-shadow: 0 4px 12px rgba(85, 91, 255, 0.3); */ /* Removed hardcoded shadow */
}

.theme-light .Contact_Page .contact-add-button:hover {
  background-color: var(--interactive-accent-hover);
  /* box-shadow: 0 8px 20px rgba(85, 91, 255, 0.4); */ /* Removed hardcoded shadow */
}

.theme-dark .Contact_Page .contact-add-button {
  background-color: var(--interactive-accent);
  color: white;
  /* box-shadow: 0 4px 12px rgba(85, 91, 255, 0.4); */ /* Removed hardcoded shadow */
}

.theme-dark .Contact_Page .contact-add-button:hover {
  background-color: var(--interactive-accent-hover);
  /* box-shadow: 0 8px 20px rgba(85, 91, 255, 0.5); */ /* Removed hardcoded shadow */
}
/* ========================= 添加联系人按钮样式结束 ========================= */


/* 当有内容时应用背景色 */
.Contact_Page .contact-results:has(.contacts-container) {
  background-color: #ffffff;
}

/* 深色主题适配 */
.theme-dark .Contact_Page .contact-results:has(.contacts-container) {
  background-color: var(--background-primary);
}

/* 联系人管理模态框的header样式 */
.modal-container:has(.Contact_Page) .modal-header {
  display: flex !important;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 0px;
  background-color: #fff;
  min-height: 30px;
  position: relative;
  margin-bottom: 0;
}

.modal-container:has(.Contact_Page) .modal-header .modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-normal);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-container:has(.Contact_Page) .modal-header .modal-title::before {
  content: "👥";
  font-size: 20px;
  margin-right: 4px;
}

/* 新增联系人模态框的header样式 */
.modal-container:has(.contact-obcm-add--contact-form) .modal-header {
  position: relative;
  top:-10px;
}

.modal-container:has(.contact-obcm-add--contact-form) .modal-header .modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-normal);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 620px;
  border-bottom: 1px solid #e2e4e3;
  position: relative;
  padding: 0px 30px 20px;
  left: -40px;

}

.modal-container:has(.contact-obcm-add--contact-form) .modal-header .modal-title svg {
  width: 22px;
  height: 22px;
  color: var(--interactive-accent);
  margin-right: 4px;
}

/* 关闭按钮样式 - 绝对定位到右上角 */
.modal-container:has(.Contact_Page) .modal-close-button,
.modal-container:has(.contact-obcm-add--contact-form) .modal-close-button {
  position: absolute !important;
  top: 10px;
  right: 10px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: var(--text-muted);
  z-index: 10;
}

.modal-container:has(.Contact_Page) .modal-close-button:hover,
.modal-container:has(.contact-obcm-add--contact-form) .modal-close-button:hover {
  background-color: var(--background-modifier-hover);
  color: var(--text-normal);
}

/* 调整Contact_Page的高度，为header留出空间 */
.modal-container:has(.Contact_Page) .Contact_Page {
  height: calc(80vh - 60px);
  margin-top: 0;
  padding-top: 0;
}

/* 防止模态框影响全局样式 */
.workspace-split.mod-root {
  position: relative !important;
}

/* ========================= 添加联系人表单样式结束 ========================= */

/* 阻止Obsidian设置可能导致内容位移的body内联样式 - 仅在模态框打开时生效 */
.modal-container:has(.Contact_Page) body.plugin-contact-active,
.modal-container:has(.contact-obcm-add--contact-form) body.plugin-contact-active {
  --zoom-factor: 1 !important;
  position: static !important;
  transform: none !important;
  top: 0 !important;
  left: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

.modal-container .workspace-split.mod-root {
  position: relative !important;
}

/* 防止body样式被干扰 - 这条规则将被删除 */

/* 确保插件样式不影响笔记内容 - 仅应用于联系人模态框打开时 */
.modal-container .markdown-source-view,
.modal-container .markdown-preview-view,
.modal-container .workspace-leaf-content[data-type="markdown"] .view-content {
  margin: 0 auto !important;
  padding: var(--file-margins) !important;
  transform: none !important;
  left: auto !important;
  right: auto !important;
  width: 100% !important;
  max-width: var(--file-line-width) !important;
  position: relative !important;
  top: 0 !important;
}

.contact-obcm-add--auto-parse-container textarea.contact-obcm-add--has-content:focus {
  box-shadow: 0 0 0 1px var(--interactive-accent);
}

/* 模态框基础样式 */

.modal:has(.contact-obcm-add--contact-form) {
  padding: 40px 40px 30px;
  width: 620px;
  border-radius: 20px;
}

/* ========================= 统一标签池样式 ========================= */

/* 统一标签池容器 */
.contact-obcm-add--unified-tags-pool {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 6px;
  min-height: 60px;
  padding: 12px;
  border-radius: 8px;
  background: var(--background-primary);
  transition: all 0.2s ease;
  cursor: text;
}

/* 标签池项目 */
.contact-obcm-add--tag-pool-item {
  display: inline-flex;
  align-items: center;
  padding: 2px 12px;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
  font-weight: 500;
  user-select: none;
  position: relative;
  overflow: hidden;
  /* border: 1px solid transparent; */
}

/* 未选中状态 */
.contact-obcm-add--tag-pool-item.unselected {
  background: #ebebeb;
  color: #777;
  border-color: var(--background-modifier-border);
}

.contact-obcm-add--tag-pool-item.unselected:hover {

  color: var(--text-normal);
  border-color: var(--interactive-accent);
}

/* 选中状态 */
.contact-obcm-add--tag-pool-item.selected {
  background-color: #e7e7e7 !important;
  border-color: #cbcbcb !important;
  color: #404040 !important;
}

.contact-obcm-add--tag-pool-item.selected:hover {
  background: var(--interactive-accent-hover);
  transform: scale(1.05);
}

/* 选中标签的对勾图标 */
.contact-obcm-add--tag-pool-item.selected .contact-obcm-add--tag-check {
  margin-left: 6px;
  display: flex;
  align-items: center;
}


/* 标签文本 */
.contact-obcm-add--tag-text {
  font-weight: 400;
  letter-spacing: 0.3px;
}

/* 内联空状态消息 */
.contact-obcm-add--tags-pool-empty-inline {
  color: var(--text-muted);
  font-style: italic;
  font-size: 13px;
  opacity: 0.7;
}

/* 内联标签输入框 */
.contact-obcm-add--inline-tag-input {
  flex: 1;
  min-width: 120px;
  max-width: 300px;
  border: none;
  outline: none;
  background: transparent;
  color: var(--text-normal);
  font-size: 14px;
  padding: 0px 8px !important;
  height: 23px !important;
  border-radius: 4px;
}

.contact-obcm-add--inline-tag-input:focus {
  min-width: 140px;
}

.contact-obcm-add--inline-tag-input::placeholder {
  color: var(--text-muted);
  opacity: 0.6;
  font-style: italic;
}

/* 动画效果 */
@keyframes contact-obcm-add--tagSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-10px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes contact-obcm-add--tagSelect {
  0% {
    transform: scale(1);
    background: var(--background-secondary);
  }
  50% {
    transform: scale(1.1);
    background: var(--interactive-accent);
  }
  100% {
    transform: scale(1);
    background: var(--interactive-accent);
  }
}

@keyframes contact-obcm-add--checkBounce {
  0% {
    opacity: 0;
    transform: scale(0) rotate(-180deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(-90deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* 保存到草稿按钮样式 */
.contact-obcm-add--save-draft-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 10px;
  background: var(--background-secondary);
  color: var(--text-normal);
  border: 1px solid var(--background-modifier-border);
  border-radius: 8px;
  height: 42px;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  margin-right: auto;
  box-shadow: none!important;
}

.contact-obcm-add--save-draft-btn:hover {
  background: var(--background-modifier-hover);
  border-color: var(--background-modifier-border-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contact-obcm-add--save-draft-btn:active {
  transform: translateY(0);
}

.contact-obcm-add--save-draft-btn svg {
  flex-shrink: 0;
}




/* 响应式设计 */
@media (max-width: 600px) {
  .contact-obcm-add--unified-tags-pool {
    padding: 8px;
    gap: 6px;
    min-height: 50px;
  }
  
  .contact-obcm-add--tag-pool-item {
    font-size: 12px;
    padding: 5px 10px;
  }
  
  .contact-obcm-add--inline-tag-input {
    font-size: 13px;
    min-width: 100px;
    max-width: 250px;
    padding: 4px 6px;
  }
  
  .contact-obcm-add--inline-tag-input:focus {
    min-width: 150px;
  }
  
  .contact-obcm-add--tags-pool-empty-inline {
    font-size: 12px;
  }
}

/* ========================= 标签选择器样式结束 ========================= */

/* ========================= 草稿联系人样式 ========================= */

/* 草稿联系人卡片 */
.contact-draft-item {
  position: relative;
}

.contact-draft-item::before {
  content: "草稿";
  position: absolute;
  top: -12px;
  right: -14px;
  background: var(--interactive-accent);
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 14px;
  z-index: 10;
  font-weight: 400;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  opacity: 0;
}
.contact-draft-item:hover::before {
  opacity: 1;
}
/* contact-draft-item .contact-preview 样式已统一管理 */

.contact-draft-item .contact-preview-title {
  color: var(--interactive-accent);
  font-weight: 400;
}

.contact-draft-item:hover .contact-preview-avatar {
  border-style: solid;
  opacity: 1;
}

/* 草稿占位符样式 */
.contact-draft-placeholder {
  background: #deddea;
  border-radius: 4px;
  min-height: 13px;
  margin: 0px auto 5px!important;
  position: relative;
  overflow: hidden;
}


/* 公共 shimmer 动画定义，必须保留 */
@keyframes shimmer {
  0% {
    left: -150%;
  }
  100% {
    left: 150%;
  }
}

/* 所有目标子元素的通用静态样式 */
.contact-draft-phone-block,
.contact-draft-mobile-block,
.contact-draft-email-block,
.contact-draft-org-block,
.contact-draft-title-block,
.contact-draft-location-block {
  position: relative;
  overflow: hidden;
}

/* hover 时由父级 contact-draft-item 触发 shimmer 动画 */
.contact-draft-item:hover .contact-draft-phone-block::before,
.contact-draft-item:hover .contact-draft-mobile-block::before,
.contact-draft-item:hover .contact-draft-email-block::before,
.contact-draft-item:hover .contact-draft-org-block::before,
.contact-draft-item:hover .contact-draft-title-block::before,
.contact-draft-item:hover .contact-draft-location-block::before {
  content: '';
  position: absolute;
  top: 0;
  left: -150%;
  width: 150%;
  height: 100%;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shimmer 1.5s infinite;
  pointer-events: none;
  z-index: 1;
}
/* 不同类型占位符的宽度设置 */
.contact-draft-name-block {
  width: 80px;
  height: 10px;
}

.contact-draft-phone-block {
  width: 75px;
  height: 10px;
}

.contact-draft-mobile-block {
  width: 100px;
  height: 10px;
}

.contact-draft-email-block {
  width: 150px;
  height: 12px;
}

.contact-draft-org-block {
  width: 150px;
  height: 10px;
}

.contact-draft-title-block {
  width: 80px;
  height: 10px;
}

.contact-draft-location-block {
  width: 45px;
  height: 10px;
}

/* 草稿头像占位符 */
.contact-draft-avatar {
  border: 2px dashed rgba(var(--interactive-accent-rgb), 0.3);
}

.contact-draft-avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(var(--interactive-accent-rgb), 0.6);
  background: rgba(var(--interactive-accent-rgb), 0.05);
  border-radius: 50%;
}

.contact-draft-avatar-placeholder svg {
  stroke: rgba(var(--interactive-accent-rgb), 0.6);
}

/* 草稿姓名样式 */
.contact-draft-name {
  color: var(--interactive-accent) !important;
  font-weight: 500;
}

/* 草稿时间样式 */
.contact-draft-date {
  color: var(--text-muted);
  font-size: 11px;
  opacity: 0;
}
.contact-draft-item:hover .contact-draft-date {
  opacity: 1;
}

/* 懒加载占位符动画 */
.contact-draft-placeholder {
  margin: 0 auto;
}

@keyframes draft-pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 深色主题下的草稿样式 */
.theme-dark .contact-draft-placeholder {
  background: #6b6c73;
}

.theme-dark .contact-draft-placeholder:hover {
  background: #7a7b82;
}

.theme-dark .contact-draft-avatar {
  background: rgba(var(--interactive-accent-rgb), 0.15);
  border-color: rgba(var(--interactive-accent-rgb), 0.4);
}

.theme-dark .contact-draft-avatar-placeholder {
  background: rgba(var(--interactive-accent-rgb), 0.08);
}

/* 响应式草稿样式 */
@media (max-width: 600px) {
  .contact-draft-item::before {
    font-size: 9px;
    padding: 1px 4px;
    top: 6px;
    right: 6px;
  }
  
  .contact-draft-item .contact-preview-title::after {
    display: none;
  }
  
  .contact-draft-placeholder {
    font-size: 0.8em;
    gap: 4px;
    padding: 3px 6px;
  }
  
  .contact-draft-placeholder-text {
    font-size: 0.9em;
  }
}

/* ========================= 草稿联系人样式结束 ========================= */

/* ========================= 浮动添加联系人按钮样式 ========================= */
/* 圆形半透明浮动按钮 */
.contact-add-button-floating {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--interactive-accent);
  color: white;
  border: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  opacity: 0.6;
}

.contact-add-button-floating:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
  opacity: 1;
  background-color: var(--interactive-accent-hover);
}

.contact-add-button-floating:active {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.contact-add-button-floating svg {
  width: 24px;
  height: 24px;
  stroke-width: 2;
  transition: transform 0.2s ease;
  color: #fff!important;
}

.contact-add-button-floating:hover svg {
  transform: rotate(90deg);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .contact-add-button-floating {
    width: 48px;
    height: 48px;
    bottom: 16px;
    right: 16px;
  }
  
  .contact-add-button-floating svg {
    width: 20px;
    height: 20px;
  }
}

/* 深色主题样式 */
.theme-dark .contact-add-button-floating {
  background-color: var(--interactive-accent);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.theme-dark .contact-add-button-floating:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
}

/* 浅色主题样式 */
.theme-light .contact-add-button-floating {
  background-color: var(--interactive-accent);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.theme-light .contact-add-button-floating:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
}
/* ========================= 浮动添加联系人按钮样式结束 ========================= */

/* ========================= 搜索提示样式 ========================= */
.search-hint-container {
  margin-bottom: 16px;
}

.search-hint {
  padding: 12px 16px;
  border: 1px solid #ffda9f;
  border-radius: 8px;
  margin: 20px auto 0px;
  font-size: 13px;
  color: #ba7a2d;
  background: #fdf6e9;
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-dark .search-hint {
  background: linear-gradient(135deg, #2c2416 0%, #3d2f1a 100%);
  border-color: #d68910;
  color: #f4d03f;
}

.hint-link {
  color: #e67e22;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.hint-link:hover {
  color: #d35400;
}

.theme-dark .hint-link {
  color: #f39c12;
}

.theme-dark .hint-link:hover {
  color: #e67e22;
}
/* ========================= 搜索提示样式结束 ========================= */

/* ========================= 响应式设计 - 搜索组件 ========================= */
@media (max-width: 640px) {
  .contact-obcm-search--input-container {
    width: 320px;
    min-width: 320px;
    max-width: 95vw;
  }
  
  .contact-obcm-search--search-input {
    min-width: 60px;
  }
  
  .contact-obcm-search--search-results {
    max-height: 250px;
  }
}
/* ========================= 响应式设计结束 ========================= */

/* ========================= 联系人预览样式 - 统一管理 ========================= */

/* 联系人预览基础样式 */
.contact-preview {
  /* 基础布局 */
  width: 100%;
  height: 222px;
  padding: 15px;
  position: relative;
  
  /* 外观样式 */
  border-radius: 12px;
  overflow: hidden;
  background-color: #fff;
  color: var(--text-normal);
  text-align: center;
  
  /* 初始阴影效果 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04);
  
  /* 卡片样式2 - 边框和高级阴影 */
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
}

/* 深色主题下的联系人预览 */
.theme-dark .Contact_Page .contact-preview {
  background-color: #20242a;
}

/* 联系人项目悬停效果 */
.contact-item:hover .contact-preview {
  /* 卡片样式1 - 白色背景 */
  background-color: #fff !important;
  
  /* 卡片样式2 - 高级阴影 */
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  
  /* 渐变背景纹理 */
  background-color: #f4f4f4;
}

/* 联系人项目悬停时的纹理效果 */
.contact-item:hover .contact-preview::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: radial-gradient(circle at center, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
  background-size: 4px 4px;
}

/* 响应式设计 - 600px以下 */
@media (max-width: 600px) {
  .contact-preview {
    width: 100%;
  }
}

/* 特殊用途的联系人预览 */
body:not(.plugin-contact-active) .markdown-reading-view .contact-preview {
  margin: 0 !important;
  padding: 10px !important;
  position: static !important;
  transform: none !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
}

/* 新建联系人预览样式 */
/* new-contact-item .contact-preview 样式已统一管理 */

/* dragover 样式已统一管理 */

/* 草稿联系人预览样式 */
/* contact-draft-item 样式已统一管理 */

/* ========================= 联系人预览样式结束 ========================= */

/* ========================= 滚动响应式样式 ========================= */
/* 当页面滚动时，contact-results扩大显示范围 */
.contact-results.scroll-expanded {
  max-height: calc(100vh - 60px);
  transition: max-height 0.3s ease-out;
}

/* 当页面滚动时，search-container缩小显示范围 */
/* .search-container.scroll-shrunk {
  height: 40%;
  transition: height 0.3s ease-out;
} */

/* ==================== 虚拟滚动样式 ==================== */

/* 虚拟滚动视口容器 */
.contact-viewport {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  will-change: transform;
  /* 启用硬件加速 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* 虚拟滚动内容容器 */
.contact-viewport-content {
  position: relative;
  width: 100%;
  min-height: 100%;
}

/* 虚拟滚动项目容器 */
.virtual-scroll-item {
  position: absolute;
  width: 100%;
  will-change: transform;
  /* 平滑过渡 */
  transition: transform 0.15s ease-out;
}

/* 虚拟滚动项目进入视口时的动画 */
.virtual-scroll-item.entering {
  opacity: 0;
  transform: translateY(20px);
  animation: virtualItemEnter 0.2s ease-out forwards;
}

/* 虚拟滚动项目离开视口时的动画 */
.virtual-scroll-item.leaving {
  animation: virtualItemLeave 0.15s ease-in forwards;
}

@keyframes virtualItemEnter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes virtualItemLeave {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* 虚拟滚动优化：缓冲区样式 */
.virtual-scroll-buffer {
  height: 0;
  overflow: hidden;
  pointer-events: none;
}

/* 虚拟滚动：滚动指示器 */
.virtual-scroll-indicator {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 10;
}

/* 滚动时显示指示器 */
.contact-viewport.scrolling .virtual-scroll-indicator {
  opacity: 1;
}

.theme-dark .virtual-scroll-indicator {
  background: rgba(255, 255, 255, 0.15);
}

/* 虚拟滚动：性能监控面板（开发模式） */
.virtual-scroll-debug {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  z-index: 1000;
  pointer-events: none;
  opacity: 0.8;
}

.theme-dark .virtual-scroll-debug {
  background: rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
}

/* 虚拟滚动：空状态样式 */
.virtual-scroll-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-muted);
  text-align: center;
}

.virtual-scroll-empty-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

/* 优化：减少重排重绘 */
.contact-list.virtual-enabled {
  contain: layout style paint;
}

.contact-list.virtual-enabled .contact-item {
  contain: layout style paint;
}

/* 虚拟滚动：加载状态 */
.virtual-scroll-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  color: var(--text-muted);
}

.virtual-scroll-loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid var(--text-muted);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

/* 虚拟滚动：平滑滚动优化 */
.contact-viewport.smooth-scroll {
  scroll-behavior: smooth;
}

/* 对于支持的浏览器，启用滚动快照 */
@supports (scroll-snap-type: y mandatory) {
  .contact-viewport.snap-scroll {
    scroll-snap-type: y proximity;
  }
  
  .contact-viewport.snap-scroll .virtual-scroll-item {
    scroll-snap-align: start;
  }
}

/* 响应式虚拟滚动 */
@media (max-width: 768px) {
  .virtual-scroll-item {
    transition: none; /* 移动设备禁用过渡以提升性能 */
  }
  
  .contact-viewport {
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .virtual-scroll-indicator {
    background: currentColor;
    opacity: 0.8;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .virtual-scroll-item,
  .virtual-scroll-item.entering,
  .virtual-scroll-item.leaving {
    animation: none;
    transition: none;
  }
  
  .contact-viewport.smooth-scroll {
    scroll-behavior: auto;
  }
}

/* ==================== 加载和错误状态样式 ==================== */

/* 加载状态容器 */
.contact-loading-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 1;
  transition: opacity 0.2s ease;
}

/* 加载状态淡出动画 */
.contact-loading-state.fading-out {
  opacity: 0;
  pointer-events: none;
}

/* 加载指示器 */
.contact-loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: var(--text-muted);
}

/* 加载动画 */
.contact-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid transparent;
  border-top: 3px solid var(--interactive-accent);
  border-radius: 50%;
  animation: contactLoadingSpin 1s linear infinite;
}

@keyframes contactLoadingSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载文本 */
.contact-loading-text {
  font-size: 14px;
  color: var(--text-muted);
  text-align: center;
}

/* 错误状态容器 */
.contact-error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  padding: 40px;
  text-align: center;
  color: var(--text-muted);
}

/* 错误图标 */
.contact-error-icon {
  margin-bottom: 20px;
  opacity: 0.6;
}

.contact-error-icon svg {
  color: var(--text-error);
  width: 48px;
  height: 48px;
}

/* 错误信息 */
.contact-error-message h3 {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-normal);
}

.contact-error-message p {
  margin: 0 0 20px 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-muted);
  max-width: 300px;
}

/* 重试按钮 */
.contact-error-retry {
  background: var(--interactive-accent);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.contact-error-retry:hover {
  background: var(--interactive-accent-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.contact-error-retry:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 深色主题适配 */
.theme-dark .contact-loading-state {
  background: var(--background-primary);
}

.theme-dark .contact-error-icon svg {
  color: #ff6b6b;
}

/* 响应式设计 */
@media (max-width: 600px) {
  .contact-error-state {
    height: 300px;
    padding: 20px;
  }
  
  .contact-error-icon svg {
    width: 36px;
    height: 36px;
  }
  
  .contact-error-message h3 {
    font-size: 16px;
  }
  
  .contact-error-message p {
    font-size: 13px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .contact-loading-spinner {
    border-top-color: currentColor;
  }
  
  .contact-error-retry {
    border: 2px solid currentColor;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .contact-loading-spinner {
    animation: none;
  }
  
  .contact-loading-state {
    transition: none;
  }
  
  .contact-error-retry {
    transition: none;
  }
  
  .contact-error-retry:hover {
    transform: none;
  }
}

/* 右侧搜索图标样式 (Airbnb风格：添加流畅过渡) */
.contact-obcm-search--right-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  background-color: #8887d9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
  z-index: 10;
}
.contact-obcm-search--right-icon:hover {
  background-color: #8887d9;
  transform: translateY(-50%) scale(1.05);
}

.contact-obcm-search--right-icon:active {
  transform: translateY(-50%) scale(0.95);
}

.contact-obcm-search--right-icon .contact-obcm-search--search-icon {
  color: white;
  stroke: white;
}

/* 确保输入容器有相对定位 */
.contact-obcm-search--input-container {
  position: relative;
}

/* 为搜索输入框添加右侧内边距，避免与图标重叠 */
.contact-obcm-search--search-input {
  padding-right: 52px !important;
}

/* 搜索结果容器消失动画 */
/* 搜索结果容器滚动隐藏动画 */
.contact-obcm-search--results-container.contact-obcm-search--scroll-hiding {
  animation: resultsContainerScrollFadeOut 0.3s cubic-bezier(0.4, 0.0, 1, 1) forwards;
}

@keyframes resultsContainerScrollFadeOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
  }
}

/* 搜索结果容器显示动画 */
.contact-obcm-search--results-container.contact-obcm-search--scroll-showing {
  animation: resultsContainerScrollFadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

@keyframes resultsContainerScrollFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 展开状态 - 当有选中的标签时 */
.contact-obcm-search--input-container.contact-obcm-search--expanded {
  width: 90vw;
  max-width: 1000px;
  min-width: 700px;
}

/* 滚动状态 - 紧凑模式 */
.contact-obcm-search--input-container.contact-obcm-search--scrolled {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.35);
  max-width: 350px;
  min-width: 350px;
  width: 350px;
  border-radius: 24px;
  height: 46px;
  min-height: 46px;
  padding: 8px 12px 8px 16px;
  transform-origin: center center;
  transition: all 0.6s cubic-bezier(0.16, 1, 0.3, 1),
              transform 0.6s cubic-bezier(0.16, 1, 0.3, 1),
              width 0.5s cubic-bezier(0.23, 1, 0.32, 1),
              height 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  box-shadow: 0 1px 2px rgba(0,0,0,0.08), 0 4px 12px rgba(0,0,0,0.05);
  z-index: 1000;
  background: var(--background-primary);
  overflow: hidden;
  cursor: pointer;
  margin-top: 0;
}

/* 滚动状态下的展开状态 */
.contact-obcm-search--input-container.contact-obcm-search--scrolled.contact-obcm-search--expanded {
  max-width: 350px;
  min-width: 350px;
  width: 350px;
}

/* 添加准备状态类，用于控制过渡动画 */
.contact-obcm-search--input-container.contact-obcm-search--ready {
  position: fixed;
  transform-origin: center center;
}

/* 滚动状态的样式 */
.contact-obcm-search--scrolled .contact-obcm-search--input-container {
  width: 350px;
  min-width: 350px;
  height: 46px;
  border-radius: 24px;
  padding: 8px 12px 8px 16px;
  transform: scale(0.35);
}